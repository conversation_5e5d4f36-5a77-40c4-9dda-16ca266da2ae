<?php

use App\Http\Controllers\Api\AccountAffiliateController;
use App\Http\Controllers\Api\AccountController;
use App\Http\Controllers\Api\AddressController;
use App\Http\Controllers\Api\AffiliateController;
use App\Http\Controllers\Api\AffiliateRequestController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\AuthorController;
use App\Http\Controllers\Api\BannerController;
use App\Http\Controllers\Api\ContactUsController;
use App\Http\Controllers\Api\DomainController;
use App\Http\Controllers\Api\EmailSwipeController;
use App\Http\Controllers\Api\ExperimentController;
use App\Http\Controllers\Api\ExperimentGoalController;
use App\Http\Controllers\Api\ExperimentProjectController;
use App\Http\Controllers\Api\ExperimentVariationController;
use App\Http\Controllers\Api\CustomerController;
use App\Http\Controllers\Api\FBAudienceController;
use App\Http\Controllers\Api\LinkController;
use App\Http\Controllers\Api\OfferController;
use App\Http\Controllers\Api\PixelController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\SourceController;
use App\Http\Controllers\Api\TestimonialController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\ScheduledExportController;
use App\Http\Controllers\Api\ServiceCredentialController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::name('api.')->group(function () {
    Route::post('/login', [AuthController::class, 'login'])->name('login');
    Route::post('/v2/login', [AuthController::class, 'loginV2'])->name('v2.login');
    Route::post('/register', [AuthController::class, 'register'])->name('register');
    Route::post('/register/user', [AuthController::class, 'registerUser'])->name('register.user');
    Route::post('/register/affiliate', [AuthController::class, 'registerAffiliate'])->name('register.affiliate');
    Route::post('/register/completed', [AuthController::class, 'registerCompleted'])->name('register.completed');
    Route::post('/verify-email', [AuthController::class, 'verifyUserEmail'])->name('verify-email');
    Route::post('/v2/verify-email', [AuthController::class, 'verifyUserEmailV2'])->name('v2.verify-email');
    Route::post('/password-recovery', [AuthController::class, 'passwordRecovery'])->name('password.recovery');
    Route::post('/password-reset', [AuthController::class, 'passwordReset'])->name('password.reset');

    Route::post('/contact-us', [ContactUsController::class, 'send'])->name('contact-us.send');

    Route::name('scheduled-export.unsubscribe')
        ->get('/scheduled-exports/{hash}/unsubscribe', [ScheduledExportController::class, 'unsubscribe']);

    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
        Route::get('/users/me', [UserController::class, 'me']);
        Route::post('/users/new-password', [UserController::class, 'newPassword'])->name('user.new-password');
    });

    Route::middleware(['auth:sanctum'])->group(function () {
        Route::apiResource('users', UserController::class);

        // Experiments
        Route::prefix('experiments')->name('experiments.')->group(function () {
            Route::get('/', [ExperimentController::class, 'index'])->name('index');
            Route::get('/{id}', [ExperimentController::class, 'show'])->name('show');

            // Variations on Experiments
            Route::name('variations.list')
                ->get('/{id}/variations', [ExperimentVariationController::class, 'index']);
            Route::name('variations.show')
                ->get('/variations/{id}', [ExperimentVariationController::class, 'show']);

            // Goals on Experiments
            Route::name('goals.list')
                ->get('/{id}/goals', [ExperimentGoalController::class, 'index']);
            Route::name('goals.show')
                ->get('/{id}/goals/{goal}', [ExperimentGoalController::class, 'show']);
        });

        // Experiments Variations
        Route::name('experiment-variations.list')
            ->get('/experiment-variations', [ExperimentVariationController::class, 'index']);
        Route::name('experiment-variations.show')
            ->get('/experiment-variations/{id}', [ExperimentVariationController::class, 'show']);
        Route::name('experiment-variations.decision')
            ->post('/experiment-variations/{id}/decision', [ExperimentVariationController::class, 'saveDecision']);

        // Experiments Goals
        Route::name('experiment-goals.list')
            ->get('/experiment-goals', [ExperimentGoalController::class, 'index']);
        Route::name('experiment-goals.show')
            ->get('/experiment-goals/{id}', [ExperimentGoalController::class, 'show']);
        Route::name('experiment-goals.variation-event')
            ->post('/experiment-goals/{id}/variation/{variation}/event', [
                ExperimentGoalController::class,
                'saveEvent',
            ]);
        Route::name('experiment-goals.event')
            ->post('/experiment-goals/{id}/event', [ExperimentGoalController::class, 'saveEvent']);

        // Experiment Projects
        Route::name('experiment-projects.list')
            ->get('/experiment-projects', [ExperimentProjectController::class, 'index']);
        Route::name('experiment-projects.show')
            ->get('/experiment-projects/{id}', [ExperimentProjectController::class, 'show']);

        Route::middleware(['check-ip'])->group(function () {
            Route::name('pixels.index')
                ->get('/pixels/', [PixelController::class, 'index']);
        });

        Route::prefix('v2/pixels')->group(function () {
            Route::name('v2.pixels.index')->get('/', [PixelController::class, 'indexV2'])
                ->middleware('abilities:view all pixels');
            Route::name('v2.pixels.store')->post('/', [PixelController::class, 'store'])
                ->middleware('abilities:create pixels');
            Route::name('v2.pixels.update')->put('/{pixel}', [PixelController::class, 'update'])
                ->can('update', 'pixel');
            Route::name('v2.pixels.delete')->delete('/{pixel}', [PixelController::class, 'delete'])
                ->can('delete', 'pixel');
        });

        Route::prefix('customers')->name('customers.')->group(function () {
            Route::name('filter-email')
                ->get('/filter-email', [CustomerController::class, 'filterEmail'])
                ->middleware('abilities:view customers');
            Route::name('social-proof')
                ->get('/customer-social-proof', [CustomerController::class, 'socialProof']);
            // FB audience
            Route::name('export.fb-audience')
                ->get('/customers/export/csv', [FBAudienceController::class, 'exportCustomer'])
                ->middleware('abilities:export fb audience');

            Route::name('index')
                ->get('/', [CustomerController::class, 'index'])
                ->middleware('abilities:view all customers');
            Route::name('show')
                ->get('/{customer}', [CustomerController::class, 'show'])
                ->middleware('abilities:view customers');
        });

        Route::prefix('affiliate-requests')->name('affiliate-requests.')->group(function () {
            Route::name('products')->get('/products', [AffiliateRequestController::class, 'products']);
            Route::name('index')
                ->get('/', [AffiliateRequestController::class, 'index'])
                ->middleware('abilities:view all affiliate requests');
            Route::name('store')
                ->post('/', [AffiliateRequestController::class, 'store'])
                ->middleware('abilities:create affiliate requests');
            Route::name('update')
                ->put('/{id}', [AffiliateRequestController::class, 'update'])
                ->can('update', 'affiliateRequest');
            Route::name('show')
                ->get('/{affiliateRequest}', [AffiliateRequestController::class, 'show'])
                ->can('view', 'affiliateRequest');
            Route::name('delete')
                ->delete('/{affiliateRequest}', [AffiliateRequestController::class, 'delete'])
                ->can('delete', 'affiliateRequest');
        });

        Route::name('sources.index')
            ->get('/sources', [SourceController::class, 'index']);

        Route::name('affiliates.index')
            ->get('/affiliates', [AffiliateController::class, 'index'])
            ->middleware('abilities:view all affiliates');
        Route::name('affiliates.meta.update')
            ->put('/affiliates/{id}/meta', [AffiliateController::class, 'updateMeta'])
            ->middleware('abilities:update affiliates');

        Route::name('addresses.index')
            ->get('/addresses', [AddressController::class, 'index'])
            ->middleware('abilities:view all addresses');

        // Account affiliate
        Route::prefix('accounts-affiliates')->name('account-affiliate.')->group(function () {
            Route::name('index')
                ->get('/', [AccountAffiliateController::class, 'index']);
        });

        // Scheduled export
        Route::prefix('scheduled-exports')->name('scheduled-export.')->group(function () {
            Route::name('products')->get('/products', [ScheduledExportController::class, 'products']);
            Route::name('index')->get('/', [ScheduledExportController::class, 'index'])
                ->middleware('abilities:view all scheduled exports');
            Route::name('store')->post('/', [ScheduledExportController::class, 'store'])
                ->middleware('abilities:create scheduled exports');
            Route::name('update')->put('/{scheduledExport}', [ScheduledExportController::class, 'update'])
                ->can('update', 'scheduledExport');
            Route::name('delete')->delete('/{scheduledExport}', [ScheduledExportController::class, 'delete'])
                ->can('delete', 'scheduledExport');
        });

        // Link
        Route::prefix('links')->name('link.')->group(function () {
            Route::name('type.list')->get('/types', [LinkController::class, 'types']);
            Route::name('index')->get('/', [LinkController::class, 'index'])
                ->middleware('abilities:view all links');
            Route::name('store')->post('/', [LinkController::class, 'store'])
                ->middleware('abilities:create links');
            Route::name('update')->put('/{id}', [LinkController::class, 'update'])
                ->middleware('abilities:update links');
            Route::name('delete')->delete('/{id}', [LinkController::class, 'delete'])
                ->middleware('abilities:delete links');
        });

        // Domain
        Route::prefix('domains')->name('domain.')->group(function () {
            Route::name('index')->get('/', [DomainController::class, 'index'])
                ->middleware('abilities:view all domains');
        });
        Route::prefix('products')->name('products.')->group(function () {
            Route::name('index')->get('/', [ProductController::class, 'index'])
                ->middleware('abilities:view all products');
            Route::name('show')->get('/{id}', [ProductController::class, 'show'])
                ->middleware('abilities:view products');
        });

        Route::prefix('testimonials')->name('testimonials.')->group(function () {
            Route::name('index')->get('/', [TestimonialController::class, 'index'])
                ->middleware('abilities:view all testimonials');
        });

        Route::prefix('banners')->name('banners.')->group(function () {
            Route::name('index')->get('/', [BannerController::class, 'index'])
                ->middleware('abilities:view all banners');
            Route::name('zip')->get('/zip', [BannerController::class, 'zip'])
                ->middleware('abilities:view banners');
        });

        Route::prefix('email-swipes')->name('email-swipe.')->group(function () {
            Route::name('index')->get('/', [EmailSwipeController::class, 'index'])
                ->middleware('abilities:view all email swipes');
            Route::name('zip')->get('/zip', [EmailSwipeController::class, 'zip'])
                ->middleware('abilities:export email swipes');
            Route::name('download')->get('/{id}/download', [EmailSwipeController::class, 'download'])
                ->middleware('abilities:export email swipes');
        });

        Route::prefix('authors')->name('authors.')->group(function () {
            Route::name('index')->get('/', [AuthorController::class, 'index'])
                ->middleware('abilities:view all authors');
            Route::name('zip')->get('/zip', [AuthorController::class, 'zip'])
                ->middleware('abilities:view authors');
        });

        // Offer
        Route::prefix('offers')->name('offer.')->group(function () {
            Route::name('index')->get('/', [OfferController::class, 'index'])
                ->middleware('abilities:view all offers');
            Route::name('show')->get('/{id}', [OfferController::class, 'show'])
                ->middleware('abilities:view offers');
            Route::name('store')->post('/', [OfferController::class, 'store'])
                ->middleware('abilities:create offers');
            Route::name('update')->put('/{id}', [OfferController::class, 'update'])
                ->middleware('abilities:update offers');
            Route::name('delete')->delete('/{id}', [OfferController::class, 'delete'])
                ->middleware('abilities:delete offers');
        });

        // User
        Route::prefix('users')->name('user.')->group(function () {
            Route::name('show')->get('/{id}', [UserController::class, 'show'])
                ->middleware('abilities:view users');
            Route::name('update')->put('/{id}', [UserController::class, 'update']);
            Route::name('me')->get('/me', [UserController::class, 'me']);
            Route::name('me.remove-media')->delete('/me/remove-media', [UserController::class, 'removeMedia']);
        });

        // Account
        Route::prefix('accounts')->name('account.')->group(function () {
            Route::name('show')->get('/{id}', [AccountController::class, 'show'])
                ->middleware('abilities:view accounts');
            Route::name('store')->post('/', [AccountController::class, 'store'])
                ->middleware('abilities:create accounts');
            Route::name('update')->put('/{id}', [AccountController::class, 'update'])
                ->middleware('abilities:update accounts');
        });

        // Service Credentials
        Route::prefix('service-credentials')->name('service-credentials.')->group(function () {
            Route::name('show')->get('/{id}', [ServiceCredentialController::class, 'show'])
                ->middleware('abilities:view service credentials');
        });
    });
    Route::prefix('accounts')->name('account.')->group(function () {
        Route::name('index')->get('/', [AccountController::class, 'index']);
    });
});
