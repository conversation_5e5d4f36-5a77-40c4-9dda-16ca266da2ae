<?php

use App\Models\Role;

return [

    'models' => [

        /*
         * When using the "HasPermissions" trait from this package, we need to know which
         * Eloquent model should be used to retrieve your permissions. Of course, it
         * is often just the "Permission" model but you may use whatever you like.
         *
         * The model you want to use as a Permission model needs to implement the
         * `<PERSON>tie\Permission\Contracts\Permission` contract.
         */

        'permission' => Spatie\Permission\Models\Permission::class,

        /*
         * When using the "HasRoles" trait from this package, we need to know which
         * Eloquent model should be used to retrieve your roles. Of course, it
         * is often just the "Role" model but you may use whatever you like.
         *
         * The model you want to use as a Role model needs to implement the
         * `<PERSON>tie\Permission\Contracts\Role` contract.
         */

        'role' => Role::class,

    ],

    'table_names' => [

        /*
         * When using the "HasRoles" trait from this package, we need to know which
         * table should be used to retrieve your roles. We have chosen a basic
         * default value but you may easily change it to any table you like.
         */

        'roles' => 'roles',

        /*
         * When using the "HasPermissions" trait from this package, we need to know which
         * table should be used to retrieve your permissions. We have chosen a basic
         * default value but you may easily change it to any table you like.
         */

        'permissions' => 'permissions',

        /*
         * When using the "HasPermissions" trait from this package, we need to know which
         * table should be used to retrieve your models permissions. We have chosen a
         * basic default value but you may easily change it to any table you like.
         */

        'model_has_permissions' => 'model_has_permissions',

        /*
         * When using the "HasRoles" trait from this package, we need to know which
         * table should be used to retrieve your models roles. We have chosen a
         * basic default value but you may easily change it to any table you like.
         */

        'model_has_roles' => 'model_has_roles',

        /*
         * When using the "HasRoles" trait from this package, we need to know which
         * table should be used to retrieve your roles permissions. We have chosen a
         * basic default value but you may easily change it to any table you like.
         */

        'role_has_permissions' => 'role_has_permissions',
    ],

    'column_names' => [
        /*
         * Change this if you want to name the related pivots other than defaults
         */
        'role_pivot_key'       => null, //default 'role_id',
        'permission_pivot_key' => null, //default 'permission_id',

        /*
         * Change this if you want to name the related model primary key other than
         * `model_id`.
         *
         * For example, this would be nice if your primary keys are all UUIDs. In
         * that case, name this `model_uuid`.
         */

        'model_morph_key' => 'model_id',

        /*
         * Change this if you want to use the teams feature and your related model's
         * foreign key is other than `team_id`.
         */

        'team_foreign_key' => 'team_id',
    ],

    /*
     * When set to true, the method for checking permissions will be registered on the gate.
     * Set this to false, if you want to implement custom logic for checking permissions.
     */

    'register_permission_check_method' => true,

    /*
     * When set to true the package implements teams using the 'team_foreign_key'. If you want
     * the migrations to register the 'team_foreign_key', you must set this to true
     * before doing the migration. If you already did the migration then you must make a new
     * migration to also add 'team_foreign_key' to 'roles', 'model_has_roles', and
     * 'model_has_permissions'(view the latest version of package's migration file)
     */

    'teams' => false,

    /*
     * When set to true, the required permission names are added to the exception
     * message. This could be considered an information leak in some contexts, so
     * the default setting is false here for optimum safety.
     */

    'display_permission_in_exception' => false,

    /*
     * When set to true, the required role names are added to the exception
     * message. This could be considered an information leak in some contexts, so
     * the default setting is false here for optimum safety.
     */

    'display_role_in_exception' => false,

    /*
     * By default wildcard permission lookups are disabled.
     */

    'enable_wildcard_permission' => false,

    'cache' => [

        /*
         * By default all permissions are cached for 24 hours to speed up performance.
         * When permissions or roles are updated the cache is flushed automatically.
         */

        'expiration_time' => DateInterval::createFromDateString('24 hours'),

        /*
         * The cache key used to store all permissions.
         */

        'key' => 'spatie.permission.cache',

        /*
         * You may optionally indicate a specific cache driver to use for permission and
         * role caching using any of the `store` drivers listed in the cache.php config
         * file. Using 'default' here means to use the `default` set in cache.php.
         */

        'store' => 'default',
    ],

    /**
     * The permissions that are available in the system and their default roles.
     */
    'list'  => [
        // Admin only permissions.
        'assign roles'                     => [Role::CUSTOMER_SERVICE_MANAGER],
        'assign permissions'               => [Role::CUSTOMER_SERVICE_MANAGER],
        'view all audits'                  => [Role::ADMIN],
        'view audits'                      => [Role::ADMIN, Role::CUSTOMER_SERVICE_MANAGER],
        'view all failed jobs'             => null,
        'view failed jobs'                 => null,
        'delete failed jobs'               => null,
        'retry failed jobs'                => null,

        // Accounts
        'view all accounts'                => [Role::ADMIN, Role::CUSTOMER_SERVICE_MANAGER],
        'view accounts'                    => [Role::ADMIN, Role::CUSTOMER_SERVICE_MANAGER],
        'create accounts'                  => null,
        'update accounts'                  => null,

        // Addresses
        'view all addresses'               => [Role::CUSTOMER_SERVICE, Role::CUSTOMER_SERVICE_MANAGER],
        'view addresses'                   => [Role::CUSTOMER_SERVICE, Role::CUSTOMER_SERVICE_MANAGER],
        'create addresses'                 => [Role::CUSTOMER_SERVICE_MANAGER],
        'update addresses'                 => [Role::CUSTOMER_SERVICE_MANAGER],
        'delete addresses'                 => null,
        'export addresses'                 => null,

        // Affiliates
        'view all affiliates'              => null,
        'view affiliates'                  => null,
        'create affiliates'                => null,
        'update affiliates'                => null,
        'delete affiliates'                => null,
        'export affiliates'                => null,

        // Affiliate requests
        'view all affiliate requests'      => Role::AFFILIATE,
        'view affiliate requests'          => Role::AFFILIATE,
        'create affiliate requests'        => Role::AFFILIATE,
        'update affiliate requests'        => Role::AFFILIATE,
        'delete affiliate requests'        => Role::AFFILIATE,
        'manage affiliate requests'        => null,

        // Allowed Ip
        'view all allowed ip'              => null,
        'view allowed ip'                  => null,
        'create allowed ip'                => null,
        'update allowed ip'                => null,
        'delete allowed ip'                => null,

        // Authors
        'view all authors'                 => Role::AFFILIATE,
        'view authors'                     => Role::AFFILIATE,
        'create authors'                   => null,
        'update authors'                   => null,
        'delete authors'                   => null,
        'approve authors'                  => null,

        // Banner
        'view all banners'                 => Role::AFFILIATE,
        'view banners'                     => Role::AFFILIATE,
        'create banners'                   => Role::ADMIN,
        'update banners'                   => null,
        'delete banners'                   => null,

        // BuyButton
        'view all buy buttons'             => null,
        'view buy buttons'                 => null,
        'create buy buttons'               => null,
        'update buy buttons'               => null,
        'delete buy buttons'               => null,
        'approve buy buttons'              => null,

        // Brands
        'view all brands'                  => [Role::CUSTOMER_SERVICE, Role::ADMIN, Role::AFFILIATE, Role::CUSTOMER_SERVICE_MANAGER],
        'view brands'                      => [Role::CUSTOMER_SERVICE, Role::ADMIN, Role::AFFILIATE, Role::CUSTOMER_SERVICE_MANAGER],
        'create brands'                    => null,
        'update brands'                    => null,
        'delete brands'                    => null,
        'export brands'                    => null,

        // Customers
        'view all customers'               => [Role::CUSTOMER_SERVICE, Role::CUSTOMER_SERVICE_MANAGER],
        'view customers'                   => [Role::CUSTOMER_SERVICE, Role::CUSTOMER_SERVICE_MANAGER],
        'create customers'                 => null,
        'update customers'                 => [Role::CUSTOMER_SERVICE_MANAGER],
        'delete customers'                 => null,
        'export customers'                 => null,
        'view customers social proof'      => null,

        // Dev permissions
        'view dev flagged'                 => null,
        'view repository queries'          => null,

        // Domains
        'view all domains'                 => Role::AFFILIATE,
        'view domains'                     => Role::AFFILIATE,
        'create domains'                   => null,
        'update domains'                   => null,
        'delete domains'                   => null,
        'export domains'                   => null,

        // DomainAction
        'view all domain actions'          => null,
        'view domain actions'              => null,
        'create domain actions'            => null,
        'update domain actions'            => null,
        'delete domain actions'            => null,

        // Domain Logs
        'view all domain logs'             => null,
        'view domain logs'                 => null,

        // Email Domain Marketings
        'view all email domain marketings' => null,
        'view email domain marketings'     => null,
        'create email domain marketings'   => null,
        'update email domain marketings'   => null,
        'delete email domain marketings'   => null,

        // Email Swipes
        'view all email swipes'            => Role::AFFILIATE,
        'view email swipes'                => Role::AFFILIATE,
        'create email swipes'              => null,
        'update email swipes'              => null,
        'delete email swipes'              => null,
        'export email swipes'              => Role::AFFILIATE,

        // Experiments
        'view all experiments'             => null,
        'view experiments'                 => null,
        'create experiments'               => null,
        'update experiments'               => null,
        'delete experiments'               => null,
        'approve experiments'              => null, // Approves experiments to go live.

        // Export FB Audience
        'export fb audience'               => Role::AFFILIATE,

        // Links
        'view all links'                   => Role::AFFILIATE,
        'view links'                       => Role::AFFILIATE,
        'create links'                     => Role::AFFILIATE,
        'update links'                     => Role::AFFILIATE,
        'delete links'                     => Role::AFFILIATE,

        // Meta
        'view meta'                        => [Role::ADMIN, Role::CUSTOMER_SERVICE_MANAGER],
        'create meta'                      => null,
        'update meta'                      => null,
        'delete meta'                      => null,

        // Offers
        'view all offers'                  => [Role::CUSTOMER_SERVICE, Role::ADMIN],
        'view offers'                      => [Role::CUSTOMER_SERVICE, Role::ADMIN],
        'create offers'                    => null,
        'update offers'                    => null,
        'delete offers'                    => null,
        'export offers'                    => null,

        // Orders
        'view all orders'                  => [Role::CUSTOMER_SERVICE, Role::COMMITTED_COACHES_SUPPORT, Role::CUSTOMER_SERVICE_MANAGER],
        'view orders'                      => [Role::CUSTOMER_SERVICE, Role::COMMITTED_COACHES_SUPPORT, Role::CUSTOMER_SERVICE_MANAGER],
        'create orders'                    => null,
        'update orders'                    => [Role::CUSTOMER_SERVICE_MANAGER],
        'delete orders'                    => null,
        'export orders'                    => null,

        // Refunds
        'view all refunds'                 => [Role::ADMIN, Role::COMMITTED_COACHES_SUPPORT, Role::CUSTOMER_SERVICE_MANAGER],
        'view refunds'                     => [Role::ADMIN, Role::COMMITTED_COACHES_SUPPORT, Role::CUSTOMER_SERVICE_MANAGER],
        'create refunds'                   => [Role::ADMIN],
        'update refunds'                   => [Role::ADMIN],
        'delete refunds'                   => [Role::ADMIN],
        'export refunds'                   => [Role::ADMIN],

        // Chargeback fee
        'view all chargeback fee'          => [Role::ADMIN, Role::CUSTOMER_SERVICE_MANAGER],
        'view chargeback fee'              => [Role::ADMIN, Role::CUSTOMER_SERVICE_MANAGER],
        'create chargeback fee'            => [Role::ADMIN],
        'update chargeback fee'            => [Role::ADMIN],
        'delete chargeback fee'            => [Role::ADMIN],
        'export chargeback fee'            => [Role::ADMIN],

        // Domain registration
        'view all domain registrations'    => [Role::ADMIN],
        'view domain registrations'        => [Role::ADMIN],
        'create domain registrations'      => [Role::ADMIN],
        'update domain registrations'      => [Role::ADMIN],
        'delete domain registrations'      => [Role::ADMIN],
        'export domain registrations'      => [Role::ADMIN],

        // Domain registration
        'view all digistore offer reports' => [Role::ADMIN],
        'view digistore offer reports'     => [Role::ADMIN],
        'create digistore offer reports'   => [Role::ADMIN],
        'update digistore offer reports'   => [Role::ADMIN],
        'delete digistore offer reports'   => [Role::ADMIN],
        'export digistore offer reports'   => [Role::ADMIN],

        // PageTemplate
        'view all page templates'          => null,
        'view page templates'              => null,
        'create page templates'            => null,
        'update page templates'            => null,
        'delete page templates'            => null,
        'approve page templates'           => null,

        // Payloads
        'view all payloads'                => null,
        'view payloads'                    => null,
        'create payloads'                  => null,
        'update payloads'                  => null,
        'delete payloads'                  => null,
        'replay payloads'                  => null,
        'export payloads'                  => null,

        // Pixels
        'view all pixels'                  => [Role::AFFILIATE],
        'view pixels'                      => [Role::AFFILIATE],
        'create pixels'                    => null,
        'update pixels'                    => null,
        'delete pixels'                    => null,
        'approve pixels'                   => null,

        // Products
        'view all products'                => [
            Role::CUSTOMER_SERVICE,
            Role::ADMIN,
            Role::AFFILIATE,
            Role::COMMITTED_COACHES_SUPPORT,
            Role::CUSTOMER_SERVICE_MANAGER,
        ],
        'view products'                    => [
            Role::CUSTOMER_SERVICE,
            Role::ADMIN,
            Role::AFFILIATE,
            Role::COMMITTED_COACHES_SUPPORT,
            Role::CUSTOMER_SERVICE_MANAGER,
        ],
        'create products'                  => null,
        'update products'                  => null,
        'delete products'                  => null,
        'export products'                  => null,

        // ProductCost
        'view all product cost'            => Role::ADMIN,
        'view product cost'                => Role::ADMIN,
        'create product cost'              => null,
        'update product cost'              => null,
        'delete product cost'              => null,

        // Reports (auto-generated so can only be viewed)
        'view reports'                     => null,

        // Shipments
        'view all shipments'               => [Role::CUSTOMER_SERVICE, Role::COMMITTED_COACHES_SUPPORT, Role::CUSTOMER_SERVICE_MANAGER],
        'view shipments'                   => [Role::CUSTOMER_SERVICE, Role::COMMITTED_COACHES_SUPPORT, Role::CUSTOMER_SERVICE_MANAGER],
        'create shipments'                 => null,
        'update shipments'                 => null,
        'delete shipments'                 => null,
        'export shipments'                 => null,

        // Tracking
        'view all tracking'                => Role::ADMIN,
        'view tracking'                    => Role::ADMIN,
        'create tracking'                  => null,
        'update tracking'                  => null,
        'delete tracking'                  => null,

        // Scheduled Export
        'view all scheduled exports'       => Role::AFFILIATE,
        'view scheduled exports'           => Role::AFFILIATE,
        'create scheduled exports'         => Role::AFFILIATE,
        'update scheduled exports'         => Role::AFFILIATE,
        'delete scheduled exports'         => Role::AFFILIATE,

        // Testimonials
        'view all testimonials'            => Role::AFFILIATE,
        'view testimonials'                => Role::AFFILIATE,
        'create testimonials'              => Role::ADMIN,
        'update testimonials'              => null,
        'delete testimonials'              => null,

        // Transactions
        'view all transactions'            => [Role::CUSTOMER_SERVICE, Role::CUSTOMER_SERVICE_MANAGER],
        'view transactions'                => [Role::CUSTOMER_SERVICE, Role::CUSTOMER_SERVICE_MANAGER],

        // Users
        'view all users'                   => [Role::CUSTOMER_SERVICE_MANAGER],
        'view users'                       => [Role::CUSTOMER_SERVICE_MANAGER],
        'create users'                     => [Role::CUSTOMER_SERVICE_MANAGER],
        'update users'                     => [Role::CUSTOMER_SERVICE_MANAGER],
        'delete users'                     => null,

        // Marketing lists
        'view all marketing lists'         => null,
        'view marketing lists'             => null,
        'create marketing lists'           => null,
        'update marketing lists'           => null,
        'delete marketing lists'           => null,

        // Marketing list limit
        'view all marketing list limit'    => null,
        'view marketing list limit'        => null,
        'create marketing list limit'      => null,
        'update marketing list limit'      => null,
        'delete marketing list limit'      => null,

        // Tracking category lists
        'view all tracking categories'     => Role::ADMIN,
        'view tracking categories'         => Role::ADMIN,
        'create tracking categories'       => Role::ADMIN,
        'update tracking categories'       => Role::ADMIN,
        'delete tracking categories'       => Role::ADMIN,

        // PII visibility permission
        'view customer pii'                => null,

        // ShipoffersStore
        'view all shipoffers stores'       => Role::ADMIN,
        'view shipoffers stores'           => Role::ADMIN,
        'create shipoffers stores'         => null,
        'update shipoffers stores'         => null,
        'delete shipoffers stores'         => null,

        // Metrics
        'view order metrics'               => Role::ADMIN,

        'view all service credentials'   => null,
        'view service credentials'   => null,
        'create service credentials' => null,
        'update service credentials' => null,
        'delete service credentials' => null,
    ],
];
