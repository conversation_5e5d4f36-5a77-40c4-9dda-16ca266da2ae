<?php

use App\Models\Experiment;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('experiment_variations', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Experiment::class)->constrained();
            $table->string('service_ref')->nullable(); // Reference if this is an external service.
            $table->string('key', 64)->nullable()->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->boolean('is_baseline');
            $table->unsignedDecimal('traffic_distribution');
            $table->string('type', 32);
            $table->string('status', 32);
            $table->string('url')->nullable(); // URL to redirect to. May contains brackets for dynamic values.
            $table->string('redirect_pattern')->nullable(); // Regular expression for matching the original URL.
            $table->text('css')->nullable();
            $table->text('js')->nullable();
            $table->json('meta')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('experiment_variations');
    }
};
