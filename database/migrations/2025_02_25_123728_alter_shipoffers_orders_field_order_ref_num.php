<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shipoffers_orders', function (Blueprint $table) {
            $table->string('order_ref_num')->nullable()->after('shipoffers_order_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shipoffers_orders', function (Blueprint $table) {
            $table->dropColumn(['order_ref_num']);
        });
    }
};
