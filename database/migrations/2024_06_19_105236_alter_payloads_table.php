<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payloads', function (Blueprint $table) {
            if (!Schema::hasColumn('payloads', 'retry_at')) {
                $table->timestamp('retry_at')->nullable()->index()->after('error');
            }

            if (!Schema::hasColumn('payloads', 'retry_attempts')) {
                $table->integer('retry_attempts')->default(0)->after('error');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payloads', function (Blueprint $table) {
            if (Schema::hasColumn('payloads', 'retry_at')) {
                $table->dropColumn('retry_at');
            }

            if (Schema::hasColumn('payloads', 'retry_attempts')) {
                $table->dropColumn('retry_attempts');
            }
        });
    }
};
