<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('offer_reports', function (Blueprint $table) {
            $table->integer('chargeback_amount')->default(0)->after('chargeback_fees');
            $table->unsignedInteger('chargeback_count')->default(0)->after('chargeback_amount');
            $table->integer('refunded_count')->default(0)->after('refunded_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('offer_reports', function (Blueprint $table) {
            $table->dropColumn('chargeback_amount');
            $table->dropColumn('chargeback_count');
            $table->dropColumn('refunded_count');
        });
    }
};
