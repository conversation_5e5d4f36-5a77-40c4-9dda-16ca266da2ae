<?php

use App\Models\Experiment;
use App\Models\ExperimentVariation;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('experiment_decisions', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Experiment::class)->constrained();
            $table->foreignIdFor(ExperimentVariation::class)->constrained();
            $table->string('session_id')->nullable();
            $table->json('meta')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('experiment_decisions');
    }
};
