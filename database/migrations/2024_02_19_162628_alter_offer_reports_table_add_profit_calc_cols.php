<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('offer_reports', function (Blueprint $table) {
            $table->integer('shipping_cost')->default(0)->after('shipping_amount');
            $table->integer('refunded_amount')->default(0)->after('shipping_cost');
            $table->integer('tax_amount')->default(0)->after('refunded_amount');
            $table->integer('discount_amount')->default(0)->after('tax_amount');
            $table->integer('chargeback_fees')->default(0)->after('merchant_fees');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('offer_reports', function (Blueprint $table) {
            $table->dropColumn('shipping_cost');
            $table->dropColumn('refunded_amount');
            $table->dropColumn('tax_amount');
            $table->dropColumn('discount_amount');
            $table->dropColumn('chargeback_fees');
        });
    }
};
