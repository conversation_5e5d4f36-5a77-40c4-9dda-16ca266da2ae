<?php

namespace Database\Factories;

use App\Enums\AddressType;
use App\Models\Affiliate;
use App\Models\Customer;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Affiliate>
 */
class AddressFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'customer_id' => Customer::factory(),
            'first_name'  => $this->faker->firstName,
            'last_name'   => $this->faker->lastName,
            'company'     => $this->faker->company,
            'address'     => $this->faker->streetAddress,
            'city'        => $this->faker->city,
            'state'       => 'NV',
            'country'     => 'US',
            'postal_code' => $this->faker->postcode,
            'type'        => $this->faker->randomElement(AddressType::cases()),
        ];
    }
}
