<?php

namespace Database\Factories;

use App\Enums\ServiceCredentialType;
use App\Models\ServiceCredential;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ServiceCredential>
 */
class ServiceCredentialFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'service' => $this->faker->randomElement([
                ServiceCredentialType::GOOGLE->value,
                ServiceCredentialType::USPS->value,
                ServiceCredentialType::AMAZON_SELLER_CENTRAL->value,
                ServiceCredentialType::VOLUUM->value,
                ServiceCredentialType::AWEBER->value,
                'custom-service-' . $this->faker->word(),
                'third-party-api',
            ]),
            'credentials' => [
                'api_key' => $this->faker->uuid(),
                'secret' => $this->faker->sha256(),
            ],
            'properties' => [
                'test_property' => $this->faker->word(),
                'another_property' => $this->faker->sentence(),
            ],
            'refresh_token' => $this->faker->sha256(),
            'expires_at' => $this->faker->optional()->dateTimeBetween('now', '+1 year'),
            'publicly_available' => $this->faker->boolean(),
        ];
    }

    /**
     * Indicate that the service credential is publicly available.
     */
    public function publiclyAvailable(): static
    {
        return $this->state(fn (array $attributes) => [
            'publicly_available' => true,
        ]);
    }

    /**
     * Indicate that the service credential is not publicly available.
     */
    public function private(): static
    {
        return $this->state(fn (array $attributes) => [
            'publicly_available' => false,
        ]);
    }
}
