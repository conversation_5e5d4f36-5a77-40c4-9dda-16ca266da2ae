<?php

namespace Database\Factories;

use App\Enums\TrackingType;
use App\Models\Affiliate;
use App\Models\ExperimentProject;
use App\Models\Offer;
use Illuminate\Database\Eloquent\Factories\Factory;
use Str;

/**
 * @extends Factory<ExperimentProject>
 */
class TrackingFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'affiliate_id' => Affiliate::factory(),
            'order_id'     => Offer::factory(),
            'type'         => $this->faker->randomElement(array_keys(TrackingType::keyValueCases())),
            'key'          => $this->faker->word(),
            'value'        => Str::random(10),
            'tracked_at'   => now(),
        ];
    }
}
