<?php

namespace Database\Factories;

use App\Enums\MarketingListSyncStatus;
use App\Enums\MarketingPlatform;
use App\Models\MarketingList;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<MarketingList>
 */
class MarketingListFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'platform'        => MarketingPlatform::MAROPOST->value,
            'list_id'         => '1',
            'marketable_type' => 'products',
            'marketable_id'   => 1,
            'sync_status'     => MarketingListSyncStatus::AUTOMATIC->value,
            'options'         => ['exclude_abandoned' => false],
        ];
    }
}
