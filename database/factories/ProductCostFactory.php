<?php

namespace Database\Factories;

use App\Enums\ProductCostType;
use App\Models\ProductCost;
use Cknow\Money\Money;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<ProductCost>
 */
class ProductCostFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'product_id' => $this->faker->uuid,
            'type'       => ProductCostType::COG->value,
            'amount'     => Money::parse(0, config('money.defaultCurrency')),
            'currency'   => config('money.defaultCurrency')
        ];
    }
}
