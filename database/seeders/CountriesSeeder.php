<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class CountriesSeeder extends Seeder
{

    /**
     * Run the database seeds.
     *
     * @return  void
     */
    public function run()
    {
        // Empty the countries table
        \DB::table(\Config::get('countries.table_name'))->delete();

        // Get all the countries
        $countries = countries();

        foreach ($countries as $countryCode => $countryItem) {
            // XK is Kosovo, which has incomplete ISO compliant information
            if ($countryCode == 'xk') {
                continue;
            }

            $country = country($countryCode);
            $countryIsoNumeric = $country->getIsoNumeric();
            $countryCurrency = $country->getCurrency();

            \DB::table(\Config::get('countries.table_name'))->insert([
                'id'                => intval($countryIsoNumeric),
                'capital'           => $country->getCapital(),
                'citizenship'       => $country->getDemonym(),
                'country_code'      => $countryIsoNumeric,
                'currency'          => $countryCurrency['iso_4217_name'] ?? null,
                'currency_code'     => $countryCurrency['iso_4217_code'] ?? null,
                'currency_sub_unit' => $countryCurrency['iso_4217_sub_unit'] ?? null,
                'currency_decimals' => $countryCurrency['iso_4217_minor_unit'] ?? null,
                'full_name'         => $country->getOfficialName(),
                'iso_3166_2'        => $country->getIsoAlpha2(),
                'iso_3166_3'        => $country->getIsoAlpha3(),
                'name'              => $country->getName(),
                'region_code'       => $country->getRegionCode(),
                'sub_region_code'   => $country->getSubRegionCode(),
                'eea'               => (bool)$country->isEuMember(),
                'calling_code'      => $country->getCallingCode(),
                'currency_symbol'   => $countryCurrency['iso_4217_symbol'] ?? null,
                'flag'              => $country->getIsoAlpha2() . '.png',
            ]);
        }
    }
}
