<?php

namespace Database\Seeders;

use App\Enums\MarketingPlatform;
use App\Models\MarketingList;
use App\Models\MarketingListLimit;
use Illuminate\Database\Seeder;

class MarketingListLimitSeeder extends Seeder
{
    /**
     * Domain groups configuration for GetResponse marketing lists
     */
    private array $domainGroups = [
        'group1' => [
            'domains' => ['gmail.com'],
            'limit'   => 50,
        ],
        'group2' => [
            'domains' => ['yahoo.com', 'aol.com', 'verizon.com', 'sbcglobal.net', 'comcast.com'],
            'limit'   => 50,
        ],
        'group3' => [
            'domains' => ['hotmail.com', 'outlook.com', 'live.com', 'msn.com'],
            'limit'   => 25,
        ],
        'group4' => [
            'domains' => ['icloud.com', 'mac.com', 'me.com'],
            'limit'   => 25,
        ],
    ];

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all GetResponse marketing lists
        $getResponseLists = MarketingList::where('platform', MarketingPlatform::GET_RESPONSE->value)->get();

        if ($getResponseLists->isEmpty()) {
            $this->command->info('No GetResponse marketing lists found. Skipping seeder.');
            return;
        }

        $this->command->info('Found ' . $getResponseLists->count() . ' GetResponse marketing lists.');

        foreach ($getResponseLists as $marketingList) {
            $this->command->info("Processing marketing list: {$marketingList->list_id}");

            // Check if limits already exist for this marketing list
            $existingLimits = MarketingListLimit::where('marketing_list_id', $marketingList->id)->count();

            if ($existingLimits > 0) {
                $this->command->info("  - Skipping (already has {$existingLimits} limits)");
                continue;
            }

            // Create limits for each domain group
            foreach ($this->domainGroups as $groupName => $groupData) {
                MarketingListLimit::create([
                    'marketing_list_id' => $marketingList->id,
                    'group' => $groupName,
                    'domains' => $groupData['domains'],
                    'limit' => $groupData['limit'],
                ]);

                $this->command->info("  - Created limit for {$groupName}: " . implode(', ', $groupData['domains']) . " (limit: {$groupData['limit']})");
            }
        }

        $this->command->info('MarketingListLimitSeeder completed successfully.');
    }
}
