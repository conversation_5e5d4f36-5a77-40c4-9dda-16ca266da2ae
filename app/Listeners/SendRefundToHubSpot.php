<?php

namespace App\Listeners;

use App\Enums\AccountSource;
use App\Enums\OrderStatus;
use App\Enums\PayloadSource;
use App\Enums\PayloadStatus;
use App\Enums\PhoneLineType;
use App\Events\RefundSaved;
use App\Exceptions\ExceptionDontReport;
use App\Facades\OpenAi;
use App\Helpers\PhoneHelper;
use App\Models\Customer;
use App\Models\DomainProduct;
use App\Models\Order;
use App\Facades\HubSpot;
use App\Models\Refund;
use App\Services\HubSpot\HubspotDealService;
use App\Services\HubSpotService;
use App\Services\PayloadService;
use App\Traits\HubspotObjectsTrait;
use HubSpot\Client\Crm\Contacts\ApiException;
use HubSpot\Client\Crm\Deals\Model\Error;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\UniqueConstraintViolationException;
use Illuminate\Support\Arr;

class SendRefundToHubSpot implements ShouldQueue
{
    use HubspotObjectsTrait;

    public $queue = 'hubspot';
    public $delay = 60 * 5;
    public $tries = 10;
    public $backoff = 90;

    public function __construct(private PayloadService $payloadService)
    {
    }

    /**
     * Handle the event.
     */
    public function handle(RefundSaved $event): void
    {
        if (!config('services.hubspot.sync_enabled')) {
            return;
        }

        try {
            $refund = $event->refund;

            if ($refund->account->source === AccountSource::COMMITTED_COACHES->value
                || $refund->account->source === AccountSource::AMAZON_SELLER_CENTRAL->value) {
                return;
            }

            if (!empty($refund->hubspot_id)) {
                return;
            }

            $this->createHubspotDeal($refund);
        } catch (UniqueConstraintViolationException $e) {
            throw new ExceptionDontReport($e->getMessage());
        } catch (\Exception $e) {
            report($e);

            throw $e;
        }
    }

    protected function createHubspotDeal(Refund $refund): void
    {
        $dealProperties = $this->getDealData($refund);
        $payloadModel = $this->payloadService->getPayload(
            PayloadSource::HUBSPOT_SEND_REFUND,
            $dealProperties,
            $refund->order->source_ref
        );

        $this->payloadService->attachModelToPayload($payloadModel, $refund, true);
        $this->payloadService->attachModelToPayload($payloadModel, $refund->order, true);

        $contactId = $refund->order->customer->hubspot_id;
        if (empty($contactId)) {
            $contactId = $this->searchOrCreateHubspotContact($refund->order, $refund->order->customer);
        }

        if (!$contactId) {
            return;
        }

        $dealAssociations = [
            $contactId => [
                'association_category' => HubSpotService::ASSOCIATION_CATEGORY_HUBSPOT_DEFINED,
                'association_type_id'  => HubSpotService::ASSOCIATION_DEAL_TO_CONTACT_TYPE_ID,
            ],
        ];

        try {
            $deal = HubSpot::createDeal($dealProperties, $dealAssociations);

            if ($deal instanceof Error) {
                throw new \Exception($deal->getMessage());
            }
        } catch (\Exception $e) {
            $this->payloadService->savePayloadError($payloadModel, $e);

            throw $e;
        }

        $this->payloadService->savePayloadResponse($payloadModel, $deal);
        $this->payloadService->setStatus($payloadModel, PayloadStatus::SUCCESS);

        $refund->hubspot_id = $deal['id'];

        $refund->saveQuietly();
    }

    public function getFunnel(Order $order): ?string
    {
        // Get the first domain from the first OrderOffer.
        foreach ($order->orderOffers as $orderOffer) {
            $domain = $orderOffer->domain;

            if ($domain) {
                return $domain->domain;
            }
        }

        // Try looking at the domain product association. (legacy data)
        if ($order->getMeta('domain_product')) {
            $domainProduct = DomainProduct::find($order->getMeta('domain_product'));

            if ($domainProduct) {
                return $domainProduct->domain->domain;
            }
        }

        foreach ($order->offers as $offer) {
            foreach ($offer->products as $product) {
                foreach ($product->domains as $domain) {
                    if ($domain->domain) {
                        return $domain->domain;
                    }
                }
            }
        }

        return null;
    }

    protected function getDealData(Refund $refund): array
    {
        $offer = $refund->order->offers->first();

        return [
            'dealname'                 => $refund->order->customer->full_name ?? $refund->order->customer->email,
            'pipeline'                 => HubSpotService::PIPELINE_ID,
            'closedate'                => $refund->order->purchased_at,
            'transaction_type'         => OrderStatus::getHubSpotTransactionTypes(OrderStatus::REFUNDED->value),
            'receipt'                  => $refund->order->source_ref,
            'previous_order_receipt'   => $refund->order->parentOrder?->source_ref ?? '',
            'payment_method'           => $refund->order->payment_method ?? '',
            'sales_funnel'             => $this->getFunnel($refund->order),
            'tracking_number'          => $refund->order->shipments->implode('tracking_number', ','),
            'shipped_at'               => $refund->order->shipments->value('shipped_at')?->format('Y-m-d') ?? '',
            'offer_source_ref'         => $offer?->source_ref,
            'offer_quantity'           => $refund->order->orderOffers->value('quantity', 1) * ($offer?->quantity ?? 1),
            'import_sku'               => $offer?->product_skus?->implode(', '),
            'brand'                    => app(HubspotDealService::class)->getBrandsString($offer),
            'processor'                => HubSpotService::PROCESSORS[$refund->order->account->source] ?? '',
            'account_source_reference' => $refund->order->account->source_ref,
            'dealstage'                => HubSpotService::PIPELINE_STAGE_REFUNDS_ID,
            'media_source'             => HubSpotService::MEDIA_SOURCE_ONLINE,
            'affiliate_id'             => $refund->order->affiliate?->source_ref ?? '',
            'amount'                   => $refund->amount->negative()->formatByDecimal(),
        ];
    }

    protected function searchOrCreateHubspotContact(Order $order, Customer $customer): ?int
    {
        $contact = HubSpot::searchContact(['email' => $customer->email], ['hs_object_id']);

        if (empty($contact) && !$customer->numverify_valid && $customer->zerobounce_status != 'valid') {
            return null;
        }

        $address = $customer->getAddress();
        $lifecycleStage = $order->status == OrderStatus::ABANDONED ? HubSpotService::LIFECYCLE_STAGE_OPPORTUNITY : HubSpotService::LIFECYCLE_STAGE_CUSTOMER;

        $contactProperties = [
            'email'                       => $customer->email,
            'zb_status'                   => $customer->zerobounce_status,
            'firstname'                   => $customer->first_name,
            'lastname'                    => $customer->last_name,
            'phone'                       => PhoneHelper::formatting($customer->phone),
            'numverify_valid'             => $customer->numverify_valid,
            'line_type'                   => PhoneLineType::getHubspotLineType($customer->line_type ?? ''),
            'address'                     => empty($address) ? '' : $address->full_address,
            'city'                        => $address->city ?? '',
            'state'                       => $address->state ?? '',
            'country'                     => $address->country ?? '',
            'zip'                         => $address->postal_code ?? '',
            'hs_marketable_status'        => true,
            'hs_marketable_until_renewal' => false,
            'lifecyclestage'              => $lifecycleStage,
            'ip_address'                  => $order->ip_address ?? '',
        ];

        $payloadModel = $this->payloadService->getPayload(
            PayloadSource::HUBSPOT_SEND_CONTACT,
            $contactProperties,
            $customer->id
        );
        $this->payloadService->attachModelToPayload($payloadModel, $customer, true);
        $this->payloadService->attachModelToPayload($payloadModel, $address, true);
        $this->payloadService->attachModelToPayload($payloadModel, $order, true);

        try {
            if (empty($contact)) {
                try {
                    $contact = HubSpot::createContact($contactProperties);
                } catch (ApiException $e) {
                    if (OpenAi::shouldFixEmail($e)) {
                        $contact = $this->fixEmailAndCheckContact($contactProperties);
                    } elseif (str_contains($e->getMessage(), 'Contact already exists')) {
                        if (preg_match('/Existing ID: (\d+)/', $e->getMessage(), $matches)) {
                            $contactId = $matches[1];
                            $contact = HubSpot::getContactById($contactId);
                        } else {
                            throw $e;
                        }
                    } else {
                        throw $e;
                    }
                }
            } else {
                Arr::forget($contactProperties, ['hs_marketable_status', 'hs_marketable_until_renewal']);

                $contact = HubSpot::updateContact($contact[0]['properties']['hs_object_id'], $contactProperties);
            }

            if ($contact instanceof ApiException) {
                throw $contact;
            }

            $customer->hubspot_id = is_numeric($contact) ? $contact : $contact['id'];
            $customer->save();
        } catch (ApiException $e) {
            $this->payloadService->savePayloadError($payloadModel, $e);

            throw $e;
        }

        $this->payloadService->savePayloadResponse($payloadModel, $contact);
        $this->payloadService->setStatus($payloadModel, PayloadStatus::SUCCESS);

        return is_numeric($contact) ? $contact : Arr::get($contact, 'id');
    }
}
