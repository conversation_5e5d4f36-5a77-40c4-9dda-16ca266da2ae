<?php

namespace App\Listeners;

use App\Enums\AccountSource;
use App\Enums\OrderStatus;
use App\Enums\PayloadSource;
use App\Enums\PayloadStatus;
use App\Events\OrderCreated;
use App\Models\Offer;
use App\Models\Order;
use App\Models\Product;
use App\Models\ProductSku;
use App\Services\PayloadService;
use Exception;
use Http;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class CommittedCoachesLead implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * @var int Delay to give all offers on the order time to attach from the payloads.
     */
    public int $delay = 60 * 5;
    public $tries = 5;
    public $backoff = 60 * 15;

    /**
     * Handle the event.
     */
    public function handle(OrderCreated $event): void
    {
        if (!config('services.committed_coaches.webhook_enabled')) {
            return;
        }

        $order = $event->order;

        if (!OrderStatus::COMPLETED->is($order->status)) {
            return;
        }

        if (AccountSource::COMMITTED_COACHES->is($order->account->source)) {
            return; // Don't send them their own orders.
        }

        if (!$this->hasValidProducts($order)) {
            return;
        }

        // Don't send orders older than a week. Indicates a backfill.
        if ($order->purchased_at->isBefore(now()->subWeek())) {
            return;
        }

        $data = $this->compileData($order);

        $this->sendPayloadData($data, $order);
    }

    public function compileData(Order $order): array
    {
        $shippingAddress = $order->shippingAddress;

        if (empty($shippingAddress)) {
            $shippingAddress = $order->customer->shippingAddress;
        }

        $billingAddress = $order->billingAddress;

        if (empty($billingAddress)) {
            $billingAddress = $order->customer->billingAddress;
        }

        return [
            'event'    => 'lead',
            'customer' => [
                'id'         => $order->customer_id,
                'first_name' => $order->customer->clean_first_name
                    ?: $shippingAddress?->clean_first_name
                        ?: $billingAddress?->clean_first_name,
                'last_name'  => $order->customer->clean_last_name
                    ?: $shippingAddress?->clean_last_name
                        ?: $billingAddress?->clean_last_name,
                'email'      => $order->customer->email,
                'phone'      => $order->customer->phone,
            ],
            'order'    => [
                'id'           => $order->id,
                'source_ref'   => $order->source_ref,
                'status'       => $order->status,
                'purchased_at' => $order->purchased_at->tz(config('app.timezone'))->toDateTimeString(),
                'offers'       => $order->offers->map(function (Offer $offer) {
                    return [
                        'id'         => $offer->id,
                        'source_ref' => $offer->source_ref,
                        'name'       => $offer->name,
                        'products'   => $offer->products->map(function (Product $product) {
                            return [
                                'id'   => $product->id,
                                'name' => $product->name,
                                'skus' => $product->skus->flatMap(fn(ProductSku $sku) => $sku->sku)->toArray(),
                            ];
                        }),
                        'account'    => [
                            'id'         => $offer->account->id,
                            'source_ref' => $offer->account->source_ref,
                            'source'     => $offer->account->source,
                            'name'       => $offer->account->name,
                        ],
                    ];
                }),
            ],
            'address'  => [
                'street'      => $shippingAddress?->address ?? $billingAddress?->address . (
                        $shippingAddress?->address2 ?? $billingAddress?->address2 ? ' ' . $shippingAddress?->address2 ?? $billingAddress?->address2 : ''
                    ) . (
                        $shippingAddress?->address3 ?? $billingAddress?->address3 ? ' ' . $shippingAddress?->address3 ?? $billingAddress?->address3 : ''
                    ),
                'city'        => $shippingAddress?->city ?? $billingAddress?->city,
                'state'       => $shippingAddress?->state ?? $billingAddress?->state,
                'postal_code' => $shippingAddress?->postal_code ?? $billingAddress?->postal_code,
                'country'     => $shippingAddress?->country ?? $billingAddress?->country,
            ],
        ];
    }

    public function sendPayloadData(array $data, Order $order): array|string
    {
        $payloadService = app()->make(PayloadService::class);

        $payload = $payloadService->getPayload(PayloadSource::COMMITTED_COACHES_LEAD, $data, $order->source_ref);

        try {
            $response = Http::asJson()->post(config('services.committed_coaches.webhook_url'), $data);

            $responseData = $response->json() ?? $response->body();

            if ($response->failed()) {
                $response->throw();
            }

            $payloadService->setStatus($payload, PayloadStatus::SUCCESS);

            if ($responseData) {
                $payloadService->savePayloadResponse($payload, $responseData);
            }

            return $responseData;
        } catch (Exception $e) {
            $payloadService->setStatus($payload, PayloadStatus::FAILED);
            $payloadService->savePayloadError($payload, $responseData ?? $e);

            throw $e;
        }
    }

    public function hasValidProducts(Order $order): bool
    {
        // Only send orders for a product that Committed Coaches can service.
        return $order->offers->filter(function (Offer $offer) {
            // Check if offer's product has SKUs on the excluded list and completely exit the function if so.
            if ($offer->products->filter(function ($product) {
                foreach ($product?->skus ?? [] as $productSku) {
                    if (in_array($productSku->sku, config('services.committed_coaches.excluded_products'))) {
                        return true;
                    }
                }

                return false;
            })->isNotEmpty()) {
                return false;
            }

            $includedProducts = config('services.committed_coaches.included_products');

            // If included_products is empty or contains only empty strings,
            // include all products (except those excluded above)
            if (empty($includedProducts) || (count($includedProducts) === 1 && empty($includedProducts[0]))) {
                return true;
            }

            // Check if offer's product has SKUs on the included list.
            $valid_product = $offer->products->filter(function ($product) use ($includedProducts) {
                foreach ($product?->skus ?? [] as $productSku) {
                    if (in_array($productSku->sku, $includedProducts)) {
                        return true;
                    }
                }

                return false;
            })->isNotEmpty();

            if ($valid_product) {
                return true;
            }

            // Check if the offer is from a Committed Coaches authorized account.
            if (in_array($offer->account_id, config('services.committed_coaches.accounts'))) {
                return true;
            }

            return false;
        })->isNotEmpty();
    }
}
