<?php

namespace App\Traits;

use App\Models\User;
use App\Notifications\ReportLinkNotification;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;

trait GeneratesCsvReports
{
    /**
     * Generate CSV report and upload to S3
     */
    protected function generateAndUploadCsvReport(
        Collection $data,
        array $headers,
        string $baseFilename,
        User $user,
        bool $sendEmail = false
    ): ?string {
        if ($data->isEmpty()) {
            $this->info("No data to export for {$baseFilename}");

            return null;
        }

        $localFilePath = $this->generateLocalCsvFile($data, $headers, $baseFilename);
        $s3Url = $this->uploadToS3($localFilePath, $user);
        
        if ($s3Url) {
            $cloudFrontUrl = $this->generateCloudFrontUrl($s3Url);
            $this->info("Report uploaded: {$cloudFrontUrl}");
            
            if ($sendEmail) {
                $this->sendReportEmail($user, $baseFilename, $cloudFrontUrl);
            }
            
            // Clean up local file
            if (file_exists($localFilePath)) {
                unlink($localFilePath);
            }
            
            return $cloudFrontUrl;
        }

        return null;
    }

    /**
     * Generate local CSV file
     */
    private function generateLocalCsvFile(Collection $data, array $headers, string $baseFilename): string
    {
        $folder = storage_path('app/exports');
        
        if (!file_exists($folder)) {
            mkdir($folder, 0777, true);
        }

        $filename = $baseFilename . '_' . now()->format('Y-m-d_His') . '.csv';
        $filePath = $folder . '/' . $filename;
        
        $file = fopen($filePath, 'w+');
        
        // Write headers
        fputcsv($file, $headers);
        
        // Write data
        foreach ($data as $row) {
            fputcsv($file, $row);
        }
        
        fclose($file);
        
        return $filePath;
    }

    /**
     * Upload file to S3
     */
    private function uploadToS3(string $localFilePath, User $user): ?string
    {
        $filename = basename($localFilePath);
        $s3Path = sprintf('exports/user_%s/%s', $user->id, $filename);
        
        $content = file_get_contents($localFilePath);
        $result = Storage::disk('s3')->put($s3Path, $content);
        
        if ($result) {
            return Storage::disk('s3')->url($s3Path);
        }

        $this->error("Failed to upload {$filename} to S3");

        return null;
    }

    /**
     * Generate CloudFront URL from S3 URL
     */
    private function generateCloudFrontUrl(string $s3Url): string
    {
        $assetUrl = config('app.asset_url');

        if ($assetUrl) {
            // Extract the path from S3 URL
            $path = parse_url($s3Url, PHP_URL_PATH);

            return rtrim($assetUrl, '/') . $path;
        }

        return $s3Url;
    }

    /**
     * Send report email to user
     */
    private function sendReportEmail(User $user, string $reportName, string $downloadUrl): void
    {
        $user->notify(new ReportLinkNotification($reportName, $downloadUrl));
        $this->info("Report email sent to {$user->email}");
    }

    /**
     * Get user by email address
     */
    protected function getUserByEmail(string $email): ?User
    {
        return User::where('email', $email)->first();
    }

    /**
     * Ask for user email if not provided
     */
    protected function askForUserEmail(): string
    {
        return $this->ask('Please enter your email address to receive the report links:');
    }

    /**
     * Generate filename with date range
     */
    protected function generateFilenameWithDateRange(string $baseName, string $listName = null): string
    {
        $filename = $baseName;
        
        if ($listName) {
            $filename .= '_' . str_replace([' ', '-'], '_', strtolower($listName));
        }
        
        $filename .= '_' . $this->startsAt->format('Y-m-d') . '_to_' . $this->endsAt->format('Y-m-d');
        
        return $filename;
    }
}
