<?php

namespace App\Nova\Wizards;

use App\Enums\BusinessUnit;
use App\Enums\Category;
use App\Enums\SubCategory;
use App\Models\AccountAffiliate;
use App\Models\Affiliate;
use App\Models\User;
use App\Nova\Repeaters\AffiliateAccountRepeaterField;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Repeater;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Wdelfuego\NovaWizard\AbstractWizard;

class AddAffiliate extends AbstractWizard
{
    private $resource;
    private $allowedFields = [
        'user_id',
        'name',
        'business_unit',
        'category',
        'sub_category',
        'enabled',
        'is_approved',
    ];

    public function __construct()
    {
        $this->resource = new Affiliate();
    }

    public function wizardViewData(): array
    {
        return [
            'steps' => [
                [
                    'title' => 'Create affiliate',
                    'fields' => [
                        Select::make('User', 'user_id')
                            ->nullable()
                            ->options(
                                fn() => User::query()
                                ->select(['id', 'name'])
                                ->orderBy('name')
                                ->get()
                                ->mapWithKeys(fn (User $user) => [$user->id => $user->name])
                            )
                            ->searchable(),

                        Text::make('Name')
                            ->required()
                            ->rules('required', 'max:255'),

                        Select::make('Business Unit')
                            ->options(BusinessUnit::keyValueCases())
                            ->nullable()
                            ->displayUsing(fn() => BusinessUnit::properCase($this->resource->business_unit))
                            ->rules($this->resource->getRules('business_unit'))
                            ->help('Business Unit or name of Dashboard in Looker.'),

                        Select::make('Category')
                            ->options(Category::keyValueCases())
                            ->nullable()
                            ->displayUsing(fn() => Category::properCase($this->resource->category))
                            ->rules($this->resource->getRules('category')),

                        Select::make('Sub-Category', 'sub_category')
                            ->options(SubCategory::keyValueCases())
                            ->nullable()
                            ->displayUsing(fn() => SubCategory::properCase($this->resource->sub_category))
                            ->rules($this->resource->getRules('sub_category')),

                        Boolean::make('Enabled')
                            ->help('Whether this affiliate is enabled or not.'),

                        Boolean::make('Is Approved')
                            ->help('Whether this affiliate has been approved to login and make requests.'),
                    ],
                ],
                [
                    'title' => 'Add accounts',
                    'fields' => [
                        Repeater::make('Accounts')
                            ->required()
                            ->repeatables(
                                [
                                    AffiliateAccountRepeaterField::make()
                                ]
                            )
                            ->rules(['required'])
                    ],
                ],
            ],
        ];
    }

    public function onSubmit($formData, &$context): bool
    {
        $accountAffiliates = [];
        foreach ($formData['accounts'] ?? [] as $v) {
            $accountAffiliates[] = new AccountAffiliate(
                [
                    'account_id' => $v['fields']['account_id'],
                    'affiliate_source_ref' => $v['fields']['source_ref'],
                ]
            );
        }

        $affiliate = new Affiliate(
            collect($formData)
                ->only($this->allowedFields)
                ->toArray()
        );
        $affiliate->save();
        $affiliate->accountAffiliates()->saveMany($accountAffiliates);

        $context['id'] = $affiliate->id;

        return true;
    }

    public function successViewData($context): array
    {
        return [
            'message' => sprintf('<a href="/nova/resources/affiliates/%s">Go to affiliate</a>', $context['id'])
        ];
    }
}
