<?php

namespace App\Nova;

use App\Enums\CustomerGender;
use App\Enums\ExportType;
use App\Helpers\ExportHelper;
use App\Nova\Actions\ExportAsCsvCustom;
use App\Nova\Actions\ExportLALCustomers;
use App\Nova\Actions\ScheduledExportCustomerAction;
use App\Nova\Filters\AccountFilter;
use App\Nova\Filters\CustomDateFilter;
use App\Nova\Filters\CustomEndDateFilter;
use App\Nova\Filters\CustomerAbandonedCart;
use App\Nova\Filters\CustomerAccountSourceFilter;
use App\Nova\Filters\CustomerIsNumverifyValidFilter;
use App\Nova\Filters\CustomerProductFilter;
use App\Nova\Filters\CustomerZerobounceValidationFilter;
use App\Nova\Filters\CustomStartDateFilter;
use App\Nova\Filters\HasOrderWithLateShipmentFilter;
use App\Nova\Metrics\CustomerPhoneCountMetric;
use Illuminate\Support\Facades\Auth;
use Laravel\Nova\Fields\Badge;
use Laravel\Nova\Fields\BelongsToMany;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\Email;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\MorphMany;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

/**
 * The model the resource corresponds to.
 *
 * @var class-string<\App\Models\Customer>
 *
 * @property-read \App\Models\Customer $resource
 */
class Customer extends BasePiiResource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Customer>
     */
    public static $model = \App\Models\Customer::class;

    public static $group = 'Sales';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'first_name',
        'last_name',
        'email',
        'phone',
    ];

    public function title()
    {
        return implode(' | ', [
            $this->email,
            $this->full_name,
            $this->phone,
        ]);
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @param  NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Name', 'full_name')
                ->onlyOnIndex()
                ->filterable(function ($request, $query, $value, $attribute) {
                    return $query->where('first_name', 'LIKE', '%' . $value . '%')
                        ->orWhere('last_name', 'LIKE', '%' . $value . '%');
                })
                ->sortable(),

            Text::make('First Name', 'first_name')
                ->hideFromIndex()
                ->rules(array_merge($this->resource->getRules('first_name'), ['regex:/^[a-zA-Z- ]*$/i'])),

            Text::make('Last Name', 'last_name')
                ->hideFromIndex()
                ->rules(array_merge($this->resource->getRules('last_name'), ['regex:/^[a-zA-Z- ]*$/i'])),

            Select::make('Gender')
                ->options(CustomerGender::keyValueCases())
                ->rules($this->resource->getRules('gender'))
                ->filterable()
                ->sortable(),

            Email::make('Email')
                ->filterable(function ($request, $query, $value, $attribute) {
                    return $query->where('email', 'LIKE', '%' . $value . '%');
                })
                ->sortable()
                ->rules($this->resource->getRules('email')),

            Text::make('Zerobounce Status', 'zerobounce_status')
                ->onlyOnDetail()
                ->readonly(),

            Badge::make('Zerobounce', 'zerobounce_status')
                ->withIcons()
                ->labels([
                    'valid'       => 'Valid',
                    'invalid'     => 'Invalid',
                    'catch-all'   => 'Catch All',
                    'abuse'       => 'Abuse',
                    'spamtrap'    => 'Spam Trap',
                    'do_not_mail' => 'Do Not Mail',
                    'unknown'     => 'Unknown',
                    null          => 'Not Checked',
                ])
                ->map([
                    'valid'       => 'success',
                    'invalid'     => 'danger',
                    'catch-all'   => 'warning',
                    'abuse'       => 'danger',
                    'spamtrap'    => 'danger',
                    'do_not_mail' => 'danger',
                    'unknown'     => 'info',
                    null          => 'info',
                ])
                ->onlyOnIndex(),

            Text::make('Phone'),

            Text::make('Hubspot ID', 'hubspot_id')
                ->onlyOnDetail()
                ->help('Hubspot ID is used to sync with Hubspot'),

            Text::make('Numverify Valid', 'numverify_valid')
                ->onlyOnDetail()
                ->readonly(),

            Badge::make('Numverify', 'numverify_valid')
                ->withIcons()
                ->labels([
                    true  => 'Yes',
                    false => 'No',
                    null  => 'Unknown',
                ])
                ->map([
                    true  => 'success',
                    false => 'danger',
                    null  => 'info',
                ])
                ->onlyOnIndex(),

            DateTime::make('Created', 'created_at')
                ->hideWhenCreating()
                ->hideWhenUpdating(),

            DateTime::make('Updated', 'updated_at')
                ->onlyOnDetail(),

            DateTime::make('Deleted', 'deleted_at')
                ->onlyOnDetail(),

            BelongsToMany::make('Accounts')
                ->canSee(fn($request) => $request->user()->can('view accounts'))
                ->onlyOnDetail(),

            HasMany::make('Orders')
                ->canSee(fn($request) => $request->user()->can('view orders'))
                ->onlyOnDetail(),

            HasMany::make('Addresses')
                ->canSee(fn($request) => $request->user()->can('view addresses'))
                ->onlyOnDetail(),

            MorphMany::make('Audits')
                ->canSee(fn($request) => $request->user()->can('view audits'))
                ->onlyOnDetail(),

            MorphMany::make('Meta', 'meta', Meta::class)
                ->canSee(fn($request) => $request->user()->can('view meta'))
                ->onlyOnDetail(),

            MorphMany::make('Payloads', 'payloads', Payload::class)
                ->canSee(fn($request) => $request->user()->can('view payloads'))
                ->onlyOnDetail(),

            BelongsToMany::make('Marketing Lists')
                ->fields(function ($request, $relatedModel) {
                    return [
                        DateTime::make('Subscribed At', 'subscribed_at'),
                    ];
                })
                ->onlyOnDetail(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [
            CustomerPhoneCountMetric::make()
                ->defaultRange(7)
                ->width('1/4')
                ->refreshWhenFiltersChange(),
        ];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [
            CustomerAccountSourceFilter::make(),
            AccountFilter::make(),
            CustomDateFilter::make(),
            CustomStartDateFilter::make(),
            CustomEndDateFilter::make(),
            CustomerAbandonedCart::make(),
            HasOrderWithLateShipmentFilter::make(),
            CustomerIsNumverifyValidFilter::make(),
            CustomerZerobounceValidationFilter::make(),
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [
            ScheduledExportCustomerAction::make('Scheduled Look-a-like')
                ->withName('Scheduled Look-a-like'),

            ExportLALCustomers::make('Export Look-Alike List')
                ->nameable('Look-Alike-List')
                ->withFields(fn($request) => [
                    Select::make('Destination')
                        ->options([
                            'facebook' => 'Facebook',
                            'google'   => 'Google',
                        ])
                        ->nullable(),
                    Select::make('Product')
                        ->options(
                            \App\Models\Product::orderBy('name')->pluck('name', 'id')->toArray()
                        )
                        ->displayUsingLabels(),
                    Select::make('Sort by AOV')
                        ->options([
                            'asc' => 'asc',
                            'desc' => 'desc',
                        ])
                        ->nullable(),
                    Text::make('Percent of top', 'percent')
                    ->nullable(),
                ])
                ->exportType(ExportType::CUSTOMER_LAL)
                ->withFormat(ExportHelper::getFormat(ExportType::CUSTOMER_LAL, $request->all()))
                ->canSee(fn($request) => $request->user()->can('export customers')),

            ExportAsCsvCustom::make('Export Customers')
                ->nameable('Customers')
                ->withFormat(ExportHelper::getFormat(ExportType::CUSTOMER, $request->all(), Auth::user()))
                ->exceptInline()
                ->canSee(fn($request) => $request->user()->can('export customers'))
                ->exportType(ExportType::CUSTOMER)
                ->isJob(),
        ];
    }
}
