<?php

namespace App\Nova\Fields;

use App\Models\Product;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Lara<PERSON>\Nova\Fields\Field;
use Laravel\Nova\Http\Requests\NovaRequest;

class OfferProductField extends Field
{
    /**
     * @var string
     */
    public $component = 'offer-product-field';

    /**
     * @param Model $model
     * @param mixed $value
     * @param string $attribute
     * @return void
     */
    public function fillModelWithData(mixed $model, mixed $value, string $attribute)
    {
    }

    /**
     * @param NovaRequest $request
     * @return array
     * @throws ValidationException
     */
    public function getRules(NovaRequest $request)
    {
        if ($request->has($this->attribute) && !is_array($request->input($this->attribute))) {
            $request->merge(
                [
                    $this->attribute => !empty($request->input($this->attribute))
                        ? json_decode($request->input($this->attribute), true) : []
                ]
            );
        }

        Validator::make(
            $request->all(),
            array_merge_recursive([
                $this->attribute . '.*.product_id' => [
                    'required',
                    'integer',
                    Rule::exists(Product::class, 'id')
                ],
                $this->attribute . '.*.quantity' => [
                    'required',
                    'integer',
                ]
            ])
        )->validate();

        return parent::getRules($request);
    }

    public function jsonSerialize(): array
    {
        $products = Product::query()
            ->select(['id', 'name'])
            ->orderBy('name')
            ->cursor()
            ->map(
                function (Product $product) {
                    return [
                        'label' => $product->name,
                        'value' => $product->id
                    ];
                }
            );

        return array_merge(
            parent::jsonSerialize(),
            compact('products')
        );
    }
}
