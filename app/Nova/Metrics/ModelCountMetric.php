<?php

namespace App\Nova\Metrics;

use App\Helpers\Query;
use Illuminate\Database\Eloquent\Builder;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Value;
use <PERSON>vel\Nova\Nova;

class ModelCountMetric extends Value
{
    public function __construct(
        protected ?string $title = null,
        protected ?Builder $query = null,
        $component = null
    ) {
        parent::__construct($component);
    }

    /**
     * Calculate the value of the metric.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        if ($this->query) {
            $query = $this->query;
        } else {
            $query = $request->model()->on(Query::maybeReadConnection())->newQuery();
        }

        $dateColumn = 'created_at';
        if ($request->resource === 'refunds') {
            $dateColumn = 'refunded_at';
        } elseif ($request->resource === 'chargeback-fees') {
            $dateColumn = 'chargeback_at';
        }

        return $this->count($request, $query, null, $dateColumn)->format('0,0');
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            7       => Nova::__('7 Days'),
            30      => Nova::__('30 Days'),
            60      => Nova::__('60 Days'),
            365     => Nova::__('365 Days'),
            'TODAY' => Nova::__('Today'),
            'MTD'   => Nova::__('Month To Date'),
            'QTD'   => Nova::__('Quarter To Date'),
            'YTD'   => Nova::__('Year To Date'),
            'ALL'   => Nova::__('All Time'),
        ];
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int|null
     */
    public function cacheFor()
    {
        if (app()->environment('local')) {
            return null;
        }

        return now()->addMinutes(5);
    }

    /**
     * Get the displayable name of the metric
     *
     * @return string
     */
    public function name()
    {
        return $this->title ?? 'Count';
    }
}
