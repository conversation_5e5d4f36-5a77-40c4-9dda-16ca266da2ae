<?php

namespace App\Nova\Filters;

use <PERSON><PERSON>\Nova\Filters\Filter;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class CustomerZerobounceValidationFilter extends Filter
{
    public function name()
    {
        return 'Zerobounce Validation';
    }

    /**
     * Apply the filter to the given query.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(NovaRequest $request, $query, $value)
    {
        $model = $request->model();

        if ($model->getTable() === 'customers') {
            $query->where('zerobounce_status', $value);

            return $query;
        }

        return $query->whereHas('customer', function ($query) use ($value) {
            $query->where('zerobounce_status', $value);
        });
    }

    /**
     * Get the filter's available options.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function options(NovaRequest $request)
    {
        return [
            'valid',
            'invalid',
            'catch-all',
            'abuse',
            'spamtrap',
            'do_not_mail',
            'unknown',
        ];
    }
}
