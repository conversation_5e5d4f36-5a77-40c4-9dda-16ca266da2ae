<?php

namespace App\Nova\Filters;

use App\Enums\AccountSource;
use App\Helpers\Query;
use App\Models\Offer;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Outl1ne\NovaMultiselectFilter\MultiselectFilter;

class OfferFilter extends MultiselectFilter
{
    /**
     * Apply the filter to the given query.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(Request $request, $query, $value)
    {
        return $query->whereIn('offer_id', array_wrap($value));
    }

    /**
     * Get the filter's available options.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function options(Request $request)
    {
        if ($offers = cache('nova:offers_filter_options')) {
            return $offers;
        }

        $offers = Offer::on(Query::maybeReadConnection())
            ->with('account')
            ->get()
            ->groupBy('account.source')
            ->sortBy('source_ref')
            ->mapWithKeys(function (Collection $offers) {
                // Create label, value, group array for each offer.
                return $offers
                    ->sortBy('name')
                    ->mapWithKeys(function (Offer $offer) {
                        return [
                            $offer->id => [
                                'label' =>
                                    $offer->source_ref.
                                    ' - '.
                                    $offer->price->format().
                                    ' × '.
                                    $offer->quantity,
                                'value' => $offer->id,
                                'group' => AccountSource::properCase($offer->account->source),
                            ],
                        ];
                    })->toArray();
            })->toArray();

        cache(['nova:offers_filter_options' => $offers], now()->addHours(12));

        return $offers;
    }
}
