<?php

namespace App\Nova\Filters;

use App\Models\Product;
use Illuminate\Database\Eloquent\Builder;
use <PERSON>vel\Nova\Filters\Filter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class CustomerProductFilter extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->whereHas('orders', function (Builder $builder) use ($value) {
            $builder->whereHas('orderOffers', function (Builder $builder) use ($value) {
                $builder->whereHas('offer', function (Builder $builder) use ($value) {
                    $builder->whereHas('products', function (Builder $builder) use ($value) {
                        $builder->where('products.id', '=', $value);
                    });
                });
            });
        });
    }

    /**
     * Get the filter's available options.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function options(NovaRequest $request)
    {
        return Product::all()
            ->sortBy('name')
            ->mapWithKeys(fn(Product $product) => [$product->name => $product->id])
            ->toArray();
    }
}
