<?php

namespace App\Nova\Filters;

use App\Models\Product;
use Laravel\Nova\Http\Requests\NovaRequest;
use Outl1ne\NovaMultiselectFilter\MultiselectFilter;

class ProductFilter extends MultiselectFilter
{
    /**
     * Apply the filter to the given query.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->whereHas('orderOffers.offer', function ($query) use ($value) {
            $query->whereHas('products', fn($q) => $q->whereIn('products.id', array_wrap($value)));
        });
    }

    /**
     * Get the filter's available options.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function options(NovaRequest $request)
    {
        return Product::all()
            ->sortBy('name')
            ->mapWithKeys(fn(Product $product) => [$product->id => $product->name])
            ->toArray();
    }
}
