<?php

namespace App\Nova\Filters;

use App\Enums\AccountSource;
use App\Models\Account;
use <PERSON>vel\Nova\Filters\Filter;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class AccountSourceFilter extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->whereHas('account', function ($query) use ($value) {
            $query->where('accounts.source', $value);
        });
    }

    /**
     * Get the filter's available options.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function options(NovaRequest $request)
    {
        return Account::groupBy('source')
            ->get()
            ->sortBy('name')
            ->mapWithKeys(fn(Account $account) => [AccountSource::properCase($account->source) => $account->source])
            ->toArray();
    }
}
