<?php

namespace App\Nova\Filters;

use App\Models\ScheduledExport;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use <PERSON>vel\Nova\Filters\Filter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class ScheduledExportUserFilter extends Filter
{
    public $name = 'User';
    public $component = 'select-filter';

    /**
     * @param NovaRequest $request
     * @param  Builder  $query
     * @param  mixed  $value
     * @return Builder
     */
    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->where('user_id', '=', $value);
    }

    /**
     * @param NovaRequest $request
     * @return array
     */
    public function options(NovaRequest $request)
    {
        $scheduleExport = ScheduledExport::query()
            ->select('user_id')
            ->groupBy('user_id');

        return User::query()
            ->select(['id', 'email', 'name'])
            ->whereIn('id', $scheduleExport)
            ->orderBy('email', 'asc')
            ->get()
            ->map(
                fn ($model) => ['value' => $model->id, 'label' => sprintf('%s (%s)', $model->name, $model->email)]
            )
            ->toArray();
    }
}
