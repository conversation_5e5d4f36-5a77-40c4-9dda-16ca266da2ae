<?php

namespace App\Nova\Filters;

use Illuminate\Support\Carbon;
use <PERSON>vel\Nova\Filters\DateFilter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class HasOrderPurchasedAtFilter extends DateFilter
{
    /**
     * Apply the filter to the given query.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(NovaRequest $request, $query, $value)
    {
        session('has_order_purchased_at_value', $value);
        $value = Carbon::parse($value, config('app.timezone'));

        return $query->whereHas('orders', function($query) use ($value) {
            $query->whereBetween('purchased_at', [
                $value->copy()->startOfDay(),
                $value->copy()->endOfDay(),
            ]);
        });
    }
}
