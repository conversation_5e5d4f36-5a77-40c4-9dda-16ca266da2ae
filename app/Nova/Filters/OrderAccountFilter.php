<?php

namespace App\Nova\Filters;

use App\Enums\AccountSource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use App\Models\Account;
use Outl1ne\NovaMultiselectFilter\MultiselectFilter;

class OrderAccountFilter extends MultiselectFilter
{
    public function __construct(protected string|AccountSource|null $source = null)
    {
    }

    /**
     * Apply the filter to the given query.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(Request $request, $query, $value)
    {
        return $query->whereHas('account', fn(Builder $q) => $q->whereIn('accounts.id', $value))
            ->when($this->source, function (Builder $query) {
                return $query->whereHas('account', function (Builder $query) {
                    return $query->where('accounts.source', $this->source);
                });
            });
    }

    /**
     * Get the filter's available options.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function options(Request $request)
    {
        return Account::when($this->source, function ($query) {
            return $query->where('source', $this->source);
        })
            ->get()
            ->groupBy('source')
            ->sortBy('name')
            ->mapWithKeys(function (Collection $accounts) {
                // Create label, value, group array for each account.
                return $accounts
                    ->sortBy('name')
                    ->mapWithKeys(function (Account $account) {
                        return [
                            $account->id => [
                                'label' => $account->name . ' (' . $account->source_ref . ')',
                                'value' => $account->id,
                                'group' => AccountSource::properCase($account->source),
                            ],
                        ];
                    })->toArray();
            })->toArray();
    }
}
