<?php

namespace App\Nova\Actions;

use App\Enums\ExportType;
use App\Jobs\ExportAsCsvJob;
use Closure;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use <PERSON><PERSON>\Nova\Actions\Action;
use Laravel\Nova\Actions\ExportAsCsv;
use Laravel\Nova\Actions\Response;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Http\Requests\ActionRequest;
use ReflectionClass;
use ReflectionProperty;

class ExportLALCustomers extends ExportAsCsv
{
    public $showOnIndex = true;
    public $name = 'Export Look-ALike';
    public $showOnDetail = true;
    public $confirmButtonText = 'Run Action';
    public $standalone = true;

    private ?ExportType $exportType = null;

    public function exportType(ExportType $type): ExportLALCustomers
    {
        $this->exportType = $type;

        return $this;
    }

    /**
     * @inheritDoc
     */
    protected function dispatchRequestUsing(ActionRequest $request, Response $response, ActionFields $fields)
    {
        $exportFilename = $this->getFileName($fields);

        $this->then(function ($results) {
            return $results->first();
        });

        $query = $request->toQuery();

        $query->when($this->withQueryCallback instanceof Closure, function ($query) use ($fields) {
            return call_user_func($this->withQueryCallback, $query, $fields);
        });

        if ($sort = Arr::get($request->resolveFields()->getAttributes(), 'sort_by_aov')) {
            $query->whereHas('orders');
            $query->withAvg('orders', 'total_amount');
            $query->getQuery()->orders = null;
            $query->orderBy('orders_avg_total_amount', $sort);
        }

        if ($percent = Arr::get($request->resolveFields()->getAttributes(), 'percent')) {
            $totalCount = $query->count();
            $limit = ceil($totalCount * ($percent / 100));
            $query->limit($limit);
        }

        if ($product = Arr::get($request->resolveFields()->getAttributes(), 'product')) {
            $query->whereHas('orders', function ($builder) use ($product) {
                $builder->whereHas('orderOffers', function ($builder) use ($product) {
                    $builder->whereHas('offer', function ($builder) use ($product) {
                        $builder->whereHas('products', function ($builder) use ($product) {
                            $builder->where('products.id', '=', $product);
                        });
                    });
                });
            });
        }

        $data = $this->getData($query->getQuery());

        foreach ($data as &$row) {
            $this->dataReplaceBuilder($row);
        }

        dispatch(
            new ExportAsCsvJob(
                $request->user(),
                get_class($query->getModel()),
                $data,
                $exportFilename,
                $request->resolveFields()->getAttributes(),
                $this->exportType
            )
        );

        return $response->successful(Action::message('After creating the report, it will be sent to you by email'));
    }

    private function getData(Builder $query): array
    {
        $data = [];
        $reflection = new ReflectionClass($query);
        foreach ($reflection->getProperties(ReflectionProperty::IS_PUBLIC) as $property) {
            $field = $property->getName();
            if (gettype($query->$field) == 'object') {
                continue;
            }

            $data[$field] = $query->$field;
        }

        return $data;
    }

    protected function getFileName(ActionFields $fields): string
    {
        $filename = $fields->get('filename') ?? sprintf('%s-%d.csv', $this->uriKey(), now()->format('YmdHis'));

        $extension = 'csv';

        if (Str::contains($filename, '.')) {
            [$filename, $extension] = explode('.', $filename);
        }

        return sprintf(
            '%s.%s',
            $filename,
            $fields->get('writerType') ?? $extension
        );
    }

    private function saveFileToS3(Response $response, string $exportFilename, ActionRequest $request): void
    {
        ob_start();
        $response->results[0]->sendContent();
        $content = ob_get_flush();
        ob_end_clean();

        $filePath = sprintf(
            'exports/user_%s/%s',
            $request->user()->id,
            $exportFilename
        );
        Storage::disk('s3')->put($filePath, $content);
        $response->successful($content);
    }

    private function dataReplaceBuilder(mixed &$row): void
    {
        if (gettype($row) == "object" && is_a($row, Builder::class)) {
            $row = $this->getData($row);
            foreach ($row as &$v) {
                $this->dataReplaceBuilder($v);
            }

            return;
        }

        if (is_array($row)) {
            foreach ($row as &$v) {
                $this->dataReplaceBuilder($v);
            }
        }
    }
}
