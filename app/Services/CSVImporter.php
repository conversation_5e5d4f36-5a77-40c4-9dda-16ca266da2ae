<?php

namespace App\Services;

use Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Str;

class CSVImporter
{
    public function convertExcelToCSV(string $file_path, bool $delete_xls = false): string
    {
        // Read the XLS file.
        $spreadsheet = IOFactory::load($file_path);

        // Convert the XLS file to a CSV file.
        $writer = IOFactory::createWriter($spreadsheet, 'Csv');

        $writer->setDelimiter(',')
            ->setEnclosure('"')
            ->setLineEnding("\r\n")
            ->setSheetIndex(0);

        $new_file_path = str_replace('.xls', '.csv', $file_path);

        $writer->save($new_file_path);

        // Remove the XLS file.
        if ($delete_xls) {
            Storage::disk('local')->delete($file_path);
        }

        return $new_file_path;
    }

    public function findByFilename(string $needle, string $dir = 'imports'): ?string
    {
        // First, check S3.
        foreach (Storage::disk('s3')->files($dir) as $file) {
            if (Str::contains($file, $needle)) {
                // Download the file from S3 to local storage.
                $local_path = "app/$dir/".basename($file);

                Storage::disk('local')->put($local_path, Storage::disk('s3')->get($file));

                return $local_path;
            }
        }

        foreach (scandir(storage_path("app/$dir")) as $file) {
            if (Str::contains($file, $needle)) {
                return $this->getFile($file, $dir);
            }
        }

        return null;
    }

    /**
     * Get the file from S3 or local storage and return the path to the file.
     * @param  string  $filename
     * @param  string  $dir
     * @return string|false
     */
    public function getFile(string $filename, string $dir = 'imports'): string|false
    {
        $local_path = storage_path("app/$dir/$filename");

        if (!file_exists($local_path)) {
            $s3_path = "$dir/$filename";

            if (!Storage::disk('s3')->exists($s3_path)) {
                return false;
            }

            // Open a stream for reading from S3 in case of large file.
            $stream = Storage::disk('s3')->readStream($s3_path);

            if ($stream === false) {
                return false;
            }

            // Create directory if it doesn't exist
            if (!file_exists(dirname($local_path))) {
                mkdir(dirname($local_path), 0777, true);
            }

            // Open a local file handle for writing
            $localStream = fopen($local_path, 'wb');

            if ($localStream === false) {
                fclose($stream);

                return false;
            }

            // Stream contents from S3 to local file
            stream_copy_to_stream($stream, $localStream);

            // Close the streams
            fclose($stream);
            fclose($localStream);
        }

        return $local_path;
    }

    /**
     * @param  string  $file_path
     * @return false|resource
     */
    public function openFile(string $file_path)
    {
        if (!file_exists($file_path)) {
            return false;
        }

        return fopen($file_path, 'r');
    }

    /**
     * Detect the headings in the CSV file so we know which row to start on.
     *
     * @param  resource  $csvFile
     * @param  array  $headings
     * @return bool
     */
    public function detectHeadings($csvFile, array $headings): bool
    {
        while (($row = fgetcsv($csvFile)) !== false) {
            // Check the row for the first heading to see if we're on the right row.
            if (in_array(head($headings), $row)) {
                // Get the headings from this row and check if all exist in the known headings array.
                return collect($row)
                    ->filter(fn($heading) => !in_array($heading, $headings))
                    ->isEmpty();
            }
        }

        return false; // No headings found in the file.
    }

    public function isValidRow(array $data, array $headings): bool
    {
        foreach ($headings as $heading => $index) {
            if (!isset($data[$heading]) || trim($data[$heading]) === '') {
                return false;
            }
        }

        return true;
    }

    public function import(string $file_path): Collection
    {
        $data = collect(explode(PHP_EOL, Storage::disk('local')->get($file_path)));

        return $this->makeKeyedRows($data);
    }

    /**
     * Get all the data in the CSV file as rows.
     * Use this for small files. Otherwise, use a lazy collection.
     *
     * @param  resource  $file_handle
     * @return Collection
     */
    private function getDataAsRows($file_handle): Collection
    {
        $rows = [];

        while (($data = fgetcsv($file_handle)) !== false) {
            $rows[] = $data;
        }

        fclose($file_handle);

        return collect($rows);
    }

    /**
     * Convert all the rows into keyed data in one go.
     * Good for small files. Otherwise, use a lazy collection.
     *
     * @param  Collection  $dataRows
     * @return Collection
     */
    private function makeKeyedRows(Collection $dataRows): Collection
    {
        $keys = str_getcsv($dataRows[0]);

        return $dataRows
            ->slice(1)
            ->map(function ($row, $idx) use ($keys) {
                $keyedRowData = [];

                foreach ($keys as $index => $key) {
                    $rowArray = str_getcsv($row);
                    $keyedRowData[$key] = $rowArray[$index];
                }

                return $keyedRowData;
            })
            ->values();
    }

    public function makeSingleKeyedRow(
        array $row,
        array $keys,
        bool $flip = false,
        callable $callback = null
    ): Collection {
        // Flip the with the keys instead of using the values. Useful for when doing a DB insert.
        if ($flip) {
            $keys = array_flip($keys);
        }

        $keys = array_values($keys);

        $collection = new Collection();

        foreach ($row as $index => $value) {
            $key = $keys[$index];

            if ($callback) {
                $value = $callback($value, $key);
            }

            $collection->put($key, $value);
        }

        return $collection;
    }
}
