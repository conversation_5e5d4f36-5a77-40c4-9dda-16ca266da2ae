<?php

namespace App\Services\Voluum;

use App\Exceptions\VoluumException;
use App\Models\ServiceCredential;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

class VoluumAuthClient
{
    protected array $config;
    private string $baseUrl = 'https://api.voluum.com';

    public function __construct()
    {
        $this->config = config('services.voluum');
    }

    /**
     * @return string|bool|null
     */
    public function fetchToken(): string|bool|null
    {
        try {
            /** @var ServiceCredential $credential */
            $credential = ServiceCredential::firstOrCreate(['service' => ServiceCredential::VOLUUM]);

            if (!$credential) {
                return false;
            }

            if (!$credential->credentials || Carbon::now()->gt($credential->expires_at)) {
                $this->getToken($credential);
            }

            return $credential->credentials; // Access token
        } catch (Exception $ex) {
            report($ex);

            return false;
        }
    }

    private function getToken(ServiceCredential $credential): void
    {
        $res = Http::withHeaders([
             'Accept'       => 'application/json',
             'Content-Type' => 'application/json; charset=utf-8'
        ])->post(
            $this->baseUrl . '/auth/access/session',
            [
                'accessId' => $this->config['access_id'],
                'accessKey' => $this->config['access_key']
            ]
        );

        if (!isset($res['token'])) {
            throw new VoluumException('Unable refresh auth token. Error: ' . json_encode($res));
        }

        $credential->credentials = Arr::get($res, 'token');
        $credential->expires_at = Carbon::createFromFormat(
            'Y-m-d\TH:i:s.v\Z',
            Arr::get($res, 'expirationTimestamp')
        )->subMinutes(5);

        $credential->save();
    }
}
