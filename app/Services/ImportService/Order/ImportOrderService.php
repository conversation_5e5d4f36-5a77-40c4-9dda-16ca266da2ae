<?php

namespace App\Services\ImportService\Order;

use App\Dto\AffiliateDto;
use App\Dto\CustomerAddressDto;
use App\Dto\CustomerDto;
use App\Dto\ImportOptionsDto;
use App\Dto\ImportOrderDto;
use App\Dto\OfferDto;
use App\Dto\OrderDto;
use App\Dto\ShippingAddressDto;
use App\Dto\TrackingDto;
use App\Enums\AccountSource;
use App\Enums\AddressType;
use App\Enums\NotificationType;
use App\Enums\OfferType;
use App\Enums\OrderStatus;
use App\Enums\PayloadSource;
use App\Enums\PayloadStatus;
use App\Enums\TrackingType;
use App\Events\ChargebackFeeSaved;
use App\Events\EmailValidatedSuccessfully;
use App\Events\OrderSaved;
use App\Events\OrderTotalAmountCalculate;
use App\Events\RefundSaved;
use App\Exceptions\ASCException;
use App\Exceptions\DebugException;
use App\Exceptions\ImportOrderException;
use App\Exceptions\ModelValidationException;
use App\Helpers\CustomerHelper;
use App\Helpers\PhoneHelper;
use App\Jobs\SendLeadToKonnektive;
use App\Models\Account;
use App\Models\AccountAffiliate;
use App\Models\Address;
use App\Models\Affiliate;
use App\Models\ChargebackFee;
use App\Models\Customer;
use App\Models\Offer;
use App\Models\Order;
use App\Models\OrderOffer;
use App\Models\Payload;
use App\Models\Policies\Order\CanBeSentToKonnektive;
use App\Models\Product;
use App\Models\Tracking;
use App\Models\TrackingCategory;
use App\Notifications\AccountMissing;
use App\Notifications\ModelNotSaved;
use App\Services\Address\AddressServiceContract;
use App\Services\DomainProductService;
use App\Services\ImportService\Clients\ClickbankApi;
use App\Services\ImportService\Clients\DigistoreApi;
use App\Services\ImportService\Exceptions\AccountMissingException;
use App\Services\ImportService\Exceptions\ImportWithoutReportException;
use App\Services\ImportService\Exceptions\OfferMissingException;
use App\Services\ImportService\Exceptions\ProductMissingException;
use App\Services\ImportService\Exceptions\SkipImportException;
use App\Services\ImportService\ImportStrategy;
use App\Services\ImportService\Strategies\AmazonSellerCentralApiStrategy;
use App\Services\ImportService\Strategies\BuygoodsCsvStrategy;
use App\Services\ImportService\Strategies\BuygoodsIpnStrategy;
use App\Services\ImportService\Strategies\CheckoutChampStrategy;
use App\Services\ImportService\Strategies\ClickbankApiStrategy;
use App\Services\ImportService\Strategies\ClickbankInsStrategy;
use App\Services\ImportService\Strategies\CommittedCoachesStrategy;
use App\Services\ImportService\Strategies\CommittedCoachesSupplementStrategy;
use App\Services\ImportService\Strategies\DigistoreApiStrategy;
use App\Services\ImportService\Strategies\DigistoreIpnStrategy;
use App\Services\ImportService\Strategies\KonnektiveStrategy;
use App\Services\NoticeService;
use App\Services\PayloadService;
use App\Services\ReplicateStaging\ReplicateStagingService;
use Carbon\Carbon;
use Cknow\Money\Money;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification as NotificationFacade;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use League\Csv\Reader;
use League\Csv\Statement;
use Symfony\Component\Config\Definition\Exception\DuplicateKeyException;
use Throwable;

class ImportOrderService
{
    public ?string $source = null;
    public ImportStrategy $importStrategy;
    public Account $account;
    public PayloadService $payloadService;
    private DomainProductService $domainProductService;
    private AddressServiceContract $addressService;
    public ?ImportOptionsDto $options = null;
    public RefundService $refundService;
    public ReplicateStagingService $replicateStagingService;
    public NoticeService $noticeService;
    public ?Address $currentShippingAddress = null;
    public ?Address $currentBillingAddress = null;

    public function __construct(PayloadSource|string $source)
    {
        $this->payloadService = app()->make(PayloadService::class);
        $this->domainProductService = app()->make(DomainProductService::class);
        $this->addressService = app()->make(AddressServiceContract::class);
        $this->setImportStrategy($source);
        $this->refundService = app()->make(RefundService::class);
        $this->replicateStagingService = app()->make(ReplicateStagingService::class);
        $this->noticeService = app()->make(NoticeService::class);
    }

    public function setImportStrategy(PayloadSource|string $source): void
    {
        $this->source = $source instanceof PayloadSource ? $source->value : $source;

        $this->importStrategy = match ($this->source) {
            PayloadSource::DIGISTORE_IPN->value => app()->make(DigistoreIpnStrategy::class),
            PayloadSource::DIGISTORE_API->value => app()->make(DigistoreApiStrategy::class),
            PayloadSource::BUYGOODS_CSV->value => app()->make(BuygoodsCsvStrategy::class),
            PayloadSource::BUYGOODS_IPN->value => app()->make(BuygoodsIpnStrategy::class),
            PayloadSource::CLICKBANK_INS->value => app()->make(ClickbankInsStrategy::class),
            PayloadSource::CLICKBANK_ORDERS_API->value => app()->make(ClickbankApiStrategy::class),
            PayloadSource::CHECKOUTCHAMP->value => app()->make(CheckoutChampStrategy::class),
            PayloadSource::KONNEKTIVE_WEBHOOK->value => app()->make(KonnektiveStrategy::class),
            PayloadSource::COMMITTED_COACHES_WEBHOOK->value => app()->make(CommittedCoachesStrategy::class),
            PayloadSource::AMAZON_SELLER_CENTRAL_API->value => app()->make(AmazonSellerCentralApiStrategy::class),
            PayloadSource::COMMITTED_COACHES_SUPPLEMENT->value => app()->make(
                CommittedCoachesSupplementStrategy::class
            ),
            default => throw new Exception("Unknown payload source: $this->source"),
        };
    }

    public function importFromFile(string $filePath, Account $account): void
    {
        $reader = Reader::createFromPath($filePath, 'r');
        $reader->setHeaderOffset(0);
        $reader->skipEmptyRecords();
        $chunk = 10;
        $chunks = ceil(count($reader) / $chunk);

        for ($i = 0; $i < $chunks; $i++) {
            $statement = (new Statement())->offset($i * $chunk)->limit($chunk);
            $records = $statement->process($reader);

            foreach ($records as $record) {
                $this->import($account, $record);
            }
        }
    }

    public function import(
        Account $account,
        array|string|null $payload = null,
        ?string $payloadSourceRef = null,
        ?string $requestId = null
    ): ?Order {
        if ($this->importStrategy->isTestOrder($payload)) {
            $this->replicateStagingService->replicate($payload);
        }

        try {
            $this->account = $account;
            $this->importStrategy->setAccount($account);
            $this->processOptions($payload);

            $payload = $this->importStrategy->preparePayloadData($payload);
            $payloadModel = $this->savePayload($payload, $payloadSourceRef, $requestId);

            $dto = $this->importStrategy->getImportOrderDto($payload);

            if (!$dto->customer) {
                throw new SkipImportException();
            }

            /** @var Customer $customer */
            $customer = $this->importStrategy->getCustomer($dto);

            $this->currentBillingAddress = $this->saveAddress(
                $customer,
                AddressType::BILLING,
                $dto->address,
                $dto->order->ipAddress
            );
            $this->currentShippingAddress = $this->saveAddress(
                $customer,
                AddressType::SHIPPING,
                $dto->shippingAddress,
                $dto->order->ipAddress
            );

            $affiliate = $this->getAffiliate($dto->affiliate);

            $order = $this->getOrder($dto, $customer, $affiliate);

            if ($dto->offers) {
                foreach ($dto->offers as $dtoOffer) {
                    $this->addOfferToOrder($account, $order, $dtoOffer);
                }
            }

            $this->payloadService->setStatus($payloadModel, PayloadStatus::SUCCESS);

            event(new OrderTotalAmountCalculate($order));

            if ($order->valid([CanBeSentToKonnektive::class])) {
                dispatch(new SendLeadToKonnektive($order));
            }

            event(new OrderSaved($order, $dto->transactionId));
            $order->save();
            $this->setReOrderAt($order);

            if ($dto->utm) {
                $this->importStrategy->saveTrackingUtms($order, $dto->utm);
            }
            $this->importStrategy->completed($order, $payload);

            if ($customer->zerobounce_status = 'valid' && $customer->getMeta(Customer::META_KEY_SITEMANA_TAGS)) {
                event(new EmailValidatedSuccessfully($customer));
            }
        } catch (ImportOrderException $e) {
            if (isset($payloadModel)) {
                $this->payloadService->setStatus($payloadModel, PayloadStatus::FAILED);
                $this->payloadService->savePayloadError($payloadModel, $e);
            }

            if (App::isLocal()) {
                throw $e;
            }

            report($e);
        } catch (ModelValidationException $e) {
            if (isset($payloadModel)) {
                $this->payloadService->setStatus($payloadModel, PayloadStatus::FAILED);
                $this->payloadService->savePayloadError($payloadModel, $e);
            }

            NotificationFacade::route(
                'slack',
                NotificationType::MODEL_VALIDATION->getSlackChannel()
            )->notify(
                new ModelNotSaved(
                    class_basename($e),
                    $e->getMessage(),
                    $e->getModel(),
                    $payloadModel ?? null
                )
            );

            if (App::isLocal()) {
                throw $e;
            }

            report($e);
        } catch (SkipImportException $e) {
            return null;
        } catch (ImportWithoutReportException $e) {
            $payloadModel = $this->importStrategy->getPayload();
            if (isset($payloadModel)) {
                $this->payloadService->setStatus($payloadModel, PayloadStatus::FAILED);
                $this->payloadService->savePayloadError($payloadModel, $e);
            }
        } catch (ASCException $e) {
            $payloadModel = $this->importStrategy->getPayload();
            if (isset($payloadModel)) {
                $this->payloadService->setStatus($payloadModel, PayloadStatus::FAILED);
                $this->payloadService->savePayloadError($payloadModel, $e);
            }
            if (str_contains($e->getMessage(), 'QuotaExceeded') && !$e->getLimit()) {
                throw $e;
            }
        } catch (OfferMissingException|ProductMissingException|AccountMissingException $e) {
            $payloadModel = $this->importStrategy->getPayload();
            if (!isset($payloadModel)) {
                report($e);
            }

            if ($payloadModel->retry_attempts >= 30) {
                $this->payloadService->setStatus($payloadModel, PayloadStatus::FAILED);
                $this->payloadService->savePayloadError($payloadModel, $e);
                report($e);
                return $order ?? null;
            }
            $payloadModel->retry_attempts = $payloadModel->retry_attempts + 1;
            $payloadModel->retry_at = now()->addHours(8);
            $payloadModel->status = PayloadStatus::DELAYED->value;
            $payloadModel->error = [
                'message'   => $e->getMessage(),
                'code'      => !empty($e->getCode()) ? $e->getCode() : 400,
                'backtrace' => $e->getTraceAsString(),
            ];
            $payloadModel->save();
        } catch (Exception $e) {
            $payloadModel = $this->importStrategy->getPayload();
            if (isset($payloadModel)) {
                $this->payloadService->setStatus($payloadModel, PayloadStatus::FAILED);
                $this->payloadService->savePayloadError($payloadModel, $e);
            }

            $this->importStrategy->debug($e);
            report($e);
        } catch (Throwable $e) {
            $payloadModel = $this->importStrategy->getPayload();
            if (isset($payloadModel)) {
                $this->payloadService->setStatus($payloadModel, PayloadStatus::FAILED);
                $this->payloadService->savePayloadError($payloadModel, $e);
            }

            report($e);
        }

        return $order ?? null;
    }

    public function setReOrderAt(Order $order): void
    {
        $order->refresh();

        if (count($order->orderOffers) === 1) {
            foreach ($order->orderOffers as $orderOffer) {
                $orderOffer->re_order_at = $this->getReorderAtDate($order, $orderOffer->offer, $orderOffer->quantity);
                $orderOffer->save();
            }

            return;
        }

        foreach ($order->orderOffers as $orderOffer) {
            $offer = $orderOffer->offer;
            $product = $offer->products()->first();

            $query = $order->orderOffers()
                ->whereHas('offer', function ($query) use ($product) {
                    $query->where('type', OfferType::UPSELL->value)
                        ->whereHas('offerProducts', function ($q) use ($product) {
                            $q->where('product_id', $product->getKey());
                        });
                });

            if (OfferType::INITIAL->is($offer->type) && $query->exists()) {
                $quantity = $offer->quantity * $orderOffer->quantity;

                foreach ($query->get() as $upsellOrderOffer) {
                    $quantity += $upsellOrderOffer->quantity * $upsellOrderOffer->offer->quantity;
                }

                $orderOffer->re_order_at = clone($order->purchased_at)->addDays($quantity * 30);
                $orderOffer->save();
            }
        }
    }

    public function savePayload(array $payload, ?string $payloadSourceRef = null, ?string $requestId = null): Payload
    {
        $payloadSourceRef = $payloadSourceRef ?: $this->importStrategy->getPayloadSourceRef($payload);

        $model = $this->payloadService->getPayload(
            $this->importStrategy->getPayloadTitle(),
            $payload,
            $payloadSourceRef,
            $requestId
        );

        $this->importStrategy->setPayload($model);
        return $model;
    }

    private function processOptions(?array $payload = null): void
    {
        if (!$this->options || !$payload) {
            return;
        }

        if ($this->options->skipAbandoned) {
            $orderDto = $this->importStrategy->prepareOrderDto($payload);
            if ($orderDto->status === OrderStatus::ABANDONED) {
                throw new SkipImportException();
            }
        }
    }

    /**
     * @throws OfferMissingException
     */
    public function addOfferToOrder(Account $account, Order $order, OfferDto $dtoOffer): void
    {
        try {
            $offer = $this->importStrategy->getOffer($account, $dtoOffer);
            $offerIds = $order->orderOffers->fresh()->pluck('offer_id')->toArray();

            if (in_array($offer->id, $offerIds, true)) {
                $costOfGoods = $order->calculateCostOfGoods();
                $order->cost_of_goods = $costOfGoods;
                $order->saveQuietly();
                return;
            }

            $order->orderOffers()->create([
                'offer_id'        => $offer->getKey(),
                'quantity'        => $dtoOffer->quantity,
                'amount'          => $dtoOffer->price ?? $offer->price,
                'discount_amount' => $dtoOffer->discountAmount ?? Money::parse(0, config('money.defaultCurrency')),
            ]);

            if (empty($order->funnel_step)) {
                $order->funnel_step = $offer->getFunnelStep();
            }

            $domainDisabled = in_array($this->source, [
                PayloadSource::COMMITTED_COACHES_WEBHOOK->value,
                PayloadSource::COMMITTED_COACHES_SUPPLEMENT->value,
            ]);

            if (!$domainDisabled
                && $domain = $this->domainProductService->getDomainByOffer($offer, $dtoOffer->vendorName)) {
                $order->domain_id = $domain->getKey();
                $order->orderOffers->fresh()->each(function (OrderOffer $orderOffer) use ($domain) {
                    $orderOffer->domain_id = $domain->getKey();
                    $orderOffer->saveQuietly();
                });
            }

            $order->save();
        } catch (OfferMissingException $e) {
            if (PayloadSource::COMMITTED_COACHES_SUPPLEMENT->is($this->source)) {
                $order->status = OrderStatus::INCOMPLETE;
                $order->save();
            }

            throw $e;
        } catch (ModelValidationException $e) {
        }
    }

    public function getReorderAtDate(Order $order, Offer $offer, int $orderOfferQuantity): ?Carbon
    {
        $product = $offer->products()->first();

        $quantity = $offer->quantity;

        if ($order->parentOrder) {
            $offerWithTheSameProduct = $order->parentOrder
                ->offers()
                ->whereHas('offerProducts', function ($q) use ($product) {
                    $q->where('product_id', $product->getKey());
                })
                ->count();

            if ($offerWithTheSameProduct) {
                $quantity = 0;
                $this->updateReorderAtDate($order->parentOrder, $product);
            }
        }

        if ($order->upsellOrders()->count()) {
            $upsellQuantity = $this->getUpsellQuantity($order, $product);

            if ($upsellQuantity) {
                $quantity += $upsellQuantity;
            }
        }

        if (!$quantity) {
            return null;
        }

        return clone($order->purchased_at)->addDays($quantity * $orderOfferQuantity * 30);
    }

    private function getUpsellQuantity(Order $order, Product $product): int
    {
        foreach ($order->upsellOrders as $upsellOrder) {
            foreach ($upsellOrder->orderOffers as $orderOffer) {
                $hasProduct = $orderOffer->offer->products()->where('product_id', $product->getKey())->exists();
                if ($hasProduct) {
                    return $orderOffer->offer->quantity * $orderOffer->quantity;
                }
            }
        }

        return 0;
    }

    public function updateReorderAtDate(Order $order, Product $product): void
    {
        foreach ($order->orderOffers as $orderOffer) {
            if (!$orderOffer->offer->products()->where('product_id', $product->getKey())->first()) {
                continue;
            }
            $quantity = $this->getUpsellQuantity($order, $product);
            $quantity = $quantity + $orderOffer->offer->quantity * $orderOffer->quantity;
            $reOrderAt = clone($order->purchased_at)->addDays($quantity * 30);
            if  (!$orderOffer || !$orderOffer->re_order_at || $orderOffer->re_order_at->eq($reOrderAt)) {
                $orderOffer->re_order_at = $reOrderAt;
                $orderOffer->save();
            }
        }
    }


    public function getOrder(
        ImportOrderDto $dto,
        Customer $customer,
        ?Affiliate $affiliate = null
    ): Order {
        if (empty($dto->order->sourceRef)) {
            throw new ImportOrderException(
                'Empty source_ref value for order. Payload: ' . $this->importStrategy->getPayload()->id
            );
        }

        $alreadyRefunded = false;
        try {
            $order = $this->importStrategy->getOrder($this->account, $dto->order, $customer);
            if (OrderStatus::REFUNDED->is($order->status)) {
                $alreadyRefunded = true;
            }

            $this->importStrategy->updateOrder($order, $dto);
        } catch (ModelNotFoundException $ex) {
            $order = new Order(
                array_merge(
                    [
                        'account_id'   => $this->account->getKey(),
                        'customer_id'  => $customer->id,
                        'affiliate_id' => $affiliate?->id,
                    ],
                    $dto->order->toArray(true)
                )
            );

            $order->wasRecentlyCreated = true;
            $order->save();
        } catch (DuplicateKeyException $ex) {
            // Try again in case of a race condition.
            $order = Order::where('account_id', $this->account->getKey())
                ->where('source_ref', $dto->order->sourceRef)
                ->firstOrFail();

            $this->importStrategy->updateOrder($order, $dto);
        }

        $this->payloadService->attachModelToPayload($this->importStrategy->getPayload(), $order, true);
        $order->refresh();
        $this->updateOrderStatus($order, $dto->order);

        if ($this->importStrategy->isChargebackStatus($dto)) {
            $order->chargeback_fee = $dto->order->chargebackFee;

            $cb = ChargebackFee::updateOrCreate([
                'order_id' => $order->id,
            ], [
                'amount'               => $dto->order->chargebackFee->absolute(),
                'currency'             => $dto->order->currency,
                'chargeback_at'        => $dto->order->chargebackAt ?: now(),
                'source_chargeback_at' => $dto->order->sourceChargebackAt ?: now(),
                'source_timezone'      => $dto->order->sourceTimezone,
            ]);

            event(new ChargebackFeeSaved($cb, $dto->transactionId));
            $this->payloadService->attachModelToPayload($this->importStrategy->getPayload(), $cb, true);
            $order->save();
        }

        if ($this->importStrategy->isChargebackStatus($dto) && $this->importStrategy->saveRefundOnChargeback()) {
            $rf = $this->refundService->saveChargebackRefund($order, $dto->order);
            event(new RefundSaved($rf, $dto->transactionId));
            $this->payloadService->attachModelToPayload($this->importStrategy->getPayload(), $rf, true);
        }

        if ($this->importStrategy->isRefundStatus($dto) && !$alreadyRefunded) {
            $rf = $this->refundService->saveRefundData($order, $dto->order);
            event(new RefundSaved($rf, $dto->transactionId));
            $this->payloadService->attachModelToPayload($this->importStrategy->getPayload(), $rf, true);
        }

        if ($dto->meta && !empty($dto->meta->data)) {
            foreach ($dto->meta->data as $key => $value) {
                $order->setMeta($key, $value);
            }
        }

        if ($dto->tracking && $dto->tracking->data) {
            $this->saveTrackingData($order, $dto->tracking);
        }

        if ($order->customer_id !== $customer->id) {
            $order->customer_id = $customer->id;
        }

        $order->shipping_address_id = $this->currentShippingAddress
            ? $this->currentShippingAddress->id
            : $customer->shippingAddress?->id;
        $order->billing_address_id = $this->currentBillingAddress
            ? $this->currentBillingAddress->id
            : $customer->billingAddress?->id;
        $order->parent_order_id = $this->importStrategy->getParentOrder($order)?->getKey();
        $order->save();

        return $order;
    }

    public function saveTrackingData(Order $order, TrackingDto $dto): void
    {
        foreach ($dto->data as $key => $value) {
            if (is_null($value) || $value === '') {
                continue;
            }
            $trackingCategory = TrackingCategory::where(['key' => $key])->first();

            Tracking::updateOrCreate([
                'affiliate_id' => $order->affiliate_id ?? null,
                'order_id'     => $order->id,
                'type'         => TrackingType::getType($key),
                'key'          => $key,
            ], [
                'value'                => $value,
                'tracking_category_id' => $trackingCategory?->id,
                'tracked_at'           => $dto->order->purchasedAt ?? Carbon::now(),
            ]);
        }
    }

    public function updateOrderStatus(Order $order, OrderDto $dto): void
    {
        if (!$order->status) {
            $order->status = $dto->status ?: OrderStatus::PENDING_FULFILLMENT->value;
            return;
        }

        if (OrderStatus::CHARGEBACK->is($order->status) && OrderStatus::CANCELED->is($dto->status)) {
            return;
        }

        if (OrderStatus::CANCELED->is($dto->status) && !$this->importStrategy->canBeCanceled($order)) {
            logger('here');
            return;
        }

        if ($dto->status) {
            $order->status = $dto->status;
        }
    }

    public function getCustomer(CustomerDto $dto): Customer
    {
        return DB::transaction(function () use ($dto) {
            $customer = CustomerHelper::getCustomer(
                $dto->email,
                $this->account->getKey(),
                [
                    'first_name' => $dto->firstName,
                    'last_name'  => $dto->lastName,
                    'phone'      => PhoneHelper::formatIntWithCode($dto->phone),
                ]
            );

            $this->payloadService->attachModelToPayload($this->importStrategy->getPayload(), $customer, true);

            return $customer;
        }, 3);
    }

    public function getAffiliate(?AffiliateDto $dto = null): ?Affiliate
    {
        if (!$dto || !$dto->sourceRef) {
            return null;
        }

        return DB::transaction(function () use ($dto) {
            if ($affiliate = $this->getAffiliateBySourceRef($dto)) {
                return $affiliate;
            }

            if ($affiliate = $this->getAffiliateByName($dto)) {
                return $affiliate;
            }

            /** @var Affiliate $affiliate */
            $affiliate = Affiliate::create(['name' => $dto->name]);

            try {
                $affiliate->accounts()->syncWithoutDetaching([
                    $this->account->getKey() => ['affiliate_source_ref' => $dto->sourceRef],
                ]);
            } catch (ValidationException $ex) {
            }

            $this->payloadService->attachModelToPayload($this->importStrategy->getPayload(), $affiliate, true);

            return $affiliate;
        }, 3);
    }

    /**
     * @param  Customer  $customer
     * @param  AddressType  $type
     * @param  ShippingAddressDto|CustomerAddressDto|null  $dto
     * @return void
     * @throws \Throwable
     */
    private function saveAddress(
        Customer $customer,
        AddressType $type,
        ShippingAddressDto|CustomerAddressDto|null $dto,
        ?string $ip
    ): ?Address {
        if (!$dto) {
            return null;
        }

        $addressDto = $this->addressService->fillAddressFields($dto, $ip);

        if (
            $addressDto->postalCode &&
            $addressDto->city &&
            $existingAddress = $this->addressService->checkDuplicatedAddressByPostalCodeAndCity($customer, $addressDto)
        ) {
            if (empty($existingAddress->address) && $addressDto->address) {
                $existingAddress->address = $addressDto->address;
                $existingAddress->save();
            }
            return $existingAddress;
        }

        if (!$addressDto->address && !$addressDto->city) {
            return null;
        }

        $addresses = Address::where([
            'type'        => $type->value,
            'customer_id' => $customer->id,
            'city'        => $addressDto->city,
            'state'       => $addressDto->state,
        ])->get();

        foreach ($addresses as $address) {
            if (Str::lower($address->address) == Str::lower($addressDto->address)) {
                return $address;
            }
        }

        try {
            $payload = $this->importStrategy->getPayload();
            $address = DB::transaction(function () use ($customer, $addressDto, $type) {
                $address = new Address(
                    array_merge(
                        $addressDto->toArray(true),
                        [
                            'customer_id' => $customer->id,
                            'type'        => $type,
                        ]
                    )
                );

                return tap($address)->save();
            }, 3);
            $this->payloadService->attachModelToPayload($payload, $address, true);
            return $address;
        } catch (Exception $e) {
            report(
                new DebugException(
                    $e->getMessage() . '. Payload # ' . $payload->id . ': ' .
                    json_encode($addressDto->toArray(true))
                )
            );

            throw $e;
        }
    }

    private function getAffiliateByName(AffiliateDto $dto): ?Affiliate
    {
        $accountAffiliate = AccountAffiliate::whereHas('account', function ($query) use ($dto) {
            $query->where('source', $this->account->source);
        })->whereHas('affiliate', function ($query) use ($dto) {
            $query->where('name', $dto->name);
        })->first();

        if (!$accountAffiliate) {
            return null;
        }

        $affiliate = Affiliate::find($accountAffiliate->affiliate_id);

        if (!$affiliate) {
            return null;
        }

        try {
            $affiliate->accounts()->syncWithoutDetaching([
                $this->account->getKey() => ['affiliate_source_ref' => $dto->sourceRef],
            ]);
        } catch (ValidationException $ex) {
        }

        return $affiliate;
    }

    private function getAffiliateBySourceRef(AffiliateDto $dto): ?Affiliate
    {
        $accountAffiliate = AccountAffiliate::where([
            'account_id'           => $this->account->getKey(),
            'affiliate_source_ref' => $dto->sourceRef,
        ])->whereHas('affiliate')->first();

        if (!$accountAffiliate) {
            return null;
        }

        $affiliate = Affiliate::find($accountAffiliate->affiliate_id);

        if ($affiliate && $dto->name && $affiliate->name !== $dto->name) {
            $affiliate->name = $dto->name;
            $affiliate->save();
        }

        return $affiliate;
    }

    public function getAccount(array $data, ?Payload $payload = null): Account
    {
        $account = $this->importStrategy->getAccount($data);

        if ($account) {
            return $account;
        }

        $accountSourceRef = $this->importStrategy->getAccountSourceRef($data);

        $msg = $accountSourceRef
            ? "Account '$accountSourceRef' not found in the DB"
            : 'Account source_ref could not be read from the data';

        if ($this->importStrategy->sendMissingAccountNotification($accountSourceRef)) {
            $this->noticeService->sendSlackNotification(
                NotificationType::ACCOUNT_MISSING,
                new AccountMissing($this->source, $accountSourceRef, $payload)
            );
        }

        throw new ImportOrderException($msg);
    }

    public function getPayload(): ?Payload
    {
        return $this->importStrategy->getPayload();
    }

    public function setPayload(Payload $payload): void
    {
        $this->importStrategy->setPayload($payload);
    }

    public function getDataFromOrdersApi(
        Account $account,
        string $orderSourceRef,
        ?OrderStatus $status = null
    ): Collection {
        if (AccountSource::CLICKBANK->is($account->source)) {
            $client = app()->make(ClickbankApi::class);
            $client->setApiKey($account->api_key);

            $this->setImportStrategy(PayloadSource::CLICKBANK_ORDERS_API);

            return $this->importStrategy->getTransactionByStatus($account, $orderSourceRef, $status);
        } elseif (AccountSource::DIGISTORE24->is($account->source)) {
            $client = app()->make(DigistoreApi::class);
            $client->setApiKey($account->api_key);

            $response = $client->getOrders(['search' => ['purchase_id' => trim($orderSourceRef)]], $status);

            if ($status) {
                return collect($response);
            }

            return collect(Arr::get($response, 'data.transaction_list', []));
        }

        return collect();
    }

    public function setAccount(Account $account)
    {
        $this->account = $account;
    }
}
