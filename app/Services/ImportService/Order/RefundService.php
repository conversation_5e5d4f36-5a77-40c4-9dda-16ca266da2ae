<?php

namespace App\Services\ImportService\Order;

use App\Dto\OrderDto;
use App\Models\Order;
use App\Models\Refund;
use Cknow\Money\Money;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class RefundService
{
    public function saveChargebackRefund(Order $order, OrderDto $dto): Refund
    {
        $order->refunded_amount = $dto->totalAmount; // No one gets a partial refund on a chargeback
        $order->refunded_at = $dto->refundedAt ?: $dto->chargebackAt;
        $order->source_refunded_at = $dto->sourceRefundedAt
            ?: $order->refunded_at?->copy()->tz($order->source_timezone);

        try {
            $rfnd = Refund::where(['order_id' => $order->id])->firstOrFail();
        } catch (ModelNotFoundException $ex) {
            $rfnd = Refund::create([
                'order_id' => $order->id,
                'amount'             => $order->refunded_amount,
                'currency'           => $order->currency,
                'refunded_at'        => $order->refunded_at,
                'source_refunded_at' => $order->source_refunded_at,
                'source_timezone'    => $order->source_timezone,
            ]);
            $this->updateOrderAmounts($order, $dto);
        }

        return $rfnd;
    }

    public function saveRefundData(Order $order, OrderDto $dto): Refund
    {
        $order->refunded_at = $dto->refundedAt;
        $order->source_refunded_at = $dto->sourceRefundedAt
            ?: $order->refunded_at->copy()->tz($order->source_timezone);

        try {
            $rfnd = Refund::where([
                'order_id' => $order->id,
                'amount'   => $dto->refundedAmount->isZero() ? $order->total_amount->getAmount() :$dto->refundedAmount->absolute()->getAmount(),
                'currency' => $order->currency,
            ])->firstOrFail();
        } catch (ModelNotFoundException $ex) {
            $rfnd = Refund::create([
                'order_id'           => $order->id,
                'amount'             => $this->getRefundedAmount($order, $dto),
                'currency'           => $order->currency,
                'refunded_at'        => $order->refunded_at,
                'source_refunded_at' => $order->source_refunded_at,
                'source_timezone'    => $order->source_timezone,
            ]);
            $this->updateOrderAmounts($order, $dto);
        }

        $order->refunded_amount = $this->getRefundedAmountByRefund($order);

        if (!$order->refunded_amount->equals($order->total_amount)) {
            $order->status = $order->getOriginal('status');
        }

        $order->save();

        return $rfnd;
    }

    private function getRefundedAmount(Order $order, OrderDto $dto): Money
    {
        $amount =  $dto->refundedAmount->isZero() ? $order->total_amount :$dto->refundedAmount->absolute();

        if (!$order->refunds->count() && !$dto->refundedAmount->greaterThan($order->total_amount)) {
            return $amount;
        }

        if ($dto->refundedAmount->equals($order->total_amount)) {
            return $dto->refundedAmount->subtract($this->getRefundedAmountByRefund($order));
        }

        if ($dto->refundedAmount->greaterThan($order->total_amount)) {
            return $order->total_amount->subtract($this->getRefundedAmountByRefund($order));
        }

        return $amount;
    }

    private function updateOrderAmounts(Order $order, OrderDto $dto): void
    {
        if ($dto->affiliateCommission && !$dto->affiliateCommission->isZero() && $order->affiliate_commission) {
            $affCommission = $order->affiliate_commission->subtract($dto->affiliateCommission->absolute());

            if ($affCommission->isPositive()) {
                $order->affiliate_commission = $affCommission;
            }
        }

        if ($dto->taxAmount && !$dto->taxAmount->isZero() && $order->tax_amount) {
            $taxAmount = $order->tax_amount->subtract($dto->taxAmount->absolute());
            if ($taxAmount->isPositive()) {
                $order->tax_amount = $taxAmount;
            }
        }

        if ($dto->merchantFee && !$dto->merchantFee->isZero() && $order->merchant_fee) {
            $merchantFee = $order->merchant_fee->subtract($dto->merchantFee->absolute());
            if ($merchantFee->isPositive()) {
                $order->merchant_fee = $order->merchant_fee->subtract($dto->merchantFee->absolute());
            }
        }
    }

    public function getRefundedAmountByRefund(Order $order): Money
    {
        $refundedAmount = Money::parse(0);
        $order->load(['refunds']);

        foreach ($order->refunds as $refund) {
            $refundedAmount = $refundedAmount->add($refund->amount);
        }

        return $refundedAmount->greaterThan($order->total_amount) ? $order->total_amount : $refundedAmount;
    }
}
