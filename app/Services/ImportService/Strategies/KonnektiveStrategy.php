<?php

namespace App\Services\ImportService\Strategies;

use App\Dto\OrderDto;
use App\Enums\AccountSource;
use App\Enums\OrderStatus;
use App\Enums\PayloadSource;
use App\Models\Account;
use App\Models\KonnektiveModel;
use App\Models\Order;
use App\Services\ImportService\Exceptions\ProductMissingException;
use App\Services\ImportService\ImportStrategy;
use Cknow\Money\Money;
use Illuminate\Support\Arr;

class KonnektiveStrategy extends KonnektiveBaseStrategy implements ImportStrategy
{
    public function getSource(): string
    {
        return PayloadSource::KONNEKTIVE_WEBHOOK->value;
    }

    public function getAccount(array $data): ?Account
    {
        return Account::where([
            'source'     => AccountSource::KONNEKTIVE->value,
            'source_ref' => $this->getAccountSourceRef($data),
        ])->first();
    }

    /**
     * @param  array|null  $payload
     * @return array
     * @throws ProductMissingException
     */
    public function prepareOfferData(array $payload = null): array
    {
        $data = [];

        for ($i = 1; $i <= 5; $i++) {
            if (!Arr::get($payload, 'product' . $i . '_name') || !Arr::get($payload, 'product' . $i . '_sku')) {
                break;
            }

            $currency = Arr::get($payload, 'currencyCode');
            $product = $this->getProductBySku($payload['product' . $i . '_sku']);

            $data[] = [
                'source_ref'          => Arr::get($payload, 'product' . $i . '_id'),
                'name'                => Arr::get($payload, 'product' . $i . '_name'),
                'quantity'            => Arr::get($payload, 'product' . $i . '_qty'),
                'price'               => !is_null(Arr::get($payload, 'product' . $i . '_price'))
                    ? Money::parse(Arr::get($payload, 'product' . $i . '_price'), $currency, true)
                    : null,
                'currency'            => $currency,
                'productCode'         => $product?->code,
                'konnektiveSourceRef' => Arr::get($payload, 'product' . $i . '_campaignProductId'),
            ];
        }

        return $data;
    }

    public function completed(Order $order, array $data): void
    {
        $order->refresh();

        if (!$order->konnektiveModel && Arr::get($data, 'orderId')) {
            $model = new KonnektiveModel(['source_ref' => Arr::get($data, 'orderId')]);
            $order->konnektiveModel()->save($model);
        }

        if (!$order->customer->konnektiveModel && Arr::get($data, 'customerId')) {
            $model = new KonnektiveModel(['source_ref' => Arr::get($data, 'customerId')]);
            $order->customer->konnektiveModel()->save($model);
        }
    }

    public function isTestOrder(array $payload): bool
    {
        return str_contains($payload['transactionType'] ?? '', 'TEST_');
    }

    public function getTransactionId(array $payload, ?OrderDto $dto = null): ?string
    {
        $transactionId = Arr::get($payload, 'client_transaction_id');

        if (OrderStatus::REFUNDED->is($dto->status)) {
            $transactionId = Arr::get($payload, 'refund_transaction_id');
        }

        return $transactionId;
    }
}
