<?php

namespace App\Services\ImportService\Strategies;

use App\Dto\AffiliateDto;
use App\Dto\CustomerAddressDto;
use App\Dto\CustomerDto;
use App\Dto\ImportOrderDto;
use App\Dto\OfferDto;
use App\Dto\OrderDto;
use App\Dto\ShippingAddressDto;
use App\Enums\NotificationType;
use App\Enums\OfferType;
use App\Enums\OrderStatus;
use App\Mail\DebugImporterPayload;
use App\Models\Account;
use App\Models\KonnektiveModel;
use App\Models\Offer;
use App\Models\Order;
use App\Models\Payload;
use App\Models\Product;
use App\Models\ProductSku;
use App\Notifications\ProductMissing;
use App\Services\ImportService\Exceptions\ProductMissingException;
use App\Services\ImportService\Order\OfferService;
use App\Services\NoticeService;
use App\Services\PayloadService;
use Carbon\Carbon;
use Cknow\Money\Money;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Mail;

abstract class KonnektiveBaseStrategy extends BaseStrategy
{
    protected ?Account $account = null;
    protected PayloadService $payloadService;
    protected NoticeService $noticeService;
    protected OfferService $offerService;
    protected ?Payload $payload;
    protected ?string $payloadTitle = null;
    public const SOURCE_TIMEZONE = 'America/Los_Angeles';

    public function __construct()
    {
        $this->payloadService = app()->make(PayloadService::class);
        $this->noticeService = app()->make(NoticeService::class);
        $this->offerService = app()->make(OfferService::class);
        $this->payloadTitle = $this->getSource();
    }

    abstract public function getSource(): string;

    abstract public function getTransactionId(array $payload, ?OrderDto $dto = null): ?string;

    abstract public function prepareOfferData(array $payload = null): array;

    public function getProductBySku(?string $sku): Product
    {
        if (!$sku) {
            throw new ProductMissingException('Product sku is empty');
        }

        $product = ProductSku::where(['sku' => $sku])->first()?->product;

        if (!$product) {
            $skuArr = explode('-', $sku);

            if (count($skuArr) > 2 && is_numeric(end($skuArr))) {
                array_pop($skuArr);
            }
            $product = ProductSku::where(['sku' => implode('-', $skuArr)])->first()?->product;
        }

        if (!$product) {
            $this->noticeService->sendSlackNotification(
                NotificationType::PRODUCT_MISSING,
                new ProductMissing($this->account, null, $this->payload, $sku)
            );

            throw new ProductMissingException('Product with sku ' . $sku . ' not found');
        }

        return $product;
    }

    public function getImportOrderDto(array $payload = null): ImportOrderDto
    {
        if (!$this->payload) {
            $this->payload = $this->payloadService->getPayload(
                $this->payloadTitle,
                $payload,
                $this->getPayloadSourceRef($payload)
            );
        }

        $payload['account_id'] = $this->getAccount($payload)?->id;
        $affiliateDto = new AffiliateDto($payload, $this->getSource());
        $customerDto = new CustomerDto($payload, $this->getSource());
        $shippingAddressDto = new ShippingAddressDto($payload, $this->getSource());
        $customerAddressDto = new CustomerAddressDto($payload, $this->getSource());
        $orderDto = $this->prepareOrderDto($payload);
        $offers = [];

        foreach ($this->prepareOfferData($payload) as $data) {
            $offers[] = new OfferDto($data);
        }

        return new ImportOrderDto([
            'order'           => $orderDto,
            'customer'        => $customerDto,
            'address'         => $customerAddressDto,
            'affiliate'       => $affiliateDto,
            'shippingAddress' => $shippingAddressDto,
            'offers'          => $offers,
            'transactionId'   => $this->getTransactionId($payload, $orderDto),
        ]);
    }


    public function prepareOrderDto(array $payload, ?array $requestedData = []): OrderDto
    {
        $sourceRef = $this->getPayloadSourceRef($payload);
        $currency = Arr::get($payload, 'currencyCode');

        $data = [
            'recurring'           => false,
            'sourceRef'           => $sourceRef,
            'currency'            => $currency,
            'totalAmount'         => Money::parse(Arr::get($payload, 'orderTotal'), $currency, true)->absolute(),
            'shippingAmount'      => Money::parse(Arr::get($payload, 'totalShipping', 0), $currency, true)->absolute(),
            'shippingCost'        => Money::parse(Arr::get($payload, 'shippingCost', 0), $currency, true)->absolute(),
            'discountAmount'      => Money::parse(Arr::get($payload, 'totalDiscount', 0), $currency),
            'taxAmount'           => Money::parse(Arr::get($payload, 'salesTax', 0), $currency, true)->absolute(),
            'merchantFee'         => Money::parse(0, $currency),
            'affiliateCommission' => Money::parse(0, $currency),
            'paymentMethod'       => Arr::get($payload, 'paySource'),
            'status'              => Order::normalizeStatus(Arr::get($payload, 'orderStatus')),
            'purchasedAt'         => $this->getDate($payload, 'dateCreated'),
            'sourcePurchasedAt'   => $this->getDate($payload, 'dateCreated')->tz(self::SOURCE_TIMEZONE),
            'sourceTimezone'      => self::SOURCE_TIMEZONE,
            'chargebackFee'       => Money::parse('0', $currency),
            'refundedAmount'      => Money::parse('0', $currency),
            'taxRate'             => 0,
            'ipAddress'           => Arr::get($payload, 'ipAddress'),
            'is_test'             => false,
        ];

        if (strcasecmp(Arr::get($payload, 'orderStatus'), OrderStatus::REFUNDED->value) === 0) {
            $data['refundedAmount'] = Money::parse(Arr::get($payload, 'refundAmount'), $currency, true)->absolute();
            $data['refundedAt'] = $this->getDate($payload, 'dateRefunded');
            $data['sourceRefundedAt'] = $this->getDate($payload, 'dateRefunded')->tz(self::SOURCE_TIMEZONE);
        }

        return new OrderDto($data);
    }

    public function getDate(array $payload, string $key): ?Carbon
    {
        $value = Arr::get($payload, $key);

        if ($value) {
            return Carbon::parse($value, self::SOURCE_TIMEZONE)->timezone(config('app.timezone'));
        }

        return null;
    }

    public function setAccount(Account $account): void
    {
        $this->account = $account;
    }

    public function getAccountSourceRef(array $data): ?string
    {
        return Arr::get($data, 'campaignId');
    }

    public function setPayloadTitle(string $title): void
    {
        $this->payloadTitle = $title;
    }

    public function getPayload(): ?Payload
    {
        return $this->payload;
    }

    public function setPayload(Payload $payload)
    {
        $this->payload = $payload;
    }

    public function getPayloadSourceRef(array $payload): ?string
    {
        return Arr::get($payload, 'clientOrderId');
    }

    public function getOffer(Account $account, OfferDto $dtoOffer): Offer
    {
        $price = $dtoOffer->price->divide($dtoOffer->quantity);

        $offer = $this->offerService->getOffer($account, $dtoOffer, $this->payload, !$dtoOffer->productCode);

        if ($offer && !$offer->price->equals($price) && !$offer->orders()->exists()) {
            $offer->price = $dtoOffer->price;
            $offer->save();
        }

        if ($offer) {
            return $offer;
        }

        $product = Product::where(['code' => $dtoOffer->productCode])->first();
        $createOfferDto = new OfferDto(array_merge($dtoOffer->toArray(), [
            'quantity' => 1,
            'price'    => $price,
            'type'     => OfferType::INITIAL->value,
        ]));

        $offer = $this->offerService->createOffer($createOfferDto, $account, $product);
        $this->payloadService->attachModelToPayload($this->payload, $offer, true);

        if (!$offer->konnektiveModel && $dtoOffer->konnektiveSourceRef) {
            $km = new KonnektiveModel(['source_ref' => $dtoOffer->konnektiveSourceRef]);
            $offer->konnektiveModel()->save($km);
        }

        return $offer;
    }

    public function isChargebackStatus(ImportOrderDto $dto): bool
    {
        return false;
    }

    public function isRefundStatus(ImportOrderDto $dto): bool
    {
        return strcasecmp($dto->order->status, OrderStatus::REFUNDED->value) === 0;
    }

    public function isCompletedStatus(ImportOrderDto $dto): bool
    {
        return strcasecmp($dto->order->status, OrderStatus::COMPLETED->value) === 0;
    }

    public function saveRefundOnChargeback(): bool
    {
        return false;
    }

    public function debug(Exception $e)
    {
        if (config('services.konnektive.debug')) {
            $record['exception'] = $e->getMessage();
            $record['exception_code'] = $e->getCode();
            Mail::to(config('mail.debug_to'))->send(
                new DebugImporterPayload($this->payloadTitle, $record)
            );
        }
    }

    public function normalizePayloadData(array $data): array
    {
        return $data;
    }

    public function getTimezone()
    {
        return static::SOURCE_TIMEZONE;
    }
}
