<?php

namespace App\Services\ImportService\Strategies;

use App;
use App\Dto\AffiliateDto;

use App\Dto\CustomerAddressDto;
use App\Dto\CustomerDto;
use App\Dto\ImportOrderDto;
use App\Dto\MetaDto;
use App\Dto\OfferDto;
use App\Dto\OrderDto;
use App\Dto\ShippingAddressDto;
use App\Dto\TrackingDto;
use App\Dto\TrackingUtmDto;
use App\Enums\AccountSource;
use App\Enums\NotificationType;
use App\Enums\OrderStatus;
use App\Enums\PayloadSource;
use App\Models\Account;
use App\Models\Offer;
use App\Models\Order;
use App\Models\Payload;
use App\Models\Country;
use App\Notifications\OfferMissing;
use App\Services\ImportService\Clients\ClickbankApi;
use App\Services\ImportService\Exceptions\ClickbankException;
use App\Services\ImportService\Exceptions\ProductMissingException;
use App\Services\ImportService\ImportStrategy;
use App\Services\ImportService\Order\OfferService;
use App\Services\NoticeService;
use Carbon\Carbon;
use Cknow\Money\Money;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

abstract class ClickbankBaseStrategy extends BaseStrategy implements ImportStrategy
{
    protected ?Account $account = null;
    protected NoticeService $noticeService;
    protected OfferService $offerService;
    protected ClickbankApi $client;
    public const SOURCE_TIMEZONE = 'America/Los_Angeles';

    public function __construct()
    {
        $this->noticeService = app()->make(NoticeService::class);
        $this->offerService = app()->make(OfferService::class);
        $this->client = app()->make(ClickbankApi::class);
        parent::__construct();
    }

    public function getClient(): ClickbankApi
    {
        return $this->client;
    }

    public function getImportOrderDto(array $payload = null): ImportOrderDto
    {
        if (!$this->payload) {
            $this->payload = $this->payloadService->getPayload(
                $this->payloadTitle,
                $payload,
                $this->getPayloadSourceRef($payload)
            );
        }
        $payload['account_id'] = $this->account?->id;
        $affiliateDto = new AffiliateDto([
            'source_ref' => $this->getValue($payload, 'affiliate'),
            'name'       => $this->getValue($payload, 'affiliate'),
        ]);

        return new ImportOrderDto([
            'order'           => $this->prepareOrderDto($payload),
            'address'         => $this->getBillingAddress($payload),
            'shippingAddress' => $this->getShippingAddress($payload),
            'customer'        => $this->getCustomerDto($payload),
            'affiliate'       => $affiliateDto,
            'offers'          => $this->getOffersDto($payload),
            'transactionType' => Arr::get($payload, 'transactionType'),
            'meta'            => new MetaDto($payload, AccountSource::CLICKBANK->value),
            'tracking'        => new TrackingDto($payload, AccountSource::CLICKBANK->value),
            'utm'             => $this->getTrackingUtmDto($payload),
        ]);
    }

    private function getOffersDto(array $data): array
    {
        $items = $this->getItems($data);
        $offers = [];

        foreach ($items as $item) {
            $sourceRef = $this->getItemSourceRef($item);

            if (empty($sourceRef)) {
                if (App::isLocal()) {
                    echo "\n\033[31mERROR: Empty offer source ref in payload {$this->payload->id}\033[0m\n";
                    dump($data);
                } else {
                    $this->noticeService->sendSlackNotification(
                        NotificationType::OFFER_MISSING,
                        new OfferMissing($this->account, $sourceRef, $this->payload)
                    );
                }

                throw new ClickbankException("Could not find source_ref for offer in payload {$this->payload->id}");
            }

            $offers[] = new OfferDto([
                'sourceRef'      => $this->getItemSourceRef($item),
                'name'           => $this->getValue($item, 'productTitle'),
                'quantity'       => Arr::get($item, 'quantity', 1),
                'recurring'      => filter_var(Arr::get($item, 'recurring'), FILTER_VALIDATE_BOOLEAN),
                'price'          => $this->getOfferPrice($data, $item),
                'currency'       => config('money.defaultCurrency'),
                'discountAmount' => Money::parse(Arr::get($item, 'productDiscount', 0), config('money.defaultCurrency')),
            ]);
        }

        return $offers;
    }

    private function getOfferPrice(array $payload, array $item): ?Money
    {
        $currency = config('money.defaultCurrency');
        $quantity = (int)Arr::get($item, "quantity", 1);

        if (Arr::has($item, "productPrice")) {
            $amount = Money::parse(Arr::get($item, 'productPrice', 0), $currency)
                ->absolute()
                ->divide($quantity);
        } else {
            // We have to do some math to get the correct amount from the orders API.
            $amount = Money::parse(Arr::get($item, 'customerAmount', 0), $currency)->absolute();

            if (!$amount->isZero()) {
                $taxAmount = Money::parse(Arr::get($payload, 'totalTaxAmount', 0), $currency)->absolute();
                $shippingAmount = Money::parse(Arr::get($payload, 'totalShippingAmount', 0), $currency)->absolute();
                $taxAndShipping = $taxAmount
                    ->subtract($shippingAmount)
                    ->divide($quantity);
                $amount = $amount->subtract($taxAndShipping);
            }
        }

        return $amount;
    }

    abstract protected function getCustomerDto(array $data): CustomerDto;

    abstract protected function getShippingAddress(array $data): ShippingAddressDto;

    abstract protected function getBillingAddress(array $data): CustomerAddressDto;

    abstract protected function getItems(array $data): array;

    abstract protected function getTotalAffiliateCommission(array $data, ?bool $checkInPayloads = true): Money;

    abstract protected function getOrderRecurring(array $data): bool;

    abstract protected function getVendorVariable(array $data, string $key): ?string;
    abstract public function getTrackingUtmDto(array $data): ?TrackingUtmDto;

    public function setAccount(Account $account): void
    {
        $this->account = $account;
    }

    public function getAccount(array $data): ?Account
    {
        if ($sourceRef = $this->getAccountSourceRef($data)) {
            return Account::bySource(AccountSource::CLICKBANK->value, $sourceRef)
                ->where('enabled', true)
                ->first();
        }

        return null;
    }

    public function getAccountSourceRef(array $data): ?string
    {
        return Arr::get($data, 'vendor');
    }

    public function setPayloadTitle(string $title): void
    {
        $this->payloadTitle = $title;
    }

    public function getPayload(): ?Payload
    {
        return $this->payload;
    }

    /**
     * @throws ProductMissingException
     */
    public function getOffer(Account $account, OfferDto $dtoOffer): Offer
    {
        $offer = $this->offerService->firstOrCreateOffer($account, $dtoOffer, $this->payload);
        $this->payloadService->attachModelToPayload($this->payload, $offer);

        return $offer;
    }

    public function isChargebackStatus(ImportOrderDto $dto): bool
    {
        return $dto->order->status === OrderStatus::CHARGEBACK->value;
    }

    public function isRefundStatus(ImportOrderDto $dto): bool
    {
        return $dto->order->status === OrderStatus::REFUNDED->value;
    }

    public function isCompletedStatus(ImportOrderDto $dto): bool
    {
        return $dto->order->status === OrderStatus::COMPLETED->value;
    }

    public function getParentOrder(Order $order): ?Order
    {
        $parentSourceRef = Arr::get($this->payload->payload, 'upsell.upsellOriginalReceipt');

        if (!$parentSourceRef) {
            $parentSourceRef = $this->getVendorVariable($this->payload->payload, 'cupsellreceipt');
        }

        if (!$parentSourceRef) {
            return null;
        }

        return Order::where(['source_ref' => $parentSourceRef])->first();
    }

    public function debug(\Exception $e)
    {
        return;
    }

    public function prepareOrderDto(array $payload, ?array $requestedData = []): OrderDto
    {
        $sourceRef = $this->getOrderSourceRef($payload);
        $currency = config('money.defaultCurrency');

        $status = $this->getOrderStatus($payload);
        $isTest = str_contains(Arr::get($payload, 'transactionType'), 'TEST');

        $data = [
            'sourceRef'           => $sourceRef,
            'currency'            => $currency,
            'totalAmount'         => Money::parse(Arr::get($payload, 'totalOrderAmount'), $currency, true)->absolute(),
            'shippingAmount'      => Money::parse(
                Arr::get($payload, 'totalShippingAmount', 0),
                $currency,
                true
            )->absolute(),
            'shippingCost'        => Money::parse(0, $currency),
            'discountAmount'      => Money::parse(0, $currency),
            'taxAmount'           => Money::parse(Arr::get($payload, 'totalTaxAmount', 0), $currency, true)->absolute(),
            'merchantFee'         => $this->getMerchantFee($payload, $currency, $status),
            'affiliateCommission' => $this->getTotalAffiliateCommission($payload),
            'paymentMethod'       => $this->getPaymentMethod($payload),
            'status'              => $status,
            'purchasedAt'         => $this->getTransactionTime($payload, $sourceRef),
            'sourcePurchasedAt'   => $this->getTransactionTime($payload, $sourceRef)->tz(self::SOURCE_TIMEZONE),
            'sourceTimezone'      => self::SOURCE_TIMEZONE,
            'rebillsAt'           => $this->getDate($payload, 'lineItemData.nextPaymentDate'),
            'recurring'           => $this->getOrderRecurring($payload),
            'isTest'              => $isTest,
            'chargebackFee'       => Money::parse('0', $currency),
            'refundedAmount'      => Money::parse('0', $currency),
            'taxRate'             => 0,
        ];

        if (Arr::get($payload, 'transactionType') === 'CGBK') {
            $data['chargebackFee'] = Money::parse('35.00', $currency);
            $data['chargebackAt'] = $this->getTransactionTime($payload, $sourceRef);
            $data['sourceChargebackAt'] = $this->getTransactionTime($payload, $sourceRef)->tz(self::SOURCE_TIMEZONE);
        }

        if (in_array(Arr::get($payload, 'transactionType'), ['CGBK', 'RFND'])) {
            $data['refundedAmount'] = Money::parse(Arr::get($payload, 'totalOrderAmount'), $currency, true)->absolute();
            $data['refundedAt'] = $this->getTransactionTime($payload, $sourceRef);
            $data['sourceRefundedAt'] = $this->getTransactionTime($payload, $sourceRef)->tz(self::SOURCE_TIMEZONE);

            // We can't use the transaction time in this payload for the original purchase date because it represents the refund date.
            [$purchasedAt, $sourcePurchasedAt] = $this->getPurchasedAtFromOrderApi($sourceRef);
            $data['purchasedAt'] = $purchasedAt;
            $data['sourcePurchasedAt'] = $sourcePurchasedAt;
        }

        return new OrderDto($data);
    }

    public function getTransactionByStatus(Account $account, string $orderSourceRef, OrderStatus $status): Collection
    {
        $this->client->setApiKey($account->api_key);

        $orders = $this->client->getOrdersByReceipt($orderSourceRef);

        if ($orders->isEmpty()) {
            return collect();
        }

        return $this->filterOrdersListByStatus($orders, $status);
    }

    public function filterOrdersListByStatus(array|Collection $orderData, OrderStatus $status): Collection
    {
        $matchingOrders = collect();

        foreach ($orderData as $order) {
            if ($status->is($this->getOrderStatus($order))) {
                $matchingOrders->push($order);
            }
        }

        return $matchingOrders;
    }

    /**
     * Sometimes the payload is an array with a single object. Sometimes it is just the object.
     * @param  array  $data
     * @return array
     */
    public function normalizePayloadData(array $data): array
    {
        if (!Arr::has($data, 'transactionTime')) {
            $data = head($data);

            if (!Arr::has($data, 'transactionTime')) {
                return $this->normalizePayloadData($data);
            }
        }

        return $data;
    }

    public function getPurchasedAtFromOrderApi(string $sourceRef): array
    {
        // Try to locate the payload in DB before calling API.
        $payloadsBySourceRef = Payload::where('source_ref', $sourceRef)
            ->where('source', PayloadSource::CLICKBANK_ORDERS_API)
            ->get()
            ->map(fn(Payload $payload) => $this->normalizePayloadData($payload->payload));

        $payloadsByOrderRef = Payload::whereNotIn('id', $payloadsBySourceRef->pluck('id'))
            ->where('source', PayloadSource::CLICKBANK_ORDERS_API)
            ->whereHas('orders', fn(Builder $query) => $query->where('source_ref', $sourceRef))
            ->get()
            ->map(fn(Payload $payload) => $this->normalizePayloadData($payload->payload));

        // Use separate queries to optimize execution time on the DB.
        $payloads = $payloadsBySourceRef;

        if ($payloadsByOrderRef->isNotEmpty()) {
            foreach ($payloadsByOrderRef as $payload) {
                $payloads->push($payload);
            }
        }

        if ($payloads->isEmpty()) {
            if (!$this->account->api_key) {
                throw new ClickbankException('Payload could not be retrieved from ClickBank because no API key.');
            }

            $orderData = $this->getTransactionByStatus($this->account, $sourceRef, OrderStatus::COMPLETED)->first();
        } else {
            $orderData = $this->filterOrdersListByStatus($payloads, OrderStatus::COMPLETED)->first();
        }

        if (!$orderData) {
            return [null, null];
        }

        return [
            $this->getTransactionTime($orderData, $sourceRef),
            $this->getTransactionTime($orderData, $sourceRef)->tz(self::SOURCE_TIMEZONE),
        ];
    }

    public function getTransactionTime(array $payload, string $source_ref): Carbon
    {
        $purchasedAt = $this->getDate($payload, 'transactionTime');

        if ($purchasedAt) {
            return $purchasedAt;
        }

        // We need to have an accurate transaction time to save the order.
        throw new ClickbankException('transactionTime not found in the ClickBank payload for order ' . $source_ref);
    }

    public function getDate(array $payload, string $key): ?Carbon
    {
        $value = $this->getValue($payload, $key);

        if ($value) {
            return Carbon::parse($value, self::SOURCE_TIMEZONE)->timezone(config('app.timezone'));
        }

        return null;
    }

    private function getMerchantFee(array $payload, string $currency, string $status): Money
    {
        if ($status === OrderStatus::ABANDONED->value) {
            return Money::parse(0);
        }

        if (Arr::get($payload, 'totalAccountAmount')) {
            $totalAmount = Money::parse(Arr::get($payload, 'totalOrderAmount', true), $currency, true)->absolute();
            $merchantFee = $totalAmount
                ->subtract(Money::parse(Arr::get($payload, 'totalTaxAmount'), $currency)->absolute())
                ->subtract($this->getTotalAffiliateCommission($payload))
                ->subtract(Money::parse($payload['totalAccountAmount'], $currency)->absolute());

            if ($merchantFee->isPositive()) {
                return $merchantFee;
            }
        }

        return Money::parse(Arr::get($payload, 'totalOrderAmount'), $currency, true)
            ->absolute()
            ->multiply(7.5 / 100)
            ->add(Money::parse('1.00'));
    }

    protected function getValue(?array $data, string $key)
    {
        $value = Arr::get($data, $key);

        if ($value === '' || $value === 'N/A') {
            return null;
        }

        if (is_array($value) && !empty($value['@nil'])) {
            return null;
        }

        return $value;
    }

    protected function getPaymentMethod(array $data): ?string
    {
        if ($pm = $this->getValue($data, 'paytmentMethod')) {
            return $pm;
        }

        return $this->getValue($data, 'paymentMethod');
    }

    public function getPayloadSourceRef(array $payload): ?string
    {
        return $this->getOrderSourceRef($payload);
    }

    protected function getOrderSourceRef(array $data): ?string
    {
        $sourceRef = data_get($data, 'receipt');

        if (empty($sourceRef) || $sourceRef === 'N/A' || $sourceRef === 'N\\/A') {
            // Abandoned carts don't have a source_ref, but we need one for the order to be saved.
            return OrderStatus::ABANDONED->value . '_' . md5(json_encode($data));
        }

        return $sourceRef;
    }

    public function getOrderStatus(array $data): ?string
    {
        if (Arr::has(Order::STATUSES, strtoupper(Arr::get($data, 'transactionType')))) {
            return Order::normalizeStatus(Arr::get($data, 'transactionType'));
        }

        // Try pulling from the line item data.
        $items = $this->getItems($data);

        $first_item = head($items);

        $status = $this->getValue($first_item, 'status');

        if (str_starts_with($status, 'cgbk')) {
            return OrderStatus::CHARGEBACK->value;
        }

        return Order::normalizeStatus($status);
    }

    protected function getCountryCode(?string $country): ?string
    {
        if ($country && strlen($country) > 2) {
            return Country::byName($country)->value('iso_3166_2');
        }

        return $country;
    }

    protected function getItemSourceRef(array $data): ?string
    {
        $source_ref = Arr::get($data, 'itemNo');

        if (!empty($source_ref)) {
            return $source_ref;
        }

        // When there is a chargeback, the item number is sometimes moved to the vendor variable "cbitems".
        return $this->getVendorVariable($data, 'cbitems');
    }

    public function setPayload(Payload $payload): void
    {
        $this->payload = $payload;
    }


    public function saveRefundOnChargeback(): bool
    {
        return true;
    }

    public function completed(Order $order, array $data): void
    {
    }

    public function getTimezone()
    {
        return static::SOURCE_TIMEZONE;
    }

    public function isTestOrder(array $payload): bool
    {
        return str_contains($payload['transactionType'] ?? '', 'TEST_');
    }

    public function preparePayloadData(array $payload): array
    {
        unset($payload['attemptCount']);
        return $payload;
    }
}
