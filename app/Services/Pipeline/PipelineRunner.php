<?php

namespace App\Services\Pipeline;

use Illuminate\Pipeline\Pipeline;

abstract class PipelineRunner
{
    private Pipeline $pipeline;

    /**
     * PipelineRunner constructor.
     * @param  Pipeline  $pipeline
     */
    public function __construct(Pipeline $pipeline)
    {
        $this->pipeline = $pipeline;
    }

    /**
     * Running passable through pipes
     *
     * @return mixed
     */
    public function run()
    {
        return $this->pipeline
            ->through($this->getPipes())
            ->send($this->getPassable())
            ->thenReturn();
    }

    /**
     * Return pipes through which passable will be carried.
     *
     * @return array
     */
    abstract protected function getPipes(): array;

    /**
     * Returns passable which should be carried through pipes.
     *
     * @return mixed
     */
    abstract protected function getPassable();
}
