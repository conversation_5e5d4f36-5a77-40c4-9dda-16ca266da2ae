<?php
namespace App\Services\Google;


use App\Enums\ServiceCredentialType;
use App\Exceptions\GoogleException;
use App\Models\ServiceCredential;
use Carbon\Carbon;
use Exception;
use Google\Client;
use Google\Service\AnalyticsReporting;
use Illuminate\Support\Arr;
use function Sentry\captureException;

/**
 * Class GoogleAuthClient
 * @package App\Infrastructure\Services\Google
 */
class GoogleAuthClient
{
    protected array $config;
    protected string $redirectUrl;
    protected ?Client $googleClient;

    public function __construct()
    {
        $this->redirectUrl = route('google-oauth');
        $this->googleClient = new Client();
        $this->googleClient->setAuthConfig(base_path(config('services.google.credential_path')));
        $this->googleClient->addScope([
            \Google_Service_TagManager::TAGMANAGER_EDIT_CONTAINERS,
            \Google_Service_TagManager::TAGMANAGER_EDIT_CONTAINERVERSIONS,
            \Google_Service_TagManager::TAGMANAGER_PUBLISH,
            AnalyticsReporting::ANALYTICS_READONLY
        ]);
        $this->googleClient->setAccessType('offline');
    }

    /**
     * @return string
     */
    public function getAuthLink()
    {
        $this->googleClient->setRedirectUri($this->redirectUrl);
        $this->googleClient->setApprovalPrompt('force');

        return $this->googleClient->createAuthUrl();
    }

    /**
     * @param string $code
     * @return void
     * @throws GoogleException
     */
    public function saveTokenFromCode(string $code): void
    {
        $this->googleClient->setRedirectUri($this->redirectUrl);
        $this->googleClient->setApprovalPrompt('force');

        $response = $this->googleClient->fetchAccessTokenWithAuthCode($code);

        if (isset($response['error'])) {
            throw new GoogleException(
                'Unable get auth token. Error: ' .
                $response['error'] . '. Error description: ' . $response['error_description']
            );
        }

        $expiresAt = Carbon::now()->subMinutes(5)->addSeconds(Arr::get($response, 'expires_in'));

        ServiceCredential::create([
            'service' => ServiceCredentialType::GOOGLE->value,
            'credentials' => $response,
            'refresh_token' => Arr::get($response, 'refresh_token'),
            'expires_at' => $expiresAt
        ]);
    }

    /**
     * @return array|void
     */
    public function fetchToken(): array|bool
    {
        try {
            /** @var ServiceCredential $googleCredential */
            $googleCredential = ServiceCredential::where(['service' => ServiceCredentialType::GOOGLE->value])->first();

            if (!$googleCredential) {
                return false;
            }

            if (is_array($googleCredential->credentials) && !count($googleCredential->credentials)) {
                return false;
            }

            if (Carbon::now()->gt(Carbon::createFromFormat('Y-m-d H:i:s', $googleCredential->expires_at))) {
                $this->refreshToken($googleCredential, $googleCredential->refresh_token);
            }

            return $googleCredential->credentials;
        } catch (Exception $ex) {
            captureException($ex);
            return false;
        }
    }

    private function refreshToken(ServiceCredential $googleCredential, string $refreshToken): void
    {
        $res = $this->googleClient->refreshToken($refreshToken);

        if (isset($res['error'])) {
            throw new GoogleException(
                'Unable refresh auth token. Error: ' .
                $res['error'] . '. Error description: ' . $res['error_description']
            );
        }

        $expiresAt = Carbon::now()->subMinutes(5)->addSeconds(Arr::get($res, 'expires_in'));

        $googleCredential->credentials = $res;
        $googleCredential->expires_at = $expiresAt;
        $googleCredential->save();
    }

    protected function setToken(): void
    {
        $token = $this->fetchToken();

        if (!$token) {
            throw new GoogleException('Access token is empty');
        }

        $this->googleClient->setAccessToken($token);
    }

    private function getClient(): Client
    {
        return $this->googleClient;
    }
}
