<?php

namespace App\Services\AmazonSellerCentral;

use App\Enums\ServiceCredentialType;
use App\Exceptions\ASCException;
use App\Models\ServiceCredential;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Crypt;


use function Sentry\captureException;

class ASCAuthClient
{
    protected array $config;

    public function __construct()
    {
        $this->config = array_merge([
            'url' => 'https://api.amazon.com/auth/o2/token',
        ], config('services.amazon_seller_central'));
    }

    /**
     * @return string|bool|null
     */
    public function fetchToken(): string|bool|null
    {
        try {
            /** @var ServiceCredential $credential */
            $credential = ServiceCredential::firstOrCreate(['service' => ServiceCredentialType::AMAZON_SELLER_CENTRAL->value]);

            if (!$credential) {
                return false;
            }

            if (!$credential->credentials || Carbon::now()->gt($credential->expires_at)) {
                $this->refreshToken($credential);
            }

            return $credential->credentials; // Access token
        } catch (Exception $ex) {
            report($ex);

            return false;
        }
    }

    private function refreshToken(ServiceCredential $credential): void
    {
        $url = parse_url($this->config['url']);
        $res = Http::withHeaders([
            'Host'         => $url['host'],
            'Content-Type' => 'application/x-www-form-urlencoded;charset=UTF-8',
        ])
            ->post(
                $this->config['url'] . '?' . http_build_query([
                    'grant_type'    => 'refresh_token',
                    'refresh_token' => $this->config['refresh_token'],
                    'client_id'     => $this->config['client_id'],
                    'client_secret' => $this->config['client_secret'],
                ])
            )->json();

        if (!isset($res['access_token'])) {
            throw new ASCException(
                'Unable refresh auth token. Error: ' .
                json_encode($res)
            );
        }

        $credential->refresh_token = Arr::get($res, 'refresh_token');
        $credential->credentials = Arr::get($res, 'access_token');
        $credential->expires_at = Carbon::now()->subMinutes(5)->addSeconds(Arr::get($res, 'expires_in'));
        $credential->save();
    }
}
