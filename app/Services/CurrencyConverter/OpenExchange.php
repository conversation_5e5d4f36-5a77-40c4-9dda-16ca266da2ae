<?php

namespace App\Services\CurrencyConverter;

use App;
use App\Exceptions\CurrencyConversionException;
use Cache;
use Carbon\Carbon;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Collection;
use Cknow\Money\Money;

/**
 * OpenExchange currency conversion class.
 */
class OpenExchange implements CurrencyConverterContract
{
    /**
     * List of known rates.
     * @var Collection
     */
    public Collection $rates;

    /**
     * List of known currencies.
     * @var Collection
     */
    public Collection $currencies;

    /**
     * How soon currencies list needs to be refreshed.
     * @var int
     */
    public int $currenciesExpireDays = 30;

    /**
     * How soon rates need to be refreshed.
     * @var int
     */
    public int $ratesExpireHours = 1;

    /**
     * Max hours a refresh is allowed to fail.
     */
    public const RATES_HARD_EXPIRE_HOURS = 24;

    public Client $client;

    public function __construct(int $rates_expire_hours = null, int $currencies_expire_days = null)
    {
        $this->ratesExpireHours = $rates_expire_hours ?: config('services.open_exchange.rates_expire_hours');
        $this->currenciesExpireDays = $currencies_expire_days ?: config(
            'services.open_exchange.currencies_expire_days'
        );
        $this->rates = new Collection();
        $this->currencies = new Collection();

        if (!config('services.open_exchange.app_id')) {
            throw new CurrencyConversionException('Cannot convert currencies without app ID!');
        }

        $this->client = new Client([
            'base_uri' => 'https://openexchangerates.org/api/',
            'query'    => [
                'app_id' => config('services.open_exchange.app_id'),
                'base'   => config('money.defaultCurrency'),
            ],
        ]);
    }

    /**
     * Convert to another currency.
     * @param  Money  $from  Value to convert.
     * @param  string  $to  Currency to convert to.
     * @param  Carbon|null  $date  The date of the historical exchange rate.
     * @return Money
     * @throws CurrencyConversionException
     * @throws GuzzleException
     */
    public function convert(Money $from, string $to, ?Carbon $date = null): Money
    {
        if ($date) {
            $rates = $this->getHistoricalRates($date);
        } elseif ($this->rates->isEmpty()) {
            $rates = $this->importConversions();
        }

        // TODO Use convert API call instead of manually calculating.
        $to = strtoupper($to);

        if (!isset($rates[$to])) {
            throw new CurrencyConversionException("Unsupported TO currency '$to'");
        }

        if (!isset($rates[$from->getCurrency()->getCode()])) {
            throw new CurrencyConversionException("Unsupported FROM currency '{$from->getCurrency()->getCode()}'");
        }

        return Money::parse($from->multiply($rates[$to]), $to);
    }

    /**
     * Convert to base currency.
     * @param  Money  $from  Value to convert.
     * @param  Carbon|null  $date  The date of the historical exchange rate.
     * @return Money
     * @throws CurrencyConversionException
     * @throws GuzzleException
     */
    public function convertToBase(Money $from, ?Carbon $date = null): Money
    {
        if ($date && !$date->isToday()) {
            $rates = $this->getHistoricalRates($date);
        } elseif ($this->rates->isEmpty()) {
            $rates = $this->importConversions();
        }

        if (!isset($rates[$from->getCurrency()->getCode()])) {
            throw new CurrencyConversionException("Unsupported FROM currency '{$from->getCurrency()->getCode()}'");
        }

        return Money::parse(
            $from->divide($rates[$from->getCurrency()->getCode()])->getAmount(),
            config('money.defaultCurrency')
        );
    }

    /**
     * Get historical exchange rates.
     * @param  Carbon  $date  The date of the historical exchange rate.
     * @return Collection
     * @throws CurrencyConversionException
     * @throws GuzzleException
     */
    public function getHistoricalRates(Carbon $date): Collection
    {
        $cacheKey = 'open_exchange.historical.rates.'.$date->toDateString();

        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            $response = $this->client->get('historical/'.$date->toDateString().'.json');
            $rates = json_decode($response->getBody()->getContents(), true);

            if (empty($rates['error'])) {
                $historicalRates = collect($rates['rates']);

                Cache::forever($cacheKey, $historicalRates);

                return $historicalRates;
            } else {
                throw new CurrencyConversionException(
                    "Error importing OpenExchange historical conversion rates ($rates[message]): $rates[description]",
                    $rates['status']
                );
            }
        } catch (CurrencyConversionException $e) {
            throw $e;
        } catch (Exception $e) {
            throw new CurrencyConversionException(
                'Error requesting OpenExchange historical conversions: '.$e->getMessage(),
                $e->getCode(),
                $e
            );
        }
    }

    /**
     * @return Collection
     * @throws CurrencyConversionException
     * @throws GuzzleException
     * @throws Exception
     */
    public function importConversions(): Collection
    {
        if ($this->rates->isEmpty() && Cache::has('open_exchange.rates')) {
            $cache = Cache::get('open_exchange.rates');

            if (Carbon::parse(data_get($cache, 'created_at'))->diffInHours() > $this->ratesExpireHours) {
                Cache::forget('open_exchange.rates');
            } else {
                return $this->rates = $cache['rates'];
            }
        }

        $this->getCurrencies();

        if ($this->rates->isNotEmpty()) {
            return $this->rates;
        }

        try {
            $response = $this->client->get('latest.json');
            $rates = json_decode($response->getBody()->getContents(), true);

            if (empty($rates['error'])) {
                $this->rates = collect($rates['rates']);

                Cache::forever('open_exchange.rates', [
                    'created_at' => now(),
                    'rates'      => $this->rates,
                ]);

                return $this->rates;
            } else {
                throw new CurrencyConversionException(
                    "Error importing OpenExchange conversion rates ($rates[message]): $rates[description]",
                    $rates['status']
                );
            }
        } catch (CurrencyConversionException $e) {
            throw $e;
        } catch (Exception $e) {
            throw new CurrencyConversionException(
                'Error requesting OpenExchange conversions: '.$e->getMessage(),
                $e->getCode(),
                $e
            );
        } finally {
            /** @var Collection $rates */
            if ($cache = Cache::get('open_exchange.rates')) {
                if (
                    !App::isLocal() &&
                    Carbon::parse(data_get($cache, 'created_at'))->diffInHours() > static::RATES_HARD_EXPIRE_HOURS
                ) {
                    throw new CurrencyConversionException(
                        "Rates are too old to use. Last refreshed {$cache['created_at']->diffInHours()} hours ago."
                    );
                }

                return $this->rates = $cache['rates'];
            }
        }
    }

    /**
     * Clear rate and currency cache.
     */
    public function clearCache()
    {
        Cache::forget('open_exchange.rates');
        Cache::forget('open_exchange.currencies');
    }

    /**
     * Get currencies.
     *
     * @return Collection
     * @throws Exception|GuzzleException
     */
    public function getCurrencies(): Collection
    {
        if ($this->currencies->isNotEmpty()) {
            return $this->currencies;
        }

        if (Cache::has('open_exchange.currencies')) {
            $cache = Cache::get('open_exchange.currencies');

            if (Carbon::parse(data_get($cache, 'created_at'))->diffInDays() > $this->currenciesExpireDays) {
                Cache::forget('open_exchange.currencies');
            } else {
                return $this->currencies = $cache['currencies'];
            }
        }

        try {
            $response = $this->client->get('currencies.json');
            $currencies = collect(json_decode($response->getBody()->getContents(), true));

            if ($currencies->isNotEmpty()) {
                // Cache forever by date so in the event there is an access issue we can still use last known currency.
                Cache::forever('open_exchange.currencies', [
                    'created_at' => now(),
                    'currencies' => $currencies,
                ]);
            }

            return $this->currencies;
        } catch (Exception $e) {
            if ($currencies = Cache::get('open_exchange.currencies')) {
                /** @var Collection $currencies */
                return $this->currencies = $currencies;
            }

            throw new CurrencyConversionException(
                'Error requesting OpenExchange currencies: '.$e->getMessage(),
                $e->getCode(),
                $e
            );
        }
    }
}
