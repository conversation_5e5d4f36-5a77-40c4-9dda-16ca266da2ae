<?php

namespace App\Services\FalsePositiveScannerReporters;

use App\Facades\Netcraft;
use App\Mail\McAfeeFalsePositiveReport;
use App\Models\Domain;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;

class McAfeeReporter implements FalsePositiveScannerReporter
{
    protected string $reportEmail;

    public function __construct()
    {
        $this->reportEmail = config('services.mcafee.false_positive_report_email');
    }

    public function isEnabled(): bool
    {
        return config('services.mcafee.enabled');
    }

    public function report(Domain $domain, array $report = null, int $daysFlagged = 0)
    {
        Mail::queue(
            new McAfeeFalsePositiveReport(config('services.mcafee.false_positive_report_email'), [
                'domain'          => $domain->domain,
                'daysFlagged'     => $daysFlagged,
                'formattedReport' => json_encode($report, JSON_PRETTY_PRINT),
                'rawReport'       => serialize($report),
            ])
        );
    }
}