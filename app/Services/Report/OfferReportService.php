<?php

namespace App\Services\Report;

use App\Enums\AccountSource;
use App\Enums\OrderStatus;
use App\Helpers\Query;
use App\Models\Account;
use App\Models\MarketplaceOfferReport;
use App\Models\Offer;
use App\Models\OfferReport;
use App\Services\Report\Exceptions\OfferReportException;
use App\Services\Report\Strategies\BuygoodsOfferReportStrategy;
use App\Services\Report\Strategies\ClickbankOfferReportStrategy;
use App\Services\Report\Strategies\CommonOfferReportStrategy;
use App\Services\Report\Strategies\DigistoreOfferReportStrategy;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class OfferReportService
{
    private OfferReportStrategy $strategy;

    public function __construct(?AccountSource $source = null)
    {
        $this->strategy = match ($source?->value) {
            AccountSource::BUYGOODS->value => app()->make(BuygoodsOfferReportStrategy::class),
            AccountSource::CLICKBANK->value => app()->make(ClickbankOfferReportStrategy::class),
            AccountSource::DIGISTORE24->value => app()->make(DigistoreOfferReportStrategy::class),
            default => app()->make(CommonOfferReportStrategy::class),
        };
    }

    public function saveReport(Offer $offer, Carbon $date): Model
    {
        $report = $this->firstOrNewReport($offer, $date);
        return $this->strategy->updateReport($report, $date);
    }

    public function offersQuery(Account $account, Carbon $reportDate): Builder|HasMany
    {
        return Offer::on(Query::maybeReadConnection())
            ->where('account_id', $account->id)
            ->where('enabled', true)
            ->whereHas('orders', function (Builder $query) use ($reportDate) {
                $query
                    ->where('is_test', false)
                    ->whereIn('status', [
                        OrderStatus::COMPLETED,
                        OrderStatus::REFUNDED,
                        OrderStatus::CHARGEBACK,
                    ])
                    ->whereBetween('purchased_at', [
                        $reportDate->copy()->startOfDay(),
                        $reportDate->copy()->endOfDay(),
                    ]);
            });
    }

    public function firstOrNewReport(Offer $offer, Carbon $reportDate): MarketplaceOfferReport
    {
        return $this->strategy->getQuery()
            ->where('offer_id', $offer->id)
            ->where('starts_at', '>=', $reportDate->copy()->startOfDay())
            ->where('ends_at', '<=', $reportDate->copy()->endOfDay())
            ->firstOr(fn() => $this->strategy->newReport()->fill([
                'offer_id'  => $offer->id,
                'starts_at' => $reportDate->copy()->startOfDay(),
                'ends_at'   => $reportDate->copy()->endOfDay(),
            ]));
    }

    public function saveOfferReport(Offer $offer, Carbon $date): Model
    {
        $report = $this->firstOrNewOfferReport($offer, $date);
        return $this->strategy->updateOfferReport($report, $date);
    }

    /**
     * @throws OfferReportException
     */
    public function firstOrNewOfferReport(Offer $offer, Carbon $reportDate): OfferReport
    {
        $report = OfferReport::where('offer_id', $offer->id)
            ->where('starts_at', '>=', $reportDate->copy()->startOfDay())
            ->where('ends_at', '<=', $reportDate->copy()->endOfDay())
            ->firstOr(fn() => (new OfferReport())->fill([
                'account_id' => $offer->account_id,
                'offer_id'   => $offer->id,
                'starts_at'  => $reportDate->copy()->startOfDay(),
                'ends_at'    => $reportDate->copy()->endOfDay(),
            ]));
        $report->save();

        if (!$this->strategy->hasRelatedModel() || $report->model) {
            return $report;
        }

        $relatedModel = $this->getRelatedModel($offer, $reportDate);
        $relatedModel?->offerReport()->save($report);

        return $report;
    }

    public function getRelatedModel(Offer $offer, Carbon $date): ?Model
    {
        $relatedModel = $this->strategy->getQuery()
            ->where('offer_id', $offer->id)
            ->where('starts_at', '>=', $date->copy()->startOfDay())
            ->where('ends_at', '<=', $date->copy()->endOfDay())
            ->first();

        if (!$relatedModel) {
            $relatedModel = $this->saveReport($offer, $date);
        }

        return $relatedModel;
    }

    public function getStrategy(): OfferReportStrategy
    {
        return $this->strategy;
    }
}
