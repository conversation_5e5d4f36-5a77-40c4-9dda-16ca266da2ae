<?php

namespace App\Services\Optizmo;

use App\Enums\PayloadSource;
use App\Enums\PayloadStatus;
use App\Services\PayloadService;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Http;

class OptizmoService implements OptizmoContract
{
    protected Client $client;
    protected ?string $apiKey;

    public function __construct(private PayloadService $payloadService)
    {
        $this->client = new Client(['base_uri' => 'https://collect.optoutsystem.com/']);
        $this->apiKey = config('services.optizmo.api_key');
    }

    public function collectOptOut(string $email, string $listId)
    {
        $endpoint = 'https://collect.optoutsystem.com/client-optout/collect';
        $payload = [
            'email' => $email,
            'optoutListId' => $listId,
        ];

        $payloadModel = $this->payloadService->getPayload(PayloadSource::OPTIZMO_OPT_OUT, $payload);

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
        ])->post($endpoint, $payload);

        $res = json_decode($response->getBody()->getContents(), true);
        $this->payloadService->savePayloadResponse($payloadModel, $res);
        $this->payloadService->setStatus($payloadModel, PayloadStatus::SUCCESS);

        return $res;
    }
}
