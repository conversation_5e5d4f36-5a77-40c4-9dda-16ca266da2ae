<?php

namespace App\Services\MalwareScanners;

use App\Facades\Netcraft;
use App\Mail\MalwareNotification;
use App\Models\Domain;
use App\Notifications\VirusTotalQuota;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;

class VirusTotalService extends MalwareScannerService
{
    public const HARMLESS_STATUS = 'harmless';
    public const UNDETECTED_STATUS = 'undetected';
    public const SUSPICIOUS_STATUS = 'suspicious';
    public const MALICIOUS_STATUS = 'malicious';

    protected string $apiKey;

    public function __construct()
    {
        $this->apiKey = config('services.virus_total.api_key');
    }

    public function getName(): string
    {
        return 'Total Virus';
    }

    /**
     * @throws Exception
     */
    public function scan(Domain $domain): array
    {
        $response = Http::withHeader('x-apikey', $this->apiKey)
            ->get("https://www.virustotal.com/api/v3/domains/{$domain->domain}");

        $report = $response->json();

        if (!isset($report['data'])) {
            $error = empty($report['error']) ? json_encode($report) : json_encode($report['error']);
            $errorMessage = empty($error) ? 'No response data was provided by virus total API' : $error;

            $this->notification($report);

            throw new Exception("Virus total encountered error scanning {$domain->domain}: {$errorMessage}");
        }

        $result = [];
        $failedChecks = array_filter(
            $report['data']['attributes']['last_analysis_results'],
            fn($item) => $this->isMalwareReported($item['category'])
        );

        foreach ($failedChecks as $check) {
            $result[$check['engine_name']] = $report;
        }

        return $result;
    }

    public function isBlacklisted(array $report): bool
    {
        $statuses = array_get_list($report, 'data.attributes.last_analysis_results.*.category');

        return in_array(self::SUSPICIOUS_STATUS, $statuses) || in_array(self::MALICIOUS_STATUS, $statuses);
    }

    public function isEnabled(): bool
    {
        return config('services.virus_total.enabled');
    }

    protected function isMalwareReported(string $category): bool
    {
        return in_array($category, [self::MALICIOUS_STATUS, self::SUSPICIOUS_STATUS]);
    }

    private function notification(?array $report): void
    {
        if (isset($report['error']['code']) && $report['error']['code'] == 'QuotaExceededError'
            || isset($report['code']) && $report['code'] == 'QuotaExceededError') {
                $cacheKey = 'notification/VirusTotalQuota';
                if (!Cache::get($cacheKey)) {
                    Cache::put($cacheKey, true, now()->endOfDay());
                    try {
                        Notification::route('slack', config('services.slack.notifications.channels.alerts'))
                            ->notify(new VirusTotalQuota());
                    } catch (Exception) {
                        Cache::delete($cacheKey);
                    }
                }
            }
    }
}
