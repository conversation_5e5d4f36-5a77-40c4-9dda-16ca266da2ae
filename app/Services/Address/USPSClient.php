<?php

namespace App\Services\Address;

use App\Enums\ServiceCredentialType;
use App\Models\ServiceCredential;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

class USPSClient
{
    public ?string $clientId = null;
    public ?string $secretId = null;
    public string $apiUrl = 'https://api.usps.com';
    public ?array $token = null;

    public function __construct()
    {
        $this->clientId = config('services.usps.client');
        $this->secretId = config('services.usps.secret');
    }

    public function fetchAccessToken(): void
    {
        $data = [
            "grant_type"    => "client_credentials",
            "client_id"     => $this->clientId,
            "client_secret" => $this->secretId,
            "scope"         => 'addresses',
        ];

        $response = Http::withHeaders(['Content-Type' => 'application/json'])
            ->post(
                "$this->apiUrl/oauth2/v3/token",
                $data
            );

        $this->token = $response->json();

        $expiresAt = Carbon::now()->subMinutes(5)->addSeconds(Arr::get($this->token, 'expires_in'));
        $credentials = new ServiceCredential([
            'service'     => ServiceCredentialType::USPS->value,
            'credentials' => $this->token,
            'expires_at'  => $expiresAt,
        ]);
        $credentials->save();
    }

    public function getZipCode(string $address, string $city, string $state)
    {
        if (!$this->token) {
            $this->getAccessToken();
        }

        $query = http_build_query(['streetAddress' => $address, 'city' => $city, 'state' => $state]);

        return $this->makeRequest('get', "addresses/v3/zipcode?$query");
    }

    public function getCityState(string $postalCode)
    {
        if (!$this->token) {
            $this->getAccessToken();
        }

        $query = http_build_query(['ZIPCode' => $postalCode]);

        return $this->makeRequest('get', "addresses/v3/city-state?$query");
    }

    private function makeRequest(string $method, string $path)
    {
        $response = Http::withHeaders([
            'Content-Type'  => 'application/json',
            'Authorization' => 'Bearer ' . $this->token['access_token'],
        ])->$method(
            "$this->apiUrl/$path"
        );

        $res = $response->json();

        if (Arr::get($res, 'error')) {
            throw new USPSClientException(Arr::get($res, 'error.message'), Arr::get($res, 'error.code'));
        }

        return $res;
    }

    public function getAccessToken()
    {
        $credentials = ServiceCredential::query()
            ->where('service', ServiceCredentialType::USPS->value)
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$credentials) {
            $this->fetchAccessToken();

            return;
        }

        if (Carbon::now()->gt($credentials->expires_at)) {
            ServiceCredential::where('service', ServiceCredentialType::USPS->value)->delete();
            $this->fetchAccessToken();

            return;
        }

        $this->token = $credentials->credentials;
    }
}
