<?php

namespace App\Jobs;

use App\Dto\HelloPackage\TrackingInfo;
use App\Dto\HelloPackage\TrackingUpdate;
use App\Enums\QueueName;
use App\Enums\ShipmentStatus;
use App\Exceptions\HelloPackageException;
use App\Facades\HubSpot;
use App\Models\OrderOffer;
use App\Models\Shipment;
use App\Services\HelloPackageService;
use Carbon\Carbon;
use HubSpot\Client\Crm\Deals\ApiException;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CheckShipmentStatusJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public $tries = 10;
    public $backoff = 90;

    public function __construct(protected Shipment|int|null $shipment = null)
    {
        $this->onQueue(QueueName::SHIPMENTS->value);

        if (is_int($shipment)) {
            $this->shipment = Shipment::find($shipment);
        }
    }

    /**
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     * @throws \JsonException
     * @throws \App\Exceptions\HelloPackageException
     */
    public function handle(): void
    {
        if (!$this->shipment) {
            return;
        }

        try {
            /** @var HelloPackageService $helloPackage */
            $helloPackage = app()->make(HelloPackageService::class);

            $info = $helloPackage->getInfoByTrackerNumber($this->shipment->tracking_number);

            $hubspotIds = $this->getHubspotIdsByShipment($this->shipment);

            $properties = [
                'delivery_status'       => ShipmentStatus::SHIPPED->value,
                'delivery_tracking_url' => $info->url,
                'delivery_location'     => trim(implode(' ', $info->updates[0]->location->toArray())),
                'shipped_at'            => $this->shipment->shipped_at?->format('Y-m-d'),
            ];

            if ($update = $this->getDeliveryUpdate($info)) {
                $deliveredAt = Carbon::parse($update->dateTime);

                // Handle invalid dates.
                if ($deliveredAt->diffInYears() > 1) {
                    $deliveredAt = Carbon::now();
                }

                $this->shipment->delivered_at = $deliveredAt;
                $this->shipment->status = ShipmentStatus::DELIVERED->value;
                $this->shipment->saveQuietly();

                $properties = [
                    'delivery_status'       => ShipmentStatus::DELIVERED->value,
                    'delivered_at'          => $deliveredAt->format('Y-m-d'),
                    'delivery_location'     => trim(implode(' ', $update->location->toArray())),
                    'delivery_tracking_url' => $info->url,
                    'shipped_at'            => $this->shipment->shipped_at?->format('Y-m-d'),
                ];
            } else {
                $this->shipment->status_checked_at = Carbon::now()->addDay();
                $this->shipment->saveQuietly();
            }

            foreach ($hubspotIds as $hubspotId) {
                if (!$hubspotId) {
                    continue;
                }

                try {
                    HubSpot::updateDeal($hubspotId, $properties);
                } catch (\Exception $exception) {
                    continue;
                }
            }
        } catch (HelloPackageException $ex) {
            if (str_contains($ex->getMessage(), 'unable to get tracking data')) {
                $this->shipment->check_status_attempts = Shipment::CHECK_STATUS_MAX_ATTEMPTS;
                $this->shipment->saveQuietly();
                return;
            }

            ++$this->shipment->check_status_attempts;
            $this->shipment->saveQuietly();

            if ($this->shipment->check_status_attempts === Shipment::CHECK_STATUS_MAX_ATTEMPTS) {
                throw $ex;
            }
        } catch (ConnectionException $ex) {
            ++$this->shipment->check_status_attempts;
            $this->shipment->saveQuietly();
        }
    }

    private function getHubspotIdsByShipment(Shipment $shipment): array
    {
        $orderIds = $shipment->orders->pluck('id')->toArray();

        return OrderOffer::query()
            ->whereIn('order_id', $orderIds)
            ->get()
            ->pluck('hubspot_id')
            ->toArray();
    }

    private function getDeliveryUpdate(TrackingInfo $info): ?TrackingUpdate
    {
        foreach ($info->updates as $update) {
            if ($update->delivered) {
                return $update;
            }

            if (str_contains(strtolower($update->status), 'delivered')) {
                return $update;
            }
        }

        return null;
    }
}
