<?php

namespace App\Jobs;

use App\Enums\PayloadSource;
use App\Enums\PayloadStatus;
use App\Enums\QueueName;
use App\Enums\ShipmentCarrier;
use App\Enums\ShipmentService;
use App\Exceptions\HubspotException;
use App\Facades\HubSpot;
use App\Models\Order;
use App\Models\Shipment;
use App\Services\PayloadService;
use HubSpot\Client\Crm\Deals\Model\Error;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class HubspotUpdateDealShipment implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private PayloadService $payloadService;

    public function __construct(private Shipment $shipment, private Order $order)
    {
        $this->onQueue(QueueName::HUBSPOT->value);
        $this->payloadService = app()->make(PayloadService::class);
    }

    public function handle(): void
    {
        if (!$this->order->customer->numverify_valid
            && $this->order->customer->zerobounce_status != 'valid') {
            return;
        }

        $deals = $this->prepareDeals();

        if (!count($deals)) {
            return;
        }

        $payload = $this->payloadService->getPayload(
            PayloadSource::HUBSPOT_UPDATE_SHIPMENT,
            $deals,
            $this->shipment->id . ':' . $this->order->id
        );

        $this->payloadService->attachModelToPayload($payload, $this->shipment, true);

        foreach ($deals as $dealId => $properties) {
            try {
                $deal = Hubspot::updateDeal($dealId, $properties);

                if ($deal instanceof Error) {
                    throw new HubspotException($deal->getMessage());
                }

                $this->payloadService->setStatus($payload, PayloadStatus::SUCCESS);
                $this->payloadService->savePayloadResponse($payload, $deal);
            } catch (\Exception $e) {
                $this->payloadService->setStatus($payload, PayloadStatus::FAILED);
                $this->payloadService->savePayloadError($payload, $e);

                report($e);
            }
        }
    }

    private function prepareDeals(): array
    {
        $deals = [];
        $properties = [
            'tracking_number'  => $this->getShipmentTracking($this->order),
            'shipped_at'       => $this->shipment->shipped_at?->format('Y-m-d'),
            'delivery_carrier' => $this->order->shipments
                ->map(fn(Shipment $shipment) => ShipmentCarrier::properCase($shipment->carrier_code))
                ->implode(', '),
            'delivery_service' => $this->order->shipments
                ->map(fn(Shipment $shipment) => ShipmentService::properCase($shipment->service_code))
                ->implode(', '),
        ];

        foreach ($this->order->orderOffers as $orderOffer) {
            // some orderOffers are not sent to hubspot depending on current order status, skip those
            if (empty($orderOffer->hubspot_id)) {
                continue;
            }

            $deals[$orderOffer->hubspot_id] = $properties;
        }

        return $deals;
    }

    private function getShipmentTracking(Order $order)
    {
        $trackingNumbers = $order->shipments
            ->pluck('tracking_number')
            ->filter(fn($value) => $value !== null && !empty($value))
            ->toArray();
        return implode(', ', $trackingNumbers);
    }
}
