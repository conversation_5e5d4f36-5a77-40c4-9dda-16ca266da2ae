<?php

namespace App\Jobs\SQS;

use App\Enums\NotificationType;
use App\Enums\PayloadSource;
use App\Enums\SQSQueueName;
use App\Exceptions\ASCException;
use App\Exceptions\ImportOrderException;
use App\Exceptions\ModelValidationException;
use App\Notifications\WebhookError;
use App\Services\ImportService\Order\ImportOrderService;
use App\Services\NoticeService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class CommittedCoachesWebhookJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;

    public function __construct(private array $message)
    {
        $this->onConnection('sqs');
        $this->onQueue(SQSQueueName::WEBHOOKS_COMMITTED_COACHES->value);
    }

    /**
     * @throws ASCException
     * @throws ModelValidationException
     * @throws ImportOrderException
     */
    public function handle(): void
    {
        $noticeService = app()->make(NoticeService::class);

        if (!config('services.committed_coaches.sqs_enabled')) {
            $this->release(now()->addMinutes(15));

            $noticeService->sendSlackNotification(
                NotificationType::WEBHOOK_ERROR,
                new WebhookError($this->message, 'Committed Coaches webhook SQS is disabled')
            );

            return;
        }

        $noticeService->sendSlackNotification(
            NotificationType::WEBHOOK_ERROR,
            new WebhookError($this->message, 'Committed Coaches webhook SQS message received')
        );

        $payload = array_merge($this->message['payload'], $this->message['queryStringParameters']);
        $import = new ImportOrderService(PayloadSource::COMMITTED_COACHES_WEBHOOK);
        $account = $import->getAccount($payload);
        $import->import($account, $payload, null, $this->message['requestId']);
    }
}
