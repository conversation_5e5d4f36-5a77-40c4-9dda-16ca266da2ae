<?php

namespace App\Jobs;

use Illuminate\Contracts\Cache\Repository;
use Illuminate\Support\Facades\Cache;
use App\Services\EmailSwipe\EmailSwipeService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class EmailSwipeToZipJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public $uniqueFor = 60;

    public function __construct()
    {
        $this->delay(now()->addSeconds($this->uniqueFor));
    }

    public function handle(EmailSwipeService $service): void
    {
        $service->listGenerateZips();
    }

    public function uniqueId(): string
    {
        return 'email_swipes_to_zip';
    }
}
