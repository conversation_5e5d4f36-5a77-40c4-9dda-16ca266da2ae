<?php

namespace App\Policies;

use App\Models\DomainRegistration;
use App\Models\Role;
use App\Models\User;

class DomainRegistrationPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view all domain registrations');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, DomainRegistration $domainRegistration): bool
    {
        return $user->can('view domain registrations');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create domain registrations');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, DomainRegistration $domainRegistration): bool
    {
        return $user->can('update domain registrations');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, DomainRegistration $domainRegistration): bool
    {
        if ($user->hasRole(Role::customerRoles())) {
            return false;
        }

        return $user->can('delete domain registrations');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, DomainRegistration $domainRegistration): bool
    {
        return $user->can('delete domain registrations');
    }

    /**
     * Determine whether the user can permanently delete the model.
     * Only super-admins can force delete.
     */
    public function forceDelete(User $user, DomainRegistration $domainRegistration): bool
    {
        return false;
    }
}
