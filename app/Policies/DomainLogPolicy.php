<?php

namespace App\Policies;

use App\Models\DomainLog;
use App\Models\User;

class DomainLogPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view all domain logs');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, DomainLog $log): bool
    {
        return $user->can('view domain logs');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return false;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, DomainLog $log): bool
    {
        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, DomainLog $log): bool
    {
        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, DomainLog $log): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     * Only super-admins can force delete.
     */
    public function forceDelete(User $user, DomainLog $log): bool
    {
        return false;
    }
}
