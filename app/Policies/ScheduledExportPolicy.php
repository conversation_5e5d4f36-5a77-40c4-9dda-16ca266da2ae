<?php

namespace App\Policies;

use App\Models\Role;
use App\Models\ScheduledExport;
use App\Models\User;

class ScheduledExportPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view all scheduled exports');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ScheduledExport $scheduledExport): bool
    {
        return $user->can('view scheduled exports') && ($user->isAdmin() || $user->id === $scheduledExport->user_id);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create scheduled exports');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ScheduledExport $scheduledExport): bool
    {
        return $user->can('update scheduled exports') && ($user->isAdmin() || $user->id === $scheduledExport->user_id);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ScheduledExport $scheduledExport): bool
    {
        if ($user->hasRole(Role::customerRoles())) {
            return false;
        }

        return $user->can('delete scheduled exports') && ($user->isAdmin() || $user->id === $scheduledExport->user_id);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, ScheduledExport $scheduledExport): bool
    {
        return $user->can('delete scheduled exports') && ($user->isAdmin() || $user->id === $scheduledExport->user_id);
    }

    /**
     * Determine whether the user can permanently delete the model.
     * Only super-admins can force delete.
     */
    public function forceDelete(User $user, ScheduledExport $scheduledExport): bool
    {
        return false;
    }
}
