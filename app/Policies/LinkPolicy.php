<?php

namespace App\Policies;

use App\Models\Role;
use App\Models\User;
use App\Models\Link;

class LinkPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view all links');
    }

    public function view(User $user, Link $link): bool
    {
        return $user->can('view links') && ($user->id === $link->affiliate->user_id || $user->hasRole(Role::ADMIN));
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create links');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Link $link): bool
    {
        return $user->can('update links') && $user->id === $link->affiliate->user_id;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Link $link): bool
    {



        return $user->can('delete links');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Link $link): bool
    {
        return $user->can('delete links');
    }

    /**
     * Determine whether the user can permanently delete the model.
     * Only super-admins can force delete.
     */
    public function forceDelete(User $user, Link $link): bool
    {
        return false;
    }
}
