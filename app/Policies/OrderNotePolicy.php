<?php

namespace App\Policies;

use App\Models\OrderNote;
use App\Models\Role;
use App\Models\User;

class OrderNotePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view orders');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, OrderNote $orderNote): bool
    {
        return $user->can('view orders');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('view orders');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, OrderNote $orderNote): bool
    {
        $minutes = config('app.time_allowed_for_editing');

        return $user->can('view orders') && $orderNote->user_id == $user->id && $orderNote->created_at
            && $orderNote->created_at->getTimestamp() > now()->subMinutes($minutes)->getTimestamp();
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, OrderNote $orderNote): bool
    {
        if ($user->hasRole(Role::customerRoles())) {
            return false;
        }

        $minutes = config('app.time_allowed_for_editing');

        return $user->can('view orders') && $orderNote->user_id == $user->id && $orderNote->created_at
            && $orderNote->created_at->getTimestamp() > now()->subMinutes($minutes)->getTimestamp();
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, OrderNote $orderNote): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     * Only super-admins can force delete.
     */
    public function forceDelete(User $user, OrderNote $orderNote): bool
    {
        return false;
    }
}
