<?php

namespace App\Notifications;

use App\Enums\AccountSource;
use App\Models\Account;
use App\Models\Offer;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Slack\BlockKit\Blocks\ContextBlock;
use Illuminate\Notifications\Slack\SlackMessage;

class MissingReportError extends Notification
{
    use Queueable;

    public function __construct(protected Account $account,protected Offer $offer,  protected Carbon $startsAt, protected Carbon $endsAt)
    {
    }

    public function via(object $notifiable): array
    {
        return ['slack'];
    }

    public function toSlack(object $notifiable): SlackMessage
    {
        return (new SlackMessage)
            ->to(config('services.slack.notifications.channels.offer_reports'))
            ->headerBlock(
                "Report not found for ".
                AccountSource::properCase($this->account->source).
                " ({$this->account->source_ref}) and offer ({$this->offer->source_ref})"
            )
            ->contextBlock(function (ContextBlock $block) {
                $block->text(
                    "There is no ".
                    AccountSource::properCase($this->account->source).
                    " report from ".
                    "{$this->startsAt->format('Y-m-d H:i:s')} thru ".
                    "{$this->endsAt->format('Y-m-d H:i:s')} ".
                    " for account {$this->account->source_ref}".
                    " and offer {$this->offer->source_ref}"
                );
            });
    }
}
