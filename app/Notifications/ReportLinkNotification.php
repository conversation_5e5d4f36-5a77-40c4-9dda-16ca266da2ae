<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ReportLinkNotification extends Notification
{
    use Queueable;

    public function __construct(
        public string $reportName,
        public string $downloadUrl,
        public array $additionalLinks = []
    ) {
    }

    public function via($notifiable): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage())
            ->subject("Report Ready: {$this->reportName}")
            ->view('mail.report-links', [
                'user' => $notifiable,
                'reportName' => $this->reportName,
                'downloadUrl' => $this->downloadUrl,
                'additionalLinks' => $this->additionalLinks,
            ]);
    }
}
