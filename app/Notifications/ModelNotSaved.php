<?php

namespace App\Notifications;

use App\Enums\AccountSource;
use App\Models\Payload;
use Illuminate\Bus\Queueable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Slack\BlockKit\Blocks\ActionsBlock;
use Illuminate\Notifications\Slack\BlockKit\Blocks\ContextBlock;
use Illuminate\Notifications\Slack\SlackMessage;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Str;

class ModelNotSaved extends Notification
{
    use Queueable;

    public function __construct(
        public ?string $exception,
        public ?string $desc,
        public ?Model $model = null,
        public ?Payload $payload = null
    ) {
    }

    public function via(object $notifiable): array
    {
        return ['slack'];
    }

    public function toSlack(object $notifiable): SlackMessage
    {
        $message = (new SlackMessage)->to(
            config('services.slack.notifications.channels.model_validation', 'triage-data-validation')
        );

        if ($this->payload || $this->model) {
            $message->actionsBlock(function (ActionsBlock $block) {
                if ($this->payload) {
                    $block->button('Payload')
                        ->url(route('nova.pages.detail', ['payloads', $this->payload->id]))
                        ->primary();
                }
                if ($this->model) {
                    $block->button(class_basename($this->model))
                        ->url(route('nova.pages.detail', [Str::plural(class_basename($this->model)), $this->model->id]))
                        ->primary();
                }
            });
        }

        $message
            ->headerBlock($this->exception)
            ->contextBlock(function (ContextBlock $block) {
                $block->text($this->desc);
            });

        return $message;
    }
}
