<?php

namespace App\Notifications;

use App\Enums\NotificationType;
use App\Enums\PayloadStatus;
use App\Exports\ExportFromCollection;
use App\Models\Payload;
use App\Nova\Filters\CustomEndDateFilter;
use App\Nova\Filters\CustomStartDateFilter;
use App\Nova\Filters\PayloadHasError;
use App\Nova\Filters\PayloadSource;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Slack\BlockKit\Blocks\ActionsBlock;
use Illuminate\Notifications\Slack\SlackMessage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Laravel\Nova\Filters\FilterEncoder;
use Maatwebsite\Excel\Facades\Excel;

class PayloadError extends Notification
{
    use Queueable;

    public function __construct(public Payload $payload)
    {
    }

    public function via(object $notifiable): array
    {
        return ['slack'];
    }

    public function toSlack(object $notifiable): SlackMessage
    {
        $message = (new SlackMessage())->to(NotificationType::PAYLOAD_ERROR->getSlackChannel());

        $filters = [
            [
                PayloadSource::class => $this->payload->source,
                PayloadHasError::class => 'true',
                CustomStartDateFilter::class => $this->payload->created_at?->format('Y-m-d'),
                CustomEndDateFilter::class => $this->payload->created_at?->format('Y-m-d'),
            ]
        ];
        $filter = (new FilterEncoder($filters))
            ->encode();

        $url = $this->generateFile();

        $message->actionsBlock(function (ActionsBlock $block) use ($filter, $url) {
            $block->button('Payload')
                ->url(route('nova.pages.index', ['resource' => 'payloads', 'payloads_filter' => $filter]))
                ->primary();
            if ($url) {
                $block->button('CSV')
                    ->url($url)
                    ->primary();
            }
        });

        $message->headerBlock(
            'Payloads with source "' . $this->payload->source . '" has escalating errors'
        );

        return $message;
    }

    public function generateFile(): ?string
    {
        $payloads = Payload::query()
            ->where('source', '=', $this->payload->source)
            ->where('status', '=', PayloadStatus::FAILED->value)
            ->where('created_at', '>=', $this->payload->created_at?->startOfDay())
            ->where('created_at', '<=', $this->payload->created_at?->endOfDay())
            ->get();

        $rows = collect();
        foreach ($payloads as $payload) {
            $rows->push(
                [
                    $payload->id,
                    $payload->source,
                    $payload->source_ref,
            ]);
        }

        $filePathS3 = sprintf(
            'payloads/%s_%s.csv',
            $this->payload->source,
            $this->payload->created_at?->toDateString(),
        );
        $content = Excel::raw(
            new ExportFromCollection(
                $rows,
                [
                    'ID',
                    'Source',
                    'Source Ref',
                ]
            ),
            \Maatwebsite\Excel\Excel::CSV
        );
        $result = Storage::disk('s3')->put($filePathS3, $content);
        if (!$result) {
            Log::error('Payload export file: not save file on s3');
            return null;
        }

        return Storage::disk('s3')->url($filePathS3);
    }
}
