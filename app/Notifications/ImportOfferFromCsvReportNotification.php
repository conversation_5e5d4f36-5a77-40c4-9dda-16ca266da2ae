<?php

namespace App\Notifications;

use App\Models\User;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class ImportOfferFromCsvReportNotification extends Notification
{
    public function __construct(
        public array $totals,
        public string $filePath,
        public ?string $url
    ) {
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        $filename = sprintf(
            'import_offers_%s',
            now()->format('Y-m-d-H-i-s')
        );
        $filename = $this->url ? basename($this->url) : $filename;
        $fileLink = $this->url;
        $totals = $this->totals;

        /** @var User $notifiable */
        $mailMessage = (new MailMessage())
            ->subject('Import offers report: ' . $filename)
            ->view('mail/import_offers_report', compact('notifiable', 'filename', 'fileLink', 'totals'));

        if (!$this->url) {
            $mailMessage->attach($this->filePath, [
                'as'   => $filename,
                'mime' => 'text/csv',
            ]);
        }

        return $mailMessage;
    }
}
