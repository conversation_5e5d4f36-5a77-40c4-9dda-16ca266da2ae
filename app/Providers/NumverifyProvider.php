<?php

namespace App\Providers;

use App\Services\NumverifyService;
use Illuminate\Support\ServiceProvider;

class NumverifyProvider extends ServiceProvider
{
    public function register()
    {
        //
    }

    public function boot()
    {
        $this->app->bind('numverify', function () {
            return new NumverifyService();
        });
    }

    public function provides()
    {
        return ['numverify'];
    }
}
