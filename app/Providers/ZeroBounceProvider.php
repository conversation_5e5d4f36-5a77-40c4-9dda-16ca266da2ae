<?php

namespace App\Providers;

use App\Services\HubSpotService;
use App\Services\ZeroBounceService;
use Illuminate\Support\ServiceProvider;

class ZeroBounceProvider extends ServiceProvider
{
    public function register()
    {
        //
    }

    public function boot()
    {
        $this->app->bind('zero_bounce', function () {
            return new ZeroBounceService();
        });
    }

    public function provides()
    {
        return ['zero_bounce'];
    }
}
