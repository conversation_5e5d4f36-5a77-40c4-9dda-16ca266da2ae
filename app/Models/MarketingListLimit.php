<?php

namespace App\Models;

use App\Traits\ValidatesModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Auditable;

class MarketingListLimit extends Model implements \OwenIt\Auditing\Contracts\Auditable
{
    use Auditable;
    use HasFactory;
    use ValidatesModel;

    protected $fillable = [
        'marketing_list_id',
        'group',
        'domains',
        'limit',
    ];

    public array $rules = [
        'marketing_list_id' => 'required',
        'group'  => 'required',
        'domains' => 'required',
        'limit'  => 'required',
    ];

    protected $casts = [
        'domains' => 'array',
    ];

    public function marketingList(): BelongsTo
    {
        return $this->belongsTo(MarketingList::class);
    }
}
