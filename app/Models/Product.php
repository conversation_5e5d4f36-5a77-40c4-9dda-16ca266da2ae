<?php

namespace App\Models;

use App\Enums\MediaCollectionName;
use App\Enums\ProductCostType;
use App\Enums\ProductType;
use App\Enums\ProductVisibility;
use App\Services\EmailSwipe\EmailSwipeService;
use App\Traits\HasMeta;
use App\Traits\HasMoney;
use App\Traits\ValidatePolicy;
use App\Traits\ValidatesModel;
use Carbon\Carbon;
use Cknow\Money\Casts\MoneyIntegerCast;
use Cknow\Money\Money;
use Cknow\Money\Rules\Currency;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Auditable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;

/**
 * @property-read Collection|Media[] $banners_zips
 * @property-read Collection|Media[] $mediaEmailSwipes
 * @property-read int $cog
 * @property-read int $avg
 * @property ProductSku[] $skus
 */
class Product extends Model implements \OwenIt\Auditing\Contracts\Auditable, HasMedia
{
    use Auditable;
    use HasFactory;
    use HasMeta;
    use HasMoney;
    use HasRelationships;
    use InteractsWithMedia;
    use SoftDeletes;
    use ValidatePolicy;

    use ValidatesModel;

    protected $guarded = ['id'];

    protected $casts = [
        'default_price'     => MoneyIntegerCast::class . ':currency',
        'parent_product_id' => 'integer',
    ];

    public $fillable = [
        'code',
        'name',
        'type',
        'default_price',
        'currency',
    ];

    public array $rules = [
        'parent_product_id' => 'nullable|exists:products,id|different:id',
        'code'              => 'required|string|unique:products,code,{{id}}|max:5',
        'name'              => 'required|string|max:255',
        'description'       => 'nullable|string|max:255',
        'type'              => ['required', ProductType::class],
        'default_price'     => 'required|money_min:0|money',
        'currency'          => ['required', 'size:3', Currency::class],
        'gtm'               => 'nullable|string|max:255',
        'enabled'           => 'boolean',
        'visibility'        => ['required', ProductVisibility::class],
    ];

    protected static function booted(): void
    {
        static::deleted(function (self $product) {
            if ($product->ordersCount > 0) {
                throw new \Exception('Cannot delete product with existing orders.');
            }

            $product->offers->each(fn(Offer $offer) => $offer->delete());
            $product->domains()->detach();
        });

        static::forceDeleted(function (self $product) {
            if ($product->ordersCount > 0) {
                throw new \Exception('Cannot delete product with existing orders.');
            }

            $product->domains()->detach();
            $product->payloads()->detach();
        });

        static::updating(function (self $product) {
            if ($product->isDirty('enabled') && !$product->enabled) {
                $product->offers->each(function (Offer $offer) {
                    $offer->enabled = false;
                    $offer->save();
                });
            }
        });
    }

    public function scopeByCode(Builder $query, string $code): void
    {
        $query->where('code', $code);
    }

    public function accounts(): HasManyThrough
    {
        return $this->hasManyThrough(
            Account::class,
            Offer::class,
            'product_id',
            'id',
            'id',
            'account_id',
        )->distinct();
    }

    public function offers(): BelongsToMany
    {
        return $this->belongsToMany(Offer::class)->using(OfferProduct::class);
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'parent_product_id', 'id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(Product::class, 'parent_product_id', 'id');
    }

    public function reports(): HasMany
    {
        return $this->hasMany(ProductReport::class, 'product_id');
    }

    public function emailSwipes(): HasMany
    {
        return $this->hasMany(EmailSwipe::class, 'product_id');
    }

    public function bannersZips(): Attribute
    {
        return Attribute::get(fn() => $this->getMedia(MediaCollectionName::BANNERS_ZIP->value));
    }

    public function authorsZips(): Attribute
    {
        return Attribute::get(fn() => $this->getMedia(MediaCollectionName::AUTHORS_ZIP->value));
    }

    public function ordersCount(): Attribute
    {
        return Attribute::get(fn() => $this->getOrderValue('count'));
    }

    public function ordersSumTotalAmount(): Attribute
    {
        return Attribute::get(fn() => $this->getOrderValue('sum_total_amount'));
    }

    public function ordersSumAffiliateCommission(): Attribute
    {
        return Attribute::get(fn() => $this->getOrderValue('sum_affiliate_commission'));
    }

    private function getOrderValue(string $type): mixed
    {
        $offerIds = $this->offers->pluck('id')->toArray();

        $query = Order::query()->whereHas('offers', function (Builder $query) use ($offerIds) {
            $query->whereIn('offers.id', $offerIds);
        });

        return match ($type) {
            'sum_total_amount' => $query->sum('base_currency_total_amount'),
            'sum_affiliate_commission' => $query->sum('base_currency_affiliate_commission'),
            default => $query->count(),
        };
    }

    public function domains(): BelongsToMany
    {
        return $this->belongsToMany(Domain::class, 'domain_product')
            ->using(DomainProduct::class);
    }

    public function payloads(): MorphToMany
    {
        return $this->morphToMany(Payload::class, 'model', 'model_payload')
            ->withTimestamps();
    }

    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('primary');
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(300)
            ->height(300)
            ->format('png');
    }

    public function mediaEmailSwipes(): MorphMany
    {
        return $this->media()
            ->where('collection_name', '=', EmailSwipeService::MEDIA_COLLECTION_NAME);
    }

    public function pageTemplates(): BelongsToMany
    {
        return $this->belongsToMany(PageTemplate::class);
    }

    public function costs(): HasMany
    {
        return $this->hasMany(ProductCost::class, 'product_id');
    }

    public function costFieldByDate(ProductCostType $type, ?Carbon $date = null): Money
    {
        if (!$date) {
            $date = now();
        }

        $cost = ProductCost::query()
            ->where([
                'product_id' => $this->id,
                'type'       => $type->value,
            ])
            ->where('created_at', '<=', $date)
            ->orderBy('created_at', 'desc')
            ->limit(1)
            ->get()
            ->first();

        if (!$cost) {
            $cost = ProductCost::query()->where([
                'product_id' => $this->id,
                'type'       => $type->value,
            ])->first();
        }

        if (!$cost) {
            return Money::parse(0);
        }

        return $cost->amount;
    }

    public function activeJobs(): MorphMany
    {
        return $this->morphMany(ActiveJob::class, 'model');
    }

    public function testimonials(): HasMany
    {
        return $this->hasMany(Testimonial::class, 'product_id');
    }

    public function skus(): HasMany
    {
        return $this->hasMany(ProductSku::class);
    }

    public function attachSku(?string $sku): void
    {
        if (!$sku) {
            return;
        }

        foreach ($this->skus ?? [] as $row) {
            if ($sku == $row->sku) {
                return;
            }
        }

        $productSku = new ProductSku(
            [
                'sku' => $sku,
            ]
        );
        $this->skus()->save($productSku);
    }

    public function detachSku(?string $sku): void
    {
        if (!$sku) {
            return;
        }

        foreach ($this->skus ?? [] as $row) {
            if ($sku == $row->sku) {
                $row->delete();
                return;
            }
        }
    }

    public function getCogAttribute()
    {
        return Money::parse(
            $this->costs()
                ->where('type', '=', ProductCostType::COG->value)
                ->latest()
                ->first()
                ?->amount ?: 0
        );
    }

    public function getAvgAttribute()
    {
        return Money::parse(
            $this->costs()
                ->where('type', '=', ProductCostType::AVG_SHIPPING->value)
                ->latest()
                ->first()
                ?->amount ?: 0
        );
    }

    public function marketingLists(): MorphMany
    {
        return $this->morphMany(MarketingList::class, 'marketable');
    }

    public function offerProducts(): HasMany
    {
        return $this->hasMany(OfferProduct::class);
    }

    public function shipoffersProducts(): MorphMany
    {
        return $this->morphMany(ShipoffersProduct::class, 'model');
    }
}
