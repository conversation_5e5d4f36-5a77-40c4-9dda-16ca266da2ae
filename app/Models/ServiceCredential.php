<?php

namespace App\Models;

use App\Enums\ServiceCredentialType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ServiceCredential extends Model
{
    use HasFactory;

    protected $casts = [
        'credentials'        => 'encrypted:array',
        'properties'         => 'array',
        'refresh_token'      => 'encrypted',
        'expires_at'         => 'datetime',
        'publicly_available' => 'boolean',
    ];

    protected $fillable = [
        'service',
        'credentials',
        'properties',
        'refresh_token',
        'expires_at',
        'publicly_available',
    ];

    /**
     * Check if this service credential is for an internal service type.
     */
    public function isInternalService(): bool
    {
        return in_array($this->service, array_column(ServiceCredentialType::cases(), 'value'));
    }

    /**
     * Get the internal service type enum if this is an internal service.
     */
    public function getInternalServiceType(): ?ServiceCredentialType
    {
        return ServiceCredentialType::tryFrom($this->service);
    }
}
