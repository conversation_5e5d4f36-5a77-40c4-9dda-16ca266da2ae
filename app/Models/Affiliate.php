<?php

namespace App\Models;

use App\Enums\BusinessUnit;
use App\Enums\Category;
use App\Enums\SubCategory;
use App\Http\Repositories\Scopes\DateRangeScope;
use App\Jobs\SendAffiliateApproveJob;
use App\Traits\HasMeta;
use App\Traits\ValidatesModel;
use Arr;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Auditable;

/**
 * @property string|null $name
 */
class Affiliate extends Model implements \OwenIt\Auditing\Contracts\Auditable
{
    use Auditable;
    use DateRangeScope;
    use HasFactory;
    use HasMeta;
    use SoftDeletes;
    use ValidatesModel;

    protected $guarded = ['id'];

    protected $casts = [
        'traffic_sources' => 'array',
        'enabled'         => 'boolean',
        'is_approved'     => 'boolean',
        'last_active_at'  => 'datetime',
    ];

    public array $rules = [
        'name'          => 'nullable|string|max:255',
        'business_unit' => ['nullable', BusinessUnit::class],
        'category'      => ['nullable', 'required_with:sub_category', Category::class],
        'sub_category'  => ['nullable', SubCategory::class],
    ];

    protected array $auditExclude = [
        'last_active_at',
    ];

    protected $appends = [
        'source_ref',
    ];

    protected static function booted(): void
    {
        static::deleted(function (self $affiliate) {
            $affiliate->pixels->each(fn(Pixel $pixel) => $pixel->delete());
            $affiliate->domains->each(function (Domain $domain) {
                $domain->affiliate_id = null;
                $domain->save();
            });
            $affiliate->requests->each(fn(AffiliateRequest $request) => $request->delete());
        });

        static::forceDeleting(function (self $affiliate) {
            $affiliate->payloads()->detach();
            $affiliate->domains->each(function (Domain $domain) {
                $domain->affiliate_id = null;
                $domain->save();
            });
            $affiliate->requests->each(fn(AffiliateRequest $request) => $request->forceDelete());
        });

        static::restored(function (self $affiliate) {
            // Pixels start as disapproved when affiliate restored.
            $affiliate->pixels->each(function (Pixel $pixel) {
                $pixel->restore();
                $pixel->approved_at = null;
                $pixel->enabled = false;
                $pixel->save();
            });
            $affiliate->requests->each(fn(AffiliateRequest $request) => $request->restore());
        });

        static::updating(function (self $affiliate) {
            if ($affiliate->isDirty('enabled') && !$affiliate->enabled) {
                $affiliate->pixels->each(function (Pixel $pixel) {
                    $pixel->enabled = false;
                    $pixel->save();
                });
            }
        });

        static::saved(function (self $affiliate) {
            if ($affiliate->wasChanged(['is_approved']) && $affiliate->is_approved) {
                if ($affiliate->user) {
                    $affiliate->user->notify(
                        new SendAffiliateApproveJob(
                            $affiliate
                        )
                    );
                }
            }
        });
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function accounts(): BelongsToMany
    {
        return $this->belongsToMany(Account::class)
            ->using(AccountAffiliate::class)
            ->withPivot('affiliate_source_ref');
    }

    public function accountAffiliates(): HasMany
    {
        return $this->hasMany(AccountAffiliate::class);
    }

    public function pixels(): HasMany
    {
        return $this->hasMany(Pixel::class);
    }

    public function domains(): HasMany
    {
        return $this->hasMany(Domain::class);
    }

    public function requests(): HasMany
    {
        return $this->hasMany(AffiliateRequest::class);
    }

    public function payloads(): MorphToMany
    {
        return $this->morphToMany(Payload::class, 'model', 'model_payload')
            ->withTimestamps();
    }

    public function domainRegistrations(): HasMany
    {
        return $this->hasMany(DomainRegistration::class);
    }

    public function tracking(): HasMany
    {
        return $this->hasMany(Tracking::class);
    }

    protected function name(): Attribute
    {
        return Attribute::make(
            get: fn($value, $attributes) => Arr::get($attributes, 'name') ?: $this->user?->name ?? $this->source_ref
        );
    }

    protected function sourceRef(): Attribute
    {
        return Attribute::make(
            get: fn($value, $attributes) => $this->accounts
                ->pluck('pivot.affiliate_source_ref')
                ->filter()
                ->unique()
                ->implode(', ')
        );
    }

    public function scheduledExports(): HasManyThrough
    {
        return $this->hasManyThrough(
            ScheduledExport::class,
            Affiliate::class,
            'user_id',
            'user_id',
            'user_id',
            'user_id',
        );
    }

    public function marketingLists(): MorphMany
    {
        return $this->morphMany(MarketingList::class, 'marketable');
    }
}
