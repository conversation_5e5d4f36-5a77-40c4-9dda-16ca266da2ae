<?php

namespace App\Models;

use App\Enums\AccountSource;
use App\Enums\ProductCostType;
use App\Events\OrderOfferSaved;
use App\Traits\ValidatesModel;
use Cknow\Money\Casts\MoneyIntegerCast;
use Cknow\Money\Money;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Auditable;

class OrderOffer extends Model implements \OwenIt\Auditing\Contracts\Auditable
{
    use Auditable;
    use HasFactory;
    use SoftDeletes;
    use ValidatesModel;

    protected $guarded = ['id'];

    protected $casts = [
        'quantity'        => 'integer',
        'amount'          => MoneyIntegerCast::class,
        'discount_amount' => MoneyIntegerCast::class,
        're_order_at'     => 'datetime',
    ];

    public array $rules = [
        'domain_id'       => 'nullable|exists:domains,id',
        'offer_id'        => 'required|exists:offers,id',
        'quantity'        => 'required|integer|min:1',
        'amount'          => 'required|money|money_min:0',
        'discount_amount' => 'money|money_min:0',
        'name'            => 'string|max:255',
    ];

    protected static function booted(): void
    {
        static::creating(function (self $orderOffer) {
            if (empty($orderOffer->name)) {
                $orderOffer->name = $orderOffer->offer->name ?: $orderOffer->offer->products->implode('name', ' + ');
            }
        });

        static::created(function (self $orderOffer) {
            $shippingCost = $orderOffer->order->shipping_cost;
            $orderOffer->order->refresh();
            $orderOffer->order->shipping_cost = $shippingCost->add(
                $orderOffer->calculateAvgShippingCostByProduct()
            );
            $orderOffer->order->saveQuietly();
        });

        static::updating(function (self $orderOffer) {
            if ($orderOffer->isDirty('quantity')) {
                $orderOffer->re_order_at = now()->addDays($orderOffer->quantity * 30);
            }
        });

        static::saved(function (self $orderOffer) {
            event(new OrderOfferSaved($orderOffer));
            /** @var Order $order */
            $order = $orderOffer->order;
            $order->refresh();
            $order->cost_of_goods = $order->calculateCostOfGoods();
            if ($order->account->source == AccountSource::CLICKBANK->value) {
                $order->discount_amount = $order->calculateDiscountAmount();
            }
            $order->profit = $order->calculateProfit();
            $order->saveQuietly();
        });
    }

    public function calculateAvgShippingCostByProduct(): Money
    {
        $amounts = $this->offer->products->map(function (Product $product) {
            return $product->costFieldByDate(ProductCostType::AVG_SHIPPING, $this->order->purchased_at)
                ->multiply($this->quantity)
                ->multiply($product->pivot->quantity);
        })->all();

        if (empty($amounts)) {
            return Money::parse(0, config('money.defaultCurrency'));
        }

        return Money::parse(...$amounts);
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function offer(): BelongsTo
    {
        return $this->belongsTo(Offer::class)->withTrashed();
    }

    public function domain(): BelongsTo
    {
        return $this->belongsTo(Domain::class);
    }
}
