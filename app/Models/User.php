<?php

namespace App\Models;

use App\Traits\HasMeta;
use App\Traits\ValidatePolicy;
use App\Traits\ValidatesModel;
use Hash;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use OwenIt\Auditing\Auditable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\Permission\Traits\HasRoles;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Visanduma\NovaTwoFactor\Models\TwoFa;
use Visanduma\NovaTwoFactor\ProtectWith2FA;

class User extends Authenticatable implements MustVerifyEmail, \OwenIt\Auditing\Contracts\Auditable, HasMedia
{
    use Auditable;
    use HasApiTokens;
    use HasFactory;
    use HasMeta;
    use HasRoles;
    use HasRelationships;
    use Notifiable;
    use SoftDeletes;
    use ValidatesModel;
    use ValidatePolicy;
    use InteractsWithMedia;
    use ProtectWith2FA;

    /**
     * Minimum length for passwords.
     */
    public const MIN_PASSWORD_LEN = 8;
    public const META_REGISTRATION_STEP_COMPLETE = 'registration_step_complete';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'company',
        'skype',
        'phone',
        'password',
        'last_login_at',
        'enforce_password_age',
        'email_verification_token',
        'email_verified_at',
    ];

    protected $appends = [
        'first_name',
        'last_name',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'password_changed_at'        => 'datetime',
        'email_verified_at'          => 'datetime',
        'last_login_at'              => 'datetime',
        'is_registered_successfully' => 'boolean',
    ];

    protected array $auditExclude = [
        'password',
        'remember_token',
        'last_login_at',
        'email_verified_at',
    ];

    public array $rules = [
        'name'                 => 'required|string|max:255',
        'email'                => 'required|string|email|max:255|unique:users,email,{{id}}',
        'company'              => 'nullable|string|max:255',
        'phone'                => 'nullable|string|max:255',
        'skype'                => 'nullable|string|max:255',
        'enforce_password_age' => 'nullable|boolean',
        'password'             => 'required|string|min:' . self::MIN_PASSWORD_LEN .
            '|regex:/^(?=.*[a-zA-Z])(?=.*\d)(?=.*[@$!%*?&+-\/_.\/()[\]])[a-zA-Z\d@$!%*?&+-\/_.\/()[\]]{8,}$/',
    ];

    protected static function booted(): void
    {
        static::saving(function (User $user) {
            // Auto hash password if it's not already hashed.
            if (
                $user->isDirty('password') &&
                data_get(Hash::info($user->password), 'algoName') !== config('hashing.driver')
            ) {
                $user->password = Hash::make($user->password);
            }
            if ($user->isDirty('password')) {
                $user->password_changed_at = now();
            }
        });

        static::deleted(function (self $user) {
            if ($user->isAffiliate()) {
                $user->affiliates->each(fn(Affiliate $affiliate) => $affiliate->delete());
            }
        });

        static::restored(function (self $user) {
            if ($user->isAffiliate()) {
                $user->affiliates->each(fn(Affiliate $affiliate) => $affiliate->restore());
            }
        });

        static::forceDeleting(function (self $user) {
            foreach ($user->scheduledExports as $scheduledExport) {
                $scheduledExport->delete();
            }
        });
    }

    public function isAdmin(): bool
    {
        return $this->hasRole(Role::ADMIN);
    }

    public function isSuperAdmin(): bool
    {
        return $this->hasRole(Role::SUPER_ADMIN);
    }

    public function isAffiliate(): bool
    {
        return $this->hasRole(Role::AFFILIATE);
    }

    public function isCommitedCoachesSupport(): bool
    {
        return $this->hasRole(Role::COMMITTED_COACHES_SUPPORT);
    }

    public function hasCustomerServiceRole(): bool
    {
        return $this->hasRole([
            Role::CUSTOMER_SERVICE,
            Role::COMMITTED_COACHES_SUPPORT,
        ]);
    }

    public function affiliates(): HasMany
    {
        return $this->hasMany(Affiliate::class);
    }

    public function scheduledExports(): HasMany
    {
        return $this->hasMany(ScheduledExport::class);
    }

    public function twoFa(): HasOne
    {
        return $this->hasOne(TwoFa::class);
    }

    public function email(): Attribute
    {
        return new Attribute(set: fn($value) => strtolower(trim($value)));
    }

    public function firstName(): Attribute
    {
        return new Attribute(fn() => head(explode(' ', $this->name)));
    }

    public function lastName(): Attribute
    {
        return new Attribute(fn() => last(explode(' ', $this->name)));
    }

    public function passwordExpired(): Attribute
    {
        return new Attribute(
            fn() => config('defaults.user_password_expires_days') && (!$this->password_changed_at
                    || $this->password_changed_at->lt(now()->subDays(config('defaults.user_password_expires_days'))))
        );
    }

    public function affiliatesApproval(): Attribute
    {
        return new Attribute(
            function () {
                foreach ($this->affiliates as $affiliate) {
                    if (!$affiliate->is_approved) {
                        return false;
                    }
                }

                return true;
            }
        );
    }

    public function isOwner(?Model $model): bool
    {
        if (!$model) {
            return false;
        }
        $affiliateIds = $this->affiliates?->map(function (Affiliate $affiliate) {
            return $affiliate->id;
        })->toArray() ?: [];

        if (in_array($model->affiliate_id, $affiliateIds)) {
            return true;
        }

        return false;
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('primary')->singleFile();
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb_300')
            ->width(300)
            ->height(300);

        $this->addMediaConversion('thumb_150')
            ->width(150)
            ->height(150);
    }
}
