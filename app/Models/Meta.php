<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Meta extends Model
{
    protected $table = 'meta';

    public $fillable = [
        'key',
        'value',
    ];

    public function metable(): MorphTo
    {
        return $this->morphTo();
    }

    public function getValueAttribute($value)
    {
        try {
            return json_decode($value, true, 512, JSON_THROW_ON_ERROR);
        } catch (\Exception $e) {
            return $value;
        }
    }

    public function setValueAttribute($value): void
    {
        if (!is_string($value)) {
            $value = json_encode($value);
        }

        $this->attributes['value'] = $value;
    }
}
