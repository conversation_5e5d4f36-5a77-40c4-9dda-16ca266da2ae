<?php

namespace App\Models;

use App;
use App\Enums\OrderStatus;
use App\Helpers\Query;
use Cknow\Money\Money;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

class OfferReport extends AggregateReport
{
    use HasFactory;

    public function __construct(array $attributes = [])
    {
        $this->rules += [
            'account_id'        => 'required|exists:accounts,id',
            'offer_id'          => 'required|exists:offers,id',
            'product_report_id' => 'nullable|exists:product_reports,id',
        ];

        parent::__construct($attributes);
    }

    protected static function booted(): void
    {
        static::deleting(function (OfferReport $report) {
            $report->buygoodsOfferReports()->detach();
            $report->clickbankOfferReports()->detach();
            $report->digistoreOfferReports()->detach();
        });
    }

    /**
     * BuygoodsOfferReport source used to generate the report.
     * @return MorphToMany
     */
    public function buygoodsOfferReports(): MorphToMany
    {
        return $this->morphedByMany(
            BuygoodsOfferReport::class,
            'source',
            'offer_report_sources',
        )
            ->using(OfferReportSource::class)
            ->withTimestamps();
    }

    /**
     * ClickbankOfferReport source used to generate the report.
     * @return MorphToMany
     */
    public function clickbankOfferReports(): MorphToMany
    {
        return $this->morphedByMany(
            ClickbankOfferReport::class,
            'source',
            'offer_report_sources',
        )
            ->using(OfferReportSource::class)
            ->withTimestamps();
    }

    /**
     * DigistoreOfferReport source used to generate the report.
     * @return MorphToMany
     */
    public function digistoreOfferReports(): MorphToMany
    {
        return $this->morphedByMany(
            DigistoreOfferReport::class,
            'source',
            'offer_report_sources',
        )
            ->using(OfferReportSource::class)
            ->withTimestamps();
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function offer(): BelongsTo
    {
        return $this->belongsTo(Offer::class);
    }

    public function domain(): BelongsTo
    {
        return $this->belongsTo(Domain::class);
    }

    public function productReport(): BelongsTo
    {
        return $this->belongsTo(ProductReport::class);
    }

//    public function hasPreparedReport(MarketplaceOfferReport $report): bool
//    {
//        if (!$this->exists) {
//            return false;
//        }
//
//        return match ($report::class) {
//            BuygoodsOfferReport::class => $this->buygoodsOfferReports()->where('source_id', $report->id)->exists(),
//            ClickbankOfferReport::class => $this->clickbankOfferReports()->where('source_id', $report->id)->exists(),
//            DigistoreOfferReport::class => $this->digistoreOfferReports()->where('source_id', $report->id)->exists(),
//            default => false,
//        };
//    }

//    public function attachReport(MarketplaceOfferReport $report): void
//    {
//        switch ($report::class) {
//            case BuygoodsOfferReport::class:
//                $this->buygoodsOfferReports()->syncWithoutDetaching($report->id);
//                break;
//            case ClickbankOfferReport::class:
//                $this->clickbankOfferReports()->syncWithoutDetaching($report->id);
//                break;
//            case DigistoreOfferReport::class:
//                $this->digistoreOfferReports()->syncWithoutDetaching($report->id);
//                break;
//        }
//    }

    public static function getTotalOrderRevenue(
        self|MarketplaceOfferReport $report,
        OrderStatus|array $status = OrderStatus::COMPLETED
    ): Money {
        return Money::parse(
            Order::on(Query::maybeReadConnection())
                ->offerPurchasedBetween($report->offer_id, $report->starts_at, $report->ends_at)
                ->where('is_test', false)
                ->whereIn('status', array_wrap($status))
                ->sum('total_amount'),
            config('money.defaultCurrency')
        );
    }

    public function model(): MorphTo
    {
        return $this->morphTo();
    }
}
