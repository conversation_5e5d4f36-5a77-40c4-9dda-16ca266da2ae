<?php

namespace App\Models;

use App\Enums\ShipmentCarrier;
use App\Enums\ShipmentStatus;
use App\Traits\HidePiiData;
use App\Traits\ValidatesModel;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Auditable;
use Str;

class Shipment extends Model implements \OwenIt\Auditing\Contracts\Auditable
{
    use Auditable;
    use HasFactory;
    use SoftDeletes;
    use ValidatesModel;
    use HidePiiData;

    public const CHECK_STATUS_MAX_ATTEMPTS = 5;

    protected $guarded = ['id'];

    public $timestamps = true;

    protected $casts = [
        'shipped_at'            => 'datetime',
        'delivered_at'          => 'datetime',
        'status_checked_at'     => 'datetime',
        'check_status_attempts' => 'integer',
    ];

    public array $rules = [
        'tracking_number'       => 'string',
        'carrier_code'          => 'string',
        'service_code'          => 'string',
        'fulfillment_service'   => 'required|string',
        'shipped_at'            => 'nullable|date',
        'check_status_attempts' => 'nullable|integer',
        'status'                => ['required', 'string', ShipmentStatus::class],
    ];

    protected $piiFields = [
        'tracking_number'  => 'text',
    ];

    protected static function booted(): void
    {
        static::deleted(function (self $shipment) {
            $shipment->payloads()->detach();
            $shipment->orders()->detach();
        });
    }

    public function orders(): BelongsToMany
    {
        return $this->belongsToMany(Order::class)
            ->using(OrderShipment::class)
            ->withPivot('order_source_ref')
            ->withTimestamps();
    }

    public function carrierCode(): Attribute
    {
        return Attribute::make(set: fn($value) => Str::snake(strtolower($value)));
    }

    public function serviceCode(): Attribute
    {
        return Attribute::make(set: fn($value) => Str::snake(strtolower($value)));
    }

    public function payloads(): MorphToMany
    {
        return $this->morphToMany(Payload::class, 'model', 'model_payload')
            ->withTimestamps();
    }

    public function trackingNumberLink(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => match($this->carrier_code) {
                ShipmentCarrier::FEDEX->value =>
                    'https://www.fedex.com/wtrk/track/?tracknumbers=' . $this->tracking_number,
                ShipmentCarrier::DHL->value =>
                    'https://www.dhl.com/us-en/home/<USER>' . $this->tracking_number,
                ShipmentCarrier::UPS->value =>
                    'https://www.ups.com/track?track=yes&trackNums=' . $this->tracking_number,
                ShipmentCarrier::USPS->value =>
                    'https://tools.usps.com/go/TrackConfirmAction?tRef=fullpage&tLc=2&text28777=&tLabels='
                        . $this->tracking_number . '&tABt=false',
                ShipmentCarrier::STAMPS_COM->value =>
                    'https://www.stamps.com/tracking-details/?t=523523532532543%s' . $this->tracking_number,
                default => null
            }
        );
    }
}
