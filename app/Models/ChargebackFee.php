<?php

namespace App\Models;

use App\Enums\TransactionType;
use App\Events\ChargebackFeeSaved;
use App\Traits\HasMoney;
use App\Traits\ValidatesModel;
use Carbon\Carbon;
use Cknow\Money\Casts\MoneyIntegerCast;
use Cknow\Money\Money;
use Cknow\Money\Rules\Currency;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Auditable;

class ChargebackFee extends Model implements \OwenIt\Auditing\Contracts\Auditable
{
    use Auditable;
    use HasFactory;
    use HasMoney;
    use SoftDeletes;
    use ValidatesModel;

    protected $guarded = ['id'];

    protected $dispatchesEvents = [
        'saved' => ChargebackFeeSaved::class,
    ];

    protected $casts = [
        'amount'               => MoneyIntegerCast::class.':currency',
        'chargeback_at'        => 'datetime',
        'source_chargeback_at' => 'datetime',
    ];

    public array $rules = [
        'order_id'             => 'required|exists:orders,id',
        'currency'             => ['required', 'size:3', Currency::class],
        'amount'               => 'required|money|money_min:1',
        'hubspot_id'           => 'nullable|numeric',
        'chargeback_at'        => 'required|date',
        'source_chargeback_at' => 'required|date',
        'source_timezone'      => 'required|string|max:32',
    ];

    protected static function booted(): void
    {
        static::saved(function (ChargebackFee $fee) {
            // When chargeback is saved, update the order's chargeback fee amount to sum of all of that order's chargeback fees.
            $fee->order->chargeback_fee = Money::sum(
                ...$fee->order->chargebackFees->pluck('amount')
            );

            // Autocorrect the source_chargeback_at.
            if ($fee->source_chargeback_at?->notEqualTo($fee->chargeback_at->copy()->tz($fee->source_timezone))) {
                $fee->source_chargeback_at = $fee->chargeback_at->copy()->tz($fee->source_timezone);
            }

            if (!$fee->order->refunded_at) {
                $fee->order->refunded_at = $fee->chargeback_at;
                $fee->order->source_refunded_at = $fee->source_chargeback_at;
            }

            $fee->order->save();
        });

        static::forceDeleting(function (self $fee) {
            $fee->payloads()->detach();
            $fee->transaction?->delete();
        });

        static::created(function (self $fee) {
            foreach ($fee->order->orderOffers as $orderOffer) {
                if (!$orderOffer->offer->activeJobs->count()) {
                    $activeJob = new ActiveJob([
                        'job'        => 'RecalculateOfferReport',
                        'properties' => [
                            'date' => $fee->order->purchased_at->format('Y-m-d H:i:s'),
                        ],
                        'starts_at' => now()->subDay()
                    ]);
                    $orderOffer->offer->activeJobs()->save($activeJob);
                }
            }
        });
    }

    public function sourceChargebackAt(): Attribute
    {
        return Attribute::make(
            get: function (Carbon|string|null $value, array $attributes) {
                if (empty($value)) {
                    return null;
                }

                return Carbon::parse($value, $attributes['source_timezone']);
            }
        );
    }

    public function account(): HasOneThrough
    {
        return $this->hasOneThrough(
            Account::class,
            Order::class,
            'id',
            'id',
            'order_id',
            'account_id'
        );
    }

    public function customer(): HasOneThrough
    {
        return $this->hasOneThrough(
            Customer::class,
            Order::class,
            'id',
            'id',
            'order_id',
            'customer_id'
        );
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function payloads(): MorphToMany
    {
        return $this->morphToMany(Payload::class, 'model', 'model_payload')
            ->withTimestamps();
    }

    public function transaction(): HasOne
    {
        return $this->hasOne(Transaction::class)->where('type', TransactionType::CHARGEBACK_FEE);
    }
}
