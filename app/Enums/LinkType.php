<?php

namespace App\Enums;

use App\Dto\EnumDto;
use App\Models\Account;
use App\Models\ChargebackFee;
use App\Models\Order;
use App\Models\Refund;
use App\Models\Transaction;
use App\Services\ImportService\Strategies\BuygoodsBaseStrategy;
use App\Services\ImportService\Strategies\ClickbankBaseStrategy;
use App\Services\ImportService\Strategies\DigistoreBaseStrategy;
use Illuminate\Database\Eloquent\Model;

enum LinkType: string
{
    use EnumHelpers;

    case BUYGOODS = 'buygoods';
    case CLICKBANK = 'clickbank';
    case DIGISTORE = 'digistore';

    public static function properCase(self|string $value): string
    {
        if ($value instanceof self) {
            $value = $value->value;
        }

        return match ($value) {
            self::BUYGOODS->value => 'BuyGoods',
            self::CLICKBANK->value => 'ClickBank',
            self::DIGISTORE->value => 'Digistore',
        };
    }

    public static function getList(): array
    {
        $data = collect();

        collect(self::cases())
            ->map(function ($case) use ($data) {
                $data->push(new EnumDto($case->value, self::properCase($case->value)));
            });

        return $data->all();
    }
}
