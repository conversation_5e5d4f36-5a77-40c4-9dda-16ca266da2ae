<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;
use OwenIt\Auditing\Models\Audit;
use ZipArchive;

class ArchiveAudits extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'archive:audits
                            {days?} : Days to keep audits in database before archiving to S3.
                            {--max=1 : Maximum number of days of audits to archive at once.}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Audits archive and load to S3';
    private ?string $filePath = null;
    private ?string $s3FilePath = null;
    private ?string $lastDate = null;
    private ?ZipArchive $zip = null;
    private array $ids = [];
    private int $max = 1;
    private int $daysArchived = 0;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->argument('days')) {
            Config::set('archive.audit.expires_after_days', $this->argument('days'));
        }

        if (!config('archive.audit.expires_after_days')) {
            return Command::SUCCESS;
        }

        $this->max = $this->option('max');

        $audits = Audit::query()
            ->whereBetween('created_at', [
                now()->startOfDay()->subDays(config('archive.audit.expires_after_days'))->subDays($this->max),
                now()->startOfDay()->subDays(config('archive.audit.expires_after_days'))
            ])
            ->orderByDesc('created_at');

        $count = $audits->count();

        $this->info("Archiving $count audits.");

        if ($count > 0) {
            $this->getOutput()->progressStart($count);

            foreach ($audits->cursor() as $audit) {
                if ($this->daysArchived >= $this->max) {
                    $this->info('Maximum number of days archived reached.');
                    break;
                }

                if ($this->lastDate !== $audit->created_at->toDateString()) {
                    $this->daysArchived++;
                    $this->lastDate = $audit->created_at->toDateString();
                }

                $this->getOutput()->progressAdvance();

                $zip = $this->getZip($audit);
                $filename = sprintf(
                    '%s.json.enc',
                    $audit->id
                );
                $zip->addFromString(
                    $filename,
                    Crypt::encrypt($audit->toJson())
                );
                $this->ids[] = $audit->id;
            }

            $this->zipClose($this->zip);

            $this->getOutput()->progressFinish();

            $this->info('Audits archived.');
        }

        return Command::SUCCESS;
    }

    private function filePath(): string
    {
        $zipPath = storage_path('app/zip/temp');
        $filePath = sprintf(
            'zip/temp/%s.zip',
            uniqid(time())
        );

        if (!file_exists($zipPath)) {
            mkdir($zipPath, 0777, true);
        }

        $this->filePath = $filePath;

        return $filePath;
    }

    private function getZip(Audit $audit): ?ZipArchive
    {
        if ($this->zip && $audit->created_at?->toDateString() == $this->lastDate) {
            return $this->zip;
        }

        $this->lastDate = $audit->created_at?->toDateString();

        $this->zipClose($this->zip);

        $this->ids = [];

        $this->filePath();

        $this->s3FilePath = sprintf(
            'audits-archive/%s.zip',
            $this->lastDate
        );

        if ($content = Storage::disk('s3')->get($this->s3FilePath)) {
            Storage::disk('local')->put($this->filePath, $content);
        }

        $this->zip = new ZipArchive();
        $this->zip->open(Storage::disk('local')->path($this->filePath), ZipArchive::CREATE);

        return $this->zip;
    }

    private function zipClose(?ZipArchive $zip): void
    {
        if (!$zip) {
            return;
        }

        $zip->close();

        Storage::disk('s3')->put($this->s3FilePath, Storage::disk('local')->get($this->filePath));

        if (Storage::disk('s3')->exists($this->s3FilePath)) {
            Storage::disk('local')->delete($this->filePath);
            Audit::query()
                ->whereIn('id', $this->ids)
                ->delete();
        } else {
            logger('Archive audit: ' . $this->s3FilePath . ' not saved');
        }
    }
}
