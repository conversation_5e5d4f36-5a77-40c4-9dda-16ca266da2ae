<?php

namespace App\Console\Commands;

use App\Models\Address;
use App\Traits\CommandHasDateRange;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Crypt;
use Throwable;

class RepairAddressEncrypted extends Command
{
    use CommandHasDateRange;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'repair:address-encrypted
                            {start? : The start date. If empty, default is 30 days ago.}
                            {end? : The end date. If empty, default is today.}
                            {--address= : Address ID to repair.}
                            {--min-id= : Min id for processing.}
                            {--max-id= : Max id for processing.}
                            {--debug : Show debug information.}';

    private int $processed = 0;
    private array $errors = [];

    protected $description = 'Repair address double encrypted.';

    public function handle()
    {
        $this->setDateRange();

        $builder = Address::query();

        if ($this->argument('start')) {
            $builder->where('created_at', '>=', $this->startsAt);
        }

        if ($this->argument('end')) {
            $builder->where('created_at', '<=', $this->endsAt);
        }

        if ($this->option('min-id')) {
            $builder->where('id', '>=', $this->option('min-id'));
        }

        if ($this->option('max-id')) {
            $builder->where('id', '<=', $this->option('max-id'));
        }

        if ($this->option('address')) {
            $builder->where('id', $this->option('address'));
        }

        $last_id = cache('repair-address-encrypted-last-id');

        if ($last_id && !$this->option('min-id') && $this->confirm("Continue from ID $last_id?")) {
            $builder->where('id', '>', $last_id);
        }

        $count = $builder->count();

        $this->info("Number of addresses to check: " . $count);
        $this->getOutput()?->progressStart($count);

        $builder->chunk(1000, function (Collection $addresses) {
            $addresses->each(function (Address $address) {
                $processed = false;

                foreach (['address', 'address2', 'address3'] as $field) {
                    try {
                        if (!$address->$field) {
                            continue;
                        }

                        $data = Crypt::decrypt($address->$field, false);
                        $processed = true;
                        $address->$field = $data;
                    } catch (Throwable $e) {
                        if (!isset($this->errors[$e->getMessage()])) {
                            $this->errors[$e->getMessage()] = 0;
                        }

                        $this->errors[$e->getMessage()]++;

                        if ($this->option('debug')) {
                            $this->error($e->getMessage());
                        }
                    }
                }

                if ($processed) {
                    $address->saveQuietly();
                    $this->processed++;
                }

                // Cache the most recent ID so we can restart from there if the command fails.
                cache(['repair-address-encrypted-last-id' => $address->id], now()->addDays(3));

                $this->getOutput()?->progressAdvance();
            });
        });

        $this->getOutput()?->progressFinish();

        foreach ($this->errors as $text => $total) {
            $this->comment(sprintf('%s: %s', $text, $total));
        }

        $this->info("Repaired $this->processed addresses.");

        return Command::SUCCESS;
    }
}
