<?php

namespace App\Console\Commands;

use App\Enums\AccountSource;
use App\Enums\OrderStatus;
use App\Enums\PayloadSource;
use App\Events\OrderCreated;
use App\Listeners\CommittedCoachesLead;
use App\Models\Order;
use App\Traits\CommandHasDateRange;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class RepairCommittedCoachesLeads extends Command
{
    use CommandHasDateRange;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'repair:committed-coaches-leads
                            {start? : The start date. If empty, default is 7 days ago.}
                            {end? : The end date. If empty, default is today.}
                            {--order=* : The order ID or list of IDs.}
                            {--source-ref= : The order source reference.}
                            {--account= : The account ID.}
                            {--without-date-filter : Skip default filter by date.}
                            {--limit= : Limit the number of orders to process.}
                            {--debug : Display debug information without sending leads.}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Repair missing CommittedCoaches leads by scanning orders and triggering leads for orders without existing payloads.';

    private int $processed = 0;
    private int $sent = 0;
    private int $skipped = 0;
    private array $errors = [];

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->setDateRange();

        if (!$this->argument('start')) {
            $this->startsAt = $this->startsAt->subDays(7);
        }

        if (!config('services.committed_coaches.webhook_enabled')) {
            $this->error('CommittedCoaches webhook is not enabled. Please check your configuration.');

            return Command::FAILURE;
        }

        $this->info('Starting CommittedCoaches leads repair process...');
        $this->info("Date range: {$this->startsAt->format('Y-m-d')} to {$this->endsAt->format('Y-m-d')}");

        if ($this->option('debug')) {
            $this->warn('Running in DEBUG mode - no leads will be sent.');
        }

        $query = $this->buildQuery();
        $totalCount = (clone $query)->count();

        if ($totalCount === 0) {
            $this->info('No orders found that need CommittedCoaches leads.');

            return Command::SUCCESS;
        }

        $this->info("Found {$totalCount} orders that may need CommittedCoaches leads.");

        if ($this->option('limit')) {
            $query->limit($this->option('limit'));
            $this->info("Processing limited to {$this->option('limit')} orders.");
        }

        $orders = $query->get();
        $progressBar = $this->output->createProgressBar($orders->count());
        $progressBar->start();

        foreach ($orders as $order) {
            $this->processOrder($order);
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        $this->displaySummary();

        return Command::SUCCESS;
    }

    private function buildQuery(): Builder
    {
        $query = Order::query()
            ->with(['account', 'offers.products.skus', 'customer'])
            ->where('status', OrderStatus::COMPLETED->value)
            ->whereDoesntHave('payloads', function (Builder $query) {
                $query->where('source', PayloadSource::COMMITTED_COACHES_LEAD->value);
            })
            ->whereHas('account', function (Builder $query) {
                $query->where('source', '!=', AccountSource::COMMITTED_COACHES->value);
            })
            ->orderBy('purchased_at', 'desc');

        // Apply filters
        if ($this->option('order')) {
            $orderIds = collect($this->option('order'))
                ->map(fn($id) => explode(',', $id))
                ->flatten()
                ->filter();
            $query->whereIn('id', $orderIds);
        } elseif ($this->option('source-ref')) {
            $query->where('source_ref', $this->option('source-ref'));
        } elseif (!$this->option('without-date-filter')) {
            $query->whereBetween('purchased_at', [
                $this->startsAt->startOfDay(),
                $this->endsAt->endOfDay(),
            ]);
        }

        if ($this->option('account')) {
            $query->where('account_id', $this->option('account'));
        }

        return $query;
    }

    private function processOrder(Order $order): void
    {
        $this->processed++;

        try {
            // Create the listener instance
            $listener = new CommittedCoachesLead();

            // Check if this order would be valid for CommittedCoaches leads
            if (!$this->shouldProcessOrder($order, $listener)) {
                $this->skipped++;

                if ($this->option('debug')) {
                    $this->line("Skipped order {$order->id} ({$order->source_ref}) - doesn't meet criteria");
                }

                return;
            }

            if ($this->option('debug')) {
                $this->line("Would send lead for order {$order->id} ({$order->source_ref})");
                $this->sent++;
                return;
            }

            // Create and dispatch the event
            $event = new OrderCreated($order);
            $listener->handle($event);

            $this->sent++;

            if ($this->option('debug')) {
                $this->line("Sent lead for order {$order->id} ({$order->source_ref})");
            }
        } catch (\Exception $e) {
            $this->errors[] = [
                'order_id'   => $order->id,
                'source_ref' => $order->source_ref,
                'error'      => $e->getMessage(),
            ];

            if ($this->option('debug')) {
                $this->error("Error processing order {$order->id}: {$e->getMessage()}");
            }
        }
    }

    private function shouldProcessOrder(Order $order, CommittedCoachesLead $listener): bool
    {
        // Check if order status is completed (already filtered in query, but double-check)
        if (!OrderStatus::COMPLETED->is($order->status)) {
            return false;
        }

        // Check if it's not from CommittedCoaches account (already filtered in query)
        if (AccountSource::COMMITTED_COACHES->is($order->account->source)) {
            return false;
        }

        // Check if order has valid products using the listener's logic
        if (!$listener->hasValidProducts($order)) {
            return false;
        }

        // Don't process orders older than a week (matches listener logic)
        if ($order->purchased_at->isBefore(now()->subWeek())) {
            return false;
        }

        return true;
    }

    private function displaySummary(): void
    {
        $this->info('=== Repair Summary ===');
        $this->info("Processed: {$this->processed} orders");
        $this->info("Sent: {$this->sent} leads");
        $this->info("Skipped: {$this->skipped} orders");
        $this->info("Errors: " . count($this->errors));

        if (!empty($this->errors)) {
            $this->newLine();
            $this->error('Errors encountered:');

            foreach ($this->errors as $error) {
                $this->error("Order {$error['order_id']} ({$error['source_ref']}): {$error['error']}");
            }
        }

        if ($this->option('debug')) {
            $this->newLine();
            $this->warn('This was a DEBUG run - no leads were actually sent.');
        }
    }
}
