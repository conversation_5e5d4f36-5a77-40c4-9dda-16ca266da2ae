<?php

namespace App\Console\Commands;

use App\Enums\MarketingPlatform;
use App\Models\Customer;
use App\Models\MarketingList;
use App\Models\User;
use App\Traits\CommandHasDateRange;
use App\Traits\GeneratesCsvReports;
use App\Traits\MarketingListHelper;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class RepairMarketingListDiscrepancy extends Command
{
    use CommandHasDateRange;
    use GeneratesCsvReports;
    use MarketingListHelper;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'repair:marketing-list-discrepancy
                            {start? : The start date. If empty, default is 30 days ago.}
                            {end? : The end date. If empty, default is today.}
                            {--limit=25 : Limit the number of samples shown in the results table.}
                            {--csv : Generate CSV reports for each platform and upload to S3.}
                            {--email : Send email notification with report links (requires --csv).}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Identify customers that should have been sent to Aweber or GetResponse but weren\'t';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->setDateRange();

        // If no start date provided, default to 30 days ago
        if (!$this->argument('start')) {
            $this->startsAt = now()->subDays(30)->startOfDay();
        }

        $limit = (int)$this->option('limit');
        $generateCsv = $this->option('csv');
        $sendEmail = $this->option('email');
        $user = null;

        // Get user email if CSV or email options are used
        if ($generateCsv || $sendEmail) {
            if ($sendEmail && !$generateCsv) {
                $this->error('The --email option requires --csv to be enabled.');

                return Command::FAILURE;
            }

            $email = $this->askForUserEmail();
            $user = $this->getUserByEmail($email);

            if (!$user) {
                $this->error("User with email '{$email}' not found.");

                return Command::FAILURE;
            }

            $this->info("Reports will be generated for user: {$user->name} ({$user->email})");
            $this->newLine();
        }

        $this->info(
            "Analyzing marketing list discrepancies from {$this->startsAt->format('Y-m-d')} to {$this->endsAt->format('Y-m-d')}"
        );
        $this->info("Sample limit: {$limit}");
        $this->newLine();

        // Get customers with valid zerobounce status and orders in date range
        $customersQuery = Customer::query()
            ->where('zerobounce_status', 'valid')
            ->whereHas('orders', function ($query) {
                $query->whereBetween('purchased_at', [$this->startsAt, $this->endsAt]);
            })
            ->with([
                'orders' => function ($query) {
                    $query->whereBetween('purchased_at', [$this->startsAt, $this->endsAt])
                        ->with(['offers.products', 'account.brand', 'affiliate', 'account'])
                        ->orderBy('purchased_at', 'desc');
                },
            ]);

        $this->info('Analyzing customers...');
        $progressBar = $this->output->createProgressBar($customersQuery->count());
        $progressBar->start();

        $aweberDiscrepancies = collect();
        $getResponseDiscrepancies = collect();
        $aweberAllDiscrepancies = collect(); // For CSV - all discrepancies
        $getResponseAllDiscrepancies = collect(); // For CSV - all discrepancies
        $aweberTotal = 0;
        $getResponseTotal = 0;

        $customersQuery->chunk(
            100,
            function ($customers) use (
                &$aweberDiscrepancies,
                &$getResponseDiscrepancies,
                &$aweberAllDiscrepancies,
                &$getResponseAllDiscrepancies,
                &$aweberTotal,
                &$getResponseTotal,
                $limit,
                $progressBar,
                $generateCsv
            ) {
                foreach ($customers as $customer) {
                    $this->analyzeCustomer(
                        $customer,
                        $aweberDiscrepancies,
                        $getResponseDiscrepancies,
                        $aweberAllDiscrepancies,
                        $getResponseAllDiscrepancies,
                        $aweberTotal,
                        $getResponseTotal,
                        $limit,
                        $generateCsv
                    );
                    $progressBar->advance();
                }
            }
        );

        $progressBar->finish();
        $this->newLine(2);

        // Display results
        $this->displayResults($aweberDiscrepancies, $getResponseDiscrepancies, $aweberTotal, $getResponseTotal);

        // Generate CSV reports if requested
        if ($generateCsv && $user) {
            $this->generateCsvReports($aweberAllDiscrepancies, $getResponseAllDiscrepancies, $user, $sendEmail);
        }

        return Command::SUCCESS;
    }

    /**
     * Analyze a customer for marketing list discrepancies
     */
    private function analyzeCustomer(
        Customer $customer,
        Collection &$aweberDiscrepancies,
        Collection &$getResponseDiscrepancies,
        Collection &$aweberAllDiscrepancies,
        Collection &$getResponseAllDiscrepancies,
        int &$aweberTotal,
        int &$getResponseTotal,
        int $limit,
        bool $generateCsv
    ): void {
        // Check Aweber
        $aweberListData = $this->getMarketingList($customer, MarketingPlatform::AWEBER);

        if ($aweberListData && !$this->customerHasMarketingListRecord(
                $customer,
                MarketingPlatform::AWEBER,
                $aweberListData->listIds
            )) {
            $aweberTotal++;

            $discrepancyRecord = $this->createDiscrepancyRecord(
                $customer,
                MarketingPlatform::AWEBER,
                $aweberListData->listIds[0]
            );

            if ($aweberDiscrepancies->count() < $limit) {
                $aweberDiscrepancies->push($discrepancyRecord);
            }

            if ($generateCsv) {
                $aweberAllDiscrepancies->push($this->createCsvRecord($customer));
            }
        }

        // Check GetResponse
        $getResponseListData = $this->getMarketingList($customer, MarketingPlatform::GET_RESPONSE);

        if ($getResponseListData && !$this->customerHasMarketingListRecord(
                $customer,
                MarketingPlatform::GET_RESPONSE,
                $getResponseListData->listIds
            )) {
            $getResponseTotal++;

            $discrepancyRecord = $this->createDiscrepancyRecord(
                $customer,
                MarketingPlatform::GET_RESPONSE,
                $getResponseListData->listIds[0]
            );

            if ($getResponseDiscrepancies->count() < $limit) {
                $getResponseDiscrepancies->push($discrepancyRecord);
            }

            if ($generateCsv) {
                $getResponseAllDiscrepancies->push($this->createCsvRecord($customer));
            }
        }
    }

    /**
     * Check if customer has a marketing list record for the given platform and list IDs
     */
    private function customerHasMarketingListRecord(
        Customer $customer,
        MarketingPlatform $platform,
        array $listIds
    ): bool {
        return $customer->marketingLists()
            ->where('platform', $platform->value)
            ->whereIn('list_id', $listIds)
            ->exists();
    }

    /**
     * Create a discrepancy record for display
     */
    private function createDiscrepancyRecord(
        Customer $customer,
        MarketingPlatform $platform,
        string $listId
    ): array {
        $marketingList = MarketingList::where('list_id', $listId)
            ->where('platform', $platform->value)
            ->first();

        $appUrl = rtrim(config('app.url'), '/');
        $novaPath = config('nova.path', 'nova');

        return [
            'customer_id'         => $customer->id,
            'customer_link'       => $appUrl . '/' . $novaPath . '/resources/customers/' . $customer->id,
            'marketing_list_id'   => $marketingList?->id ?? 'N/A',
            'marketing_list_link' => $marketingList ? $appUrl . '/' . $novaPath . '/resources/marketing-lists/' . $marketingList->id : 'N/A',
            'platform'            => $platform->value,
        ];
    }

    /**
     * Display the results in tables
     */
    private function displayResults(
        Collection $aweberDiscrepancies,
        Collection $getResponseDiscrepancies,
        int $aweberTotal,
        int $getResponseTotal
    ): void {
        if ($aweberDiscrepancies->isNotEmpty()) {
            $this->info('AWEBER DISCREPANCIES (Sample):');
            $this->table(
                ['Customer ID', 'Customer Link', 'Marketing List ID', 'Marketing List Link'],
                $aweberDiscrepancies->map(function ($item) {
                    return [
                        $item['customer_id'],
                        $item['customer_link'],
                        $item['marketing_list_id'],
                        $item['marketing_list_link'],
                    ];
                })->toArray()
            );
            $this->newLine();
        }

        if ($getResponseDiscrepancies->isNotEmpty()) {
            $this->info('GETRESPONSE DISCREPANCIES (Sample):');
            $this->table(
                ['Customer ID', 'Customer Link', 'Marketing List ID', 'Marketing List Link'],
                $getResponseDiscrepancies->map(function ($item) {
                    return [
                        $item['customer_id'],
                        $item['customer_link'],
                        $item['marketing_list_id'],
                        $item['marketing_list_link'],
                    ];
                })->toArray()
            );
            $this->newLine();
        }

        // Display totals
        $this->info('TOTAL DISCREPANCIES:');
        $this->line("Aweber: {$aweberTotal}");
        $this->line("GetResponse: {$getResponseTotal}");
        $this->line("Combined Total: " . ($aweberTotal + $getResponseTotal));

        if ($aweberTotal === 0 && $getResponseTotal === 0) {
            $this->info('No discrepancies found in the specified date range.');
        }
    }

    /**
     * Create CSV record for customer
     */
    private function createCsvRecord(Customer $customer): array
    {
        return [
            $customer->first_name ?? '',
            $customer->last_name ?? '',
            $customer->email ?? '',
            $customer->created_at ? $customer->created_at->format('Y-m-d H:i:s') : '',
        ];
    }

    /**
     * Generate CSV reports for both platforms
     */
    private function generateCsvReports(
        Collection $aweberDiscrepancies,
        Collection $getResponseDiscrepancies,
        User $user,
        bool $sendEmail
    ): void {
        $this->newLine();
        $this->info('Generating CSV reports...');

        $headers = ['First Name', 'Last Name', 'Email', 'Date Created'];
        $reportLinks = [];

        // Generate Aweber CSV
        if ($aweberDiscrepancies->isNotEmpty()) {
            $filename = $this->generateFilenameWithDateRange('aweber_missing_customers');
            $url = $this->generateAndUploadCsvReport($aweberDiscrepancies, $headers, $filename, $user, false);

            if ($url) {
                $reportLinks['Aweber Missing Customers'] = $url;
            }
        }

        // Generate GetResponse CSV
        if ($getResponseDiscrepancies->isNotEmpty()) {
            $filename = $this->generateFilenameWithDateRange('getresponse_missing_customers');
            $url = $this->generateAndUploadCsvReport($getResponseDiscrepancies, $headers, $filename, $user, false);

            if ($url) {
                $reportLinks['GetResponse Missing Customers'] = $url;
            }
        }

        // Send email with all report links if requested
        if ($sendEmail && !empty($reportLinks)) {
            $this->sendMultipleReportsEmail($user, $reportLinks);
        }

        if (empty($reportLinks)) {
            $this->info('No CSV reports generated (no discrepancies found).');
        }
    }

    /**
     * Send email with multiple report links
     */
    private function sendMultipleReportsEmail(User $user, array $reportLinks): void
    {
        if (count($reportLinks) === 1) {
            $reportName = array_keys($reportLinks)[0];
            $reportUrl = array_values($reportLinks)[0];
            $this->sendReportEmail($user, $reportName, $reportUrl);
        } else {
            // Send email with multiple links
            $firstReportName = array_keys($reportLinks)[0];
            $firstReportUrl = array_shift($reportLinks);

            $user->notify(
                new \App\Notifications\ReportLinkNotification(
                    'Marketing List Discrepancy Reports',
                    $firstReportUrl,
                    $reportLinks
                )
            );

            $this->info("Report email with multiple links sent to {$user->email}");
        }
    }
}
