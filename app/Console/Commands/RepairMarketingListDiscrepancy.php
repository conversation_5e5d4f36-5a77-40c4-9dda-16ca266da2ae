<?php

namespace App\Console\Commands;

use App\Enums\MarketingPlatform;
use App\Models\Customer;
use App\Models\MarketingList;
use App\Models\Order;
use App\Traits\CommandHasDateRange;
use App\Traits\MarketingListHelper;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class RepairMarketingListDiscrepancy extends Command
{
    use CommandHasDateRange;
    use MarketingListHelper;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'repair:marketing-list-discrepancy
                            {start? : The start date. If empty, default is 30 days ago.}
                            {end? : The end date. If empty, default is today.}
                            {--limit=25 : Limit the number of samples shown in the results table.}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Identify customers that should have been sent to Aweber or GetResponse but weren\'t';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->setDateRange();

        // If no start date provided, default to 30 days ago
        if (!$this->argument('start')) {
            $this->startsAt = now()->subDays(30)->startOfDay();
        }

        $limit = (int) $this->option('limit');

        $this->info("Analyzing marketing list discrepancies from {$this->startsAt->format('Y-m-d')} to {$this->endsAt->format('Y-m-d')}");
        $this->info("Sample limit: {$limit}");
        $this->newLine();

        // Get customers with valid zerobounce status and orders in date range
        $customersQuery = Customer::query()
            ->where('zerobounce_status', 'valid')
            ->whereHas('orders', function ($query) {
                $query->whereBetween('purchased_at', [$this->startsAt, $this->endsAt]);
            })
            ->with(['orders' => function ($query) {
                $query->whereBetween('purchased_at', [$this->startsAt, $this->endsAt])
                    ->with(['offers.products', 'account.brand', 'affiliate', 'account'])
                    ->orderBy('purchased_at', 'desc');
            }]);

        $this->info('Analyzing customers...');
        $progressBar = $this->output->createProgressBar($customersQuery->count());
        $progressBar->start();

        $aweberDiscrepancies = collect();
        $getResponseDiscrepancies = collect();
        $aweberTotal = 0;
        $getResponseTotal = 0;

        $customersQuery->chunk(100, function ($customers) use (&$aweberDiscrepancies, &$getResponseDiscrepancies, &$aweberTotal, &$getResponseTotal, $limit, $progressBar) {
            foreach ($customers as $customer) {
                $this->analyzeCustomer($customer, $aweberDiscrepancies, $getResponseDiscrepancies, $aweberTotal, $getResponseTotal, $limit);
                $progressBar->advance();
            }
        });

        $progressBar->finish();
        $this->newLine(2);

        // Display results
        $this->displayResults($aweberDiscrepancies, $getResponseDiscrepancies, $aweberTotal, $getResponseTotal);

        return Command::SUCCESS;
    }

    /**
     * Analyze a customer for marketing list discrepancies
     */
    private function analyzeCustomer(
        Customer $customer,
        Collection &$aweberDiscrepancies,
        Collection &$getResponseDiscrepancies,
        int &$aweberTotal,
        int &$getResponseTotal,
        int $limit
    ): void {
        foreach ($customer->orders as $order) {
            // Check Aweber
            $aweberListData = $this->getMarketingList($customer, MarketingPlatform::AWEBER);
            if ($aweberListData && !$this->customerHasMarketingListRecord($customer, MarketingPlatform::AWEBER, $aweberListData->listIds)) {
                $aweberTotal++;
                if ($aweberDiscrepancies->count() < $limit) {
                    $aweberDiscrepancies->push($this->createDiscrepancyRecord($customer, $order, MarketingPlatform::AWEBER, $aweberListData->listIds[0]));
                }
            }

            // Check GetResponse
            $getResponseListData = $this->getMarketingList($customer, MarketingPlatform::GET_RESPONSE);
            if ($getResponseListData && !$this->customerHasMarketingListRecord($customer, MarketingPlatform::GET_RESPONSE, $getResponseListData->listIds)) {
                $getResponseTotal++;
                if ($getResponseDiscrepancies->count() < $limit) {
                    $getResponseDiscrepancies->push($this->createDiscrepancyRecord($customer, $order, MarketingPlatform::GET_RESPONSE, $getResponseListData->listIds[0]));
                }
            }

            // Only check the most recent order per customer
            break;
        }
    }

    /**
     * Check if customer has a marketing list record for the given platform and list IDs
     */
    private function customerHasMarketingListRecord(Customer $customer, MarketingPlatform $platform, array $listIds): bool
    {
        return $customer->marketingLists()
            ->where('platform', $platform->value)
            ->whereIn('list_id', $listIds)
            ->exists();
    }

    /**
     * Create a discrepancy record for display
     */
    private function createDiscrepancyRecord(Customer $customer, Order $order, MarketingPlatform $platform, string $listId): array
    {
        $marketingList = MarketingList::where('list_id', $listId)
            ->where('platform', $platform->value)
            ->first();

        $appUrl = rtrim(config('app.url'), '/');
        $novaPath = config('nova.path', 'nova');

        return [
            'customer_id' => $customer->id,
            'customer_link' => $appUrl . '/' . $novaPath . '/resources/customers/' . $customer->id,
            'marketing_list_id' => $marketingList?->id ?? 'N/A',
            'marketing_list_link' => $marketingList ? $appUrl . '/' . $novaPath . '/resources/marketing-lists/' . $marketingList->id : 'N/A',
            'platform' => $platform->value,
        ];
    }

    /**
     * Display the results in tables
     */
    private function displayResults(
        Collection $aweberDiscrepancies,
        Collection $getResponseDiscrepancies,
        int $aweberTotal,
        int $getResponseTotal
    ): void {
        if ($aweberDiscrepancies->isNotEmpty()) {
            $this->info('AWEBER DISCREPANCIES (Sample):');
            $this->table(
                ['Customer ID', 'Customer Link', 'Marketing List ID', 'Marketing List Link'],
                $aweberDiscrepancies->map(function ($item) {
                    return [
                        $item['customer_id'],
                        $item['customer_link'],
                        $item['marketing_list_id'],
                        $item['marketing_list_link'],
                    ];
                })->toArray()
            );
            $this->newLine();
        }

        if ($getResponseDiscrepancies->isNotEmpty()) {
            $this->info('GETRESPONSE DISCREPANCIES (Sample):');
            $this->table(
                ['Customer ID', 'Customer Link', 'Marketing List ID', 'Marketing List Link'],
                $getResponseDiscrepancies->map(function ($item) {
                    return [
                        $item['customer_id'],
                        $item['customer_link'],
                        $item['marketing_list_id'],
                        $item['marketing_list_link'],
                    ];
                })->toArray()
            );
            $this->newLine();
        }

        // Display totals
        $this->info('TOTAL DISCREPANCIES:');
        $this->line("Aweber: {$aweberTotal}");
        $this->line("GetResponse: {$getResponseTotal}");
        $this->line("Combined Total: " . ($aweberTotal + $getResponseTotal));

        if ($aweberTotal === 0 && $getResponseTotal === 0) {
            $this->info('No discrepancies found in the specified date range.');
        }
    }
}
