<?php

namespace App\Console\Commands;

use App\Exceptions\GetResponseException;
use App\Models\Customer;
use App\Models\MarketingList;
use App\Models\MarketingListLimit;
use App\Services\GetResponse\GetResponseClient;
use App\Traits\CustomerGenderize;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use League\Csv\Reader;
use League\Csv\Statement;

class SendCustomersToGetResponse extends Command
{
    use CustomerGenderize;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'customer:send-to-get-response
                            {id? : Marketing list id}
                            {--info : Show info instead of sending data to GetResponse.}
                            {--limit=: limit customers per start}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send customers from csv to GetResponse';
    private string $folder = 'maropost-migration';
    private string $disk = 's3';

    private array $domainGroups = [];
    private ?MarketingList $currentMarketingList = null;
    private array $groupCounters = [];
    private array $cachedGroupCounts = [];
    private ?bool $allLimitsReachedCache = null;
    private array $groupLimitNotified = [];
    private int $limit = 0;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            // Log command execution start with timestamp
            $this->logDebug('Command execution started', [
                'limit'          => $this->option('limit'),
                'id'             => $this->argument('id'),
                'environment'    => app()->environment(),
                'php_version'    => PHP_VERSION,
                'memory_usage'   => $this->getMemoryUsage(),
                'scheduler_info' => $this->getSchedulerInfo(),
            ]);

            // Check for any locks that might be preventing execution
            $this->checkForLocks();

            // Test S3 connection
            $this->testS3Connection();

            $this->limit = (int)$this->option('limit') ?? 0;

            // Check if S3 disk is configured properly
            if (!Storage::disk($this->disk)->exists($this->folder)) {
                $this->logDebug('Folder does not exist or S3 connection issue', [
                    'folder' => $this->folder,
                    'disk'   => $this->disk,
                ]);
                Storage::disk($this->disk)->makeDirectory($this->folder);
            }

            $files = Storage::disk($this->disk)->files($this->folder);
            $marketingListId = $this->argument('id');

            $this->info('Starting GetResponse customer import process');
            $this->info('Found ' . count($files) . ' files in folder ' . $this->folder);

            if ($marketingListId) {
                $this->info('Filtering for marketing list ID: ' . $marketingListId);
            }

            $progressBar = $this->output->createProgressBar(count($files));
            $progressBar->start();

            foreach ($files as $file) {
                $fileinfo = pathinfo($file);
                $filename = $fileinfo['filename'];

                // Skip if ID argument is provided and doesn't match the filename
                if ($marketingListId && (int)$filename !== (int)$marketingListId) {
                    $progressBar->advance();
                    continue;
                }

                try {
                    $marketingList = MarketingList::where('id', (int)$filename)->firstOrFail();
                    $progressBar->advance();

                    $this->line('');
                    $this->info(
                        'Processing marketing list: ' . $marketingList->name . ' (ID: ' . $marketingList->id . ')'
                    );

                    // Load domain groups for this marketing list
                    $this->loadDomainGroupsForMarketingList($marketingList);

                    if (!$this->option('info')) {
                        $this->processFile($marketingList, $fileinfo['basename']);
                    } else {
                        $this->showInfo($marketingList, $fileinfo['basename']);
                    }
                } catch (ModelNotFoundException $e) {
                    $progressBar->advance();
                    $this->error("\nMarketing list not found for ID: " . $filename);
                } catch (\Exception $e) {
                    $progressBar->advance();
                    $this->error("\nError processing file $file: " . $e->getMessage());
                    $this->error($e->getTraceAsString());
                }
            }

            $progressBar->finish();
            $this->line('');
            $this->info('GetResponse customer import process completed');

            // Log command execution completion
            $this->logDebug('Command execution completed', [
                'files_processed' => count($files),
                'memory_usage'    => $this->getMemoryUsage(),
            ]);
        } catch (\Exception $e) {
            // Log any uncaught exceptions
            $this->logDebug('Uncaught exception in command execution', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file'  => $e->getFile(),
                'line'  => $e->getLine(),
            ]);

            // Re-throw the exception to maintain normal error handling
            throw $e;
        }

        return Command::SUCCESS;
    }

    private function showInfo($marketingList, $file)
    {
        $this->info('Analyzing file: ' . $this->folder . '/' . $file);

        // Ensure domain groups are loaded for this marketing list
        if (empty($this->domainGroups) || $this->currentMarketingList?->id !== $marketingList->id) {
            $this->loadDomainGroupsForMarketingList($marketingList);
        }

        // If no domain groups are configured, skip processing
        if (empty($this->domainGroups)) {
            $this->error("No domain groups configured for marketing list {$marketingList->id}. Cannot analyze file.");
            return;
        }

        $stream = Storage::disk($this->disk)->get($this->folder . '/' . $file);
        $csv = Reader::createFromString($stream);
        $csv->setHeaderOffset(0);
        $records = $csv->getRecords();

        // Initialize group counters from cache
        $this->initGroupCountersFromCache();

        // Count total records first
        $recordCount = iterator_count($csv->getRecords());
        $this->info('Total records in CSV: ' . $recordCount);

        // Reset the reader
        $csv = Reader::createFromString($stream);
        $csv->setHeaderOffset(0);
        $records = $csv->getRecords();

        $progressBar = $this->output->createProgressBar($recordCount);
        $progressBar->start();

        $validCustomers = 0;
        $existingInList = 0;
        $domainGroupCounts = [];
        $notFoundCount = 0;
        $errorCount = 0;

        foreach ($records as $record) {
            $progressBar->advance();

            try {
                $customer = Customer::where(['email' => $record['email']])->firstOrFail();
                $validCustomers++;

                $hasMarketingList = $customer->marketingLists()->where(
                    'marketing_lists.id',
                    $marketingList->id
                )->exists();

                if ($hasMarketingList) {
                    $existingInList++;
                    continue;
                }

                $domain = strtolower(substr(strrchr($record['email'], "@"), 1));
                $group = $this->getGroupByDomain($domain);

                if (!$group) {
                    continue;
                }

                if (!isset($this->groupCounters[$group])) {
                    $this->groupCounters[$group] = 0;
                }

                $this->groupCounters[$group]++;

                if (!isset($domainGroupCounts[$group])) {
                    $domainGroupCounts[$group] = 0;
                }
                $domainGroupCounts[$group]++;
            } catch (ModelNotFoundException $e) {
                $notFoundCount++;
                $this->error("\nCustomer not found for email: " . ($record['email'] ?? 'unknown'));
            } catch (\Exception $e) {
                $errorCount++;
                $this->error("\nError processing record: " . $e->getMessage());
            }
        }

        $progressBar->finish();
        $this->line('');

        $this->info('Analysis results:');
        $this->info('- Valid customers found: ' . $validCustomers);
        $this->info('- Customers already in list: ' . $existingInList);
        $this->info('- Customers to process: ' . ($validCustomers - $existingInList));
        $this->info('- Customers not found: ' . $notFoundCount);
        $this->info('- Processing errors: ' . $errorCount);

        $this->info('Domain group distribution:');

        foreach ($domainGroupCounts as $group => $count) {
            $limit = $this->domainGroups[$group]['limit'];
            $domains = implode(', ', $this->domainGroups[$group]['domains']);
            $this->info("- Group '$group' ($domains): $count customers (limit: $limit per day)");
        }

        $days = 0;

        foreach ($this->groupCounters as $group => $count) {
            $d = ceil($count / $this->domainGroups[$group]['limit']);

            if ($d > $days) {
                $days = $d;
            }
        }

        $this->info(
            'All customers will be sent to marketing list "' . $marketingList->name . '" (ID: ' . $marketingList->list_id . ') in approximately ' . $days . ' days'
        );
    }

    private function processFile($marketingList, $file): void
    {
        $this->logDebug('Starting to process file', [
            'marketing_list'    => $marketingList->name,
            'marketing_list_id' => $marketingList->id,
            'file'              => $file,
        ]);

        // Ensure domain groups are loaded for this marketing list
        if (empty($this->domainGroups) || $this->currentMarketingList?->id !== $marketingList->id) {
            $this->loadDomainGroupsForMarketingList($marketingList);
        }

        // If no domain groups are configured, skip processing
        if (empty($this->domainGroups)) {
            $this->error(
                "No domain groups configured for marketing list {$marketingList->id}. Skipping file processing."
            );
            return;
        }

        // Reset memoization at the beginning of processing
        $this->cachedGroupCounts = [];
        $this->allLimitsReachedCache = null;
        $this->groupLimitNotified = [];

        $this->info('Processing file: ' . $this->folder . '/' . $file);

        $stream = Storage::disk($this->disk)->get($this->folder . '/' . $file);
        $csv = Reader::createFromString($stream);
        $csv->setHeaderOffset(0);

        $offset = cache('offset-record-count') ?? 0;
        $this->info('Starting from offset: ' . $offset);

        // Initialize group counters from cache
        $this->initGroupCountersFromCache();

        // Show current domain group usage before processing
        $this->info('Current domain group usage:');

        foreach ($this->groupCounters as $group => $count) {
            $limit = $this->domainGroups[$group]['limit'];
            $domains = implode(', ', $this->domainGroups[$group]['domains']);
            $this->info("- Group '$group' ($domains): $count/$limit");

            // Pre-populate notification status based on whether limit is already reached
            $this->groupLimitNotified[$group] = ($count >= $limit);
        }

        // Count total records first
        $totalRecords = iterator_count($csv->getRecords());

        // Reset the reader
        $csv = Reader::createFromString($stream);
        $csv->setHeaderOffset(0);

        if ($offset) {
            $statement = (new Statement())->offset($offset);
            $records = $statement->process($csv);
        } else {
            $records = $csv->getRecords();
        }

        $progressBar = $this->output->createProgressBar($totalRecords - $offset);
        $progressBar->start();

        $offsetInc = true;
        $processedCount = 0;
        $sentCount = 0;
        $skippedCount = 0;
        $errorCount = 0;

        foreach ($records as $record) {
            $progressBar->advance();
            $processedCount++;

            if ($this->limit && $sentCount >= $this->limit) {
                break;
            }
            // Show sent count every 50 records
            if ($processedCount % 50 === 0) {
                $this->info("\nSent $sentCount customers to GetResponse so far...");
            }

            if ($this->allGroupLimitsReached()) {
                $this->info("\nAll domain group limits reached for today. Stopping processing.");
                break;
            }

            try {
                $customer = Customer::where(['email' => $record['email']])->first();

                if (!$customer) {
                    $customer = Customer::create([
                        'email'      => $record['email'],
                        'first_name' => $record['first_name'],
                        'last_name'  => $record['last_name'],
                    ]);
                }

                $hasMarketingList = $customer->marketingLists()->where(
                    'marketing_lists.id',
                    $marketingList->id
                )->exists();

                if ($hasMarketingList && $offsetInc) {
                    $skippedCount++;
                    $offset++;
                    continue;
                }

                $offsetInc = false;

                $domain = strtolower(substr(strrchr($record['email'], "@"), 1));

                if (!$this->checkGroupLimit($domain)) {
                    $skippedCount++;
                    continue;
                }

                if (!$customer->gender) {
                    $this->getCustomerGender($customer);
                }
                sleep(rand(1, 10));
                $this->sendCustomerToGetResponse($marketingList, $customer);
                $sentCount++;
            } catch (\Exception $e) {
                $errorCount++;
                $this->error("\nError processing record: " . $e->getMessage());
            }

            $offset++;

            // Update cache more frequently
            if ($processedCount % 100 === 0) {
                cache(['offset-record-count' => $offset], now()->addDays(5));
                $this->allLimitsReachedCache = null;
            }
        }

        // Final cache update
        cache(['offset-record-count' => $offset], now()->addDays(5));

        $progressBar->finish();
        $this->line('');

        // Show domain group usage
        $this->info('Domain group usage:');

        foreach ($this->groupCounters as $group => $count) {
            $limit = $this->domainGroups[$group]['limit'];
            $domains = implode(', ', $this->domainGroups[$group]['domains']);
            $this->info("- Group '$group' ($domains): $count/$limit");
        }

        $this->info('Processing summary:');
        $this->info("- Processed: $processedCount records");
        $this->info("- Sent to GetResponse: $sentCount customers");
        $this->info("- Skipped: $skippedCount customers");
        $this->info("- Errors: $errorCount");
        $this->info("- Current offset: $offset");

        $this->logDebug('File processing completed', [
            'marketing_list'    => $marketingList->name,
            'marketing_list_id' => $marketingList->id,
            'file'              => $file,
            'processed_count'   => $processedCount,
            'sent_count'        => $sentCount,
            'skipped_count'     => $skippedCount,
            'error_count'       => $errorCount,
            'offset'            => $offset,
        ]);
    }


    private function sendCustomerToGetResponse($marketingList, $customer): void
    {
        $this->logDebug('Attempting to send customer to GetResponse', [
            'customer_email'    => $customer->email,
            'marketing_list'    => $marketingList->name,
            'marketing_list_id' => $marketingList->id,
        ]);
        /** @var GetResponseClient $getResponseClient */
        $getResponseClient = app()->make(GetResponseClient::class);

        try {
            $getResponseClient->addContact([
                'email'             => $customer->email,
                'name'              => $customer->full_name,
                'campaign'          => [
                    'campaignId' => $marketingList->list_id,
                ],
                'customFieldValues' => [
                    [
                        'customFieldId' => Arr::get(
                            $getResponseClient->getCustomFields('customer'),
                            'customFieldId'
                        ),
                        'value'         => [
                            $customer->has_fulfilled_orders ? 'yes' : 'no',
                        ],
                    ],
                    [
                        'customFieldId' => Arr::get(
                            $getResponseClient->getCustomFields('gender'),
                            'customFieldId'
                        ),
                        'value'         => [
                            $customer->gender ?? 'Prefer not to say',
                        ],
                    ],
                ],
            ],
                $marketingList->list_id,
                false
            );

            $customer->marketingLists()->syncWithoutDetaching([
                $marketingList->getKey() => ['subscribed_at' => now()],
            ]);

            $this->logDebug('Customer successfully sent to GetResponse', [
                'customer_email' => $customer->email,
                'marketing_list' => $marketingList->name,
            ]);
        } catch (GetResponseException $e) {
            $this->logDebug('GetResponseException occurred', [
                'customer_email' => $customer->email,
                'error'          => $e->getMessage(),
                'code'           => $e->getCode(),
                'trace'          => $e->getTraceAsString(),
            ]);

            $this->error(
                "\nGetResponseException sending customer $customer->email to GetResponse: " .
                $e->getMessage() . ' [' . $e->getCode() . ']'
            );
        } catch (\Exception $e) {
            $this->logDebug('General exception sending customer to GetResponse', [
                'customer_email' => $customer->email,
                'error'          => $e->getMessage(),
                'file'           => $e->getFile(),
                'line'           => $e->getLine(),
                'trace'          => $e->getTraceAsString(),
            ]);

            $this->error("\nError sending customer to GetResponse: " . $e->getMessage());
            $this->error($e->getTraceAsString());
        }
    }

    private function checkGroupLimit(string $domain): bool
    {
        $group = $this->getGroupByDomain($domain);

        if (!$group) {
            return true;
        }

        // Create a cache key that includes the date to enforce daily limits
        $cacheKey = "getresponse_group_{$group}_" . now()->format('Y-m-d');

        // Use memoized value if available
        if (!isset($this->cachedGroupCounts[$group])) {
            $this->cachedGroupCounts[$group] = cache($cacheKey) ?? 0;
        }

        // Check if limit is reached
        if ($this->cachedGroupCounts[$group] >= $this->domainGroups[$group]['limit']) {
            // Show notification only once per group per file
            if (!isset($this->groupLimitNotified[$group]) || !$this->groupLimitNotified[$group]) {
                $domains = implode(', ', $this->domainGroups[$group]['domains']);
                $this->info(
                    "\nDomain group '$group' ($domains) has reached its daily limit of {$this->domainGroups[$group]['limit']}"
                );
                $this->groupLimitNotified[$group] = true;
            }

            return false;
        }

        // Increment counter in both cache and memoized value
        $this->cachedGroupCounts[$group]++;
        cache([$cacheKey => $this->cachedGroupCounts[$group]], now()->endOfDay());

        // Also update local counter for reporting
        if (!isset($this->groupCounters[$group])) {
            $this->groupCounters[$group] = 0;
        }

        $this->groupCounters[$group]++;

        // Check if we just reached the limit with this increment
        if ($this->cachedGroupCounts[$group] == $this->domainGroups[$group]['limit']) {
            $domains = implode(', ', $this->domainGroups[$group]['domains']);
            $this->info(
                "\nDomain group '$group' ($domains) has just reached its daily limit of {$this->domainGroups[$group]['limit']}"
            );
            $this->groupLimitNotified[$group] = true;
        }

        return true;
    }


    private function getGroupByDomain(string $domain): ?string
    {
        foreach ($this->domainGroups as $group => $data) {
            if (in_array($domain, $data['domains'])) {
                return $group;
            }
        }

        return null;
    }

    private function allGroupLimitsReached(): bool
    {
        // Return memoized result if available
        if ($this->allLimitsReachedCache !== null) {
            return $this->allLimitsReachedCache;
        }

        $allReached = true;

        foreach ($this->domainGroups as $group => $data) {
            // Use memoized value if available
            if (!isset($this->cachedGroupCounts[$group])) {
                $cacheKey = "getresponse_group_{$group}_" . now()->format('Y-m-d');
                $this->cachedGroupCounts[$group] = cache($cacheKey) ?? 0;
            }

            if ($this->cachedGroupCounts[$group] < $data['limit']) {
                $allReached = false;
                break;
            }
        }

        $this->allLimitsReachedCache = $allReached;

        // If all limits are reached, and we haven't notified yet, show a notification
        if ($allReached && (!isset($this->groupLimitNotified['all']) || !$this->groupLimitNotified['all'])) {
            $this->info("\nAll domain group limits have been reached for today");
            $this->groupLimitNotified['all'] = true;
        }

        return $allReached;
    }

    // Add this method to initialize group counters from cache for reporting
    private function initGroupCountersFromCache(): void
    {
        $this->logDebug('Initializing group counters from cache');
        $this->groupCounters = [];
        $this->cachedGroupCounts = [];
        $this->allLimitsReachedCache = null;

        foreach ($this->domainGroups as $group => $data) {
            $cacheKey = "getresponse_group_{$group}_" . now()->format('Y-m-d');
            $count = cache($cacheKey) ?? 0;

            // Store in both local counters
            $this->groupCounters[$group] = $count;
            $this->cachedGroupCounts[$group] = $count;
        }

        $this->logDebug('Group counters initialized', [
            'counters' => $this->groupCounters,
        ]);
    }

    /**
     * Log debug information to a dedicated file
     *
     * @param  string  $message
     * @param  array  $context
     * @return void
     */
    private function logDebug(string $message, array $context = []): void
    {
        // Add timestamp to all log entries
        $context['timestamp'] = now()->toDateTimeString();

        // Create a dedicated log file for this command
        $logDir = storage_path('logs');
        $logFile = $logDir . '/getresponse-command.log';

        // Ensure the log directory exists
        if (!file_exists($logDir)) {
            mkdir($logDir, 0755, true);
        }

        // Format the log message
        $formattedMessage = '[' . now()->toDateTimeString(
            ) . '] [SendCustomersToGetResponse] ' . $message . ' ' . json_encode(
                $context,
                JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE
            ) . PHP_EOL;

        // Append to the log file
        file_put_contents($logFile, $formattedMessage, FILE_APPEND);

        // Also log to Laravel's log for system monitoring
        Log::channel('daily')->info('[SendCustomersToGetResponse] ' . $message, $context);

        // Also output to console if in verbose mode
        if ($this->getOutput() && $this->getOutput()->isVerbose()) {
            $this->info('[DEBUG] ' . $message . ' ' . json_encode($context));
        }
    }

    /**
     * Get formatted memory usage information
     *
     * @return array
     */
    private function getMemoryUsage(): array
    {
        return [
            'current' => $this->formatBytes(memory_get_usage(false)),
            'peak'    => $this->formatBytes(memory_get_peak_usage(false)),
        ];
    }

    /**
     * Format bytes to human-readable format
     *
     * @param  int  $bytes
     * @return string
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= (1 << (10 * $pow));

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Test S3 connection and log detailed information about any issues
     *
     * @return void
     */
    private function testS3Connection(): void
    {
        try {
            $this->logDebug('Testing S3 connection');

            // Get S3 configuration
            $config = config('filesystems.disks.' . $this->disk);

            // Log config (without credentials)
            $safeConfig = $config;
            if (isset($safeConfig['key'])) {
                $safeConfig['key'] = substr($safeConfig['key'], 0, 4) . '****' . substr($safeConfig['key'], -4);
            }
            if (isset($safeConfig['secret'])) {
                $safeConfig['secret'] = '********';
            }

            $this->logDebug('S3 configuration', ['config' => $safeConfig]);

            // Test if we can list files
            $canList = false;
            try {
                Storage::disk($this->disk)->files('/');
                $canList = true;
            } catch (\Exception $e) {
                $this->logDebug('Cannot list S3 root directory', [
                    'error' => $e->getMessage(),
                ]);
            }

            // Test if we can write a test file
            $canWrite = false;
            $testFilePath = 'test-' . time() . '.txt';
            try {
                Storage::disk($this->disk)->put($testFilePath, 'Test file');
                $canWrite = true;

                // Clean up test file
                Storage::disk($this->disk)->delete($testFilePath);
            } catch (\Exception $e) {
                $this->logDebug('Cannot write to S3', [
                    'error' => $e->getMessage(),
                ]);
            }

            $this->logDebug('S3 connection test results', [
                'can_list'  => $canList,
                'can_write' => $canWrite,
            ]);
        } catch (\Exception $e) {
            $this->logDebug('Error testing S3 connection', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Get information about the scheduler that might be useful for debugging
     *
     * @return array
     */
    private function getSchedulerInfo(): array
    {
        return [
            'is_cli'       => app()->runningInConsole(),
            'server_time'  => now()->toDateTimeString(),
            'timezone'     => config('app.timezone'),
            'server_load'  => sys_getloadavg(),
            'artisan_path' => base_path('artisan'),
            'is_scheduled' => !app()->isLocal() && !$this->option('env'),
        ];
    }

    /**
     * Check for any locks that might be preventing the command from running
     *
     * @return void
     */
    private function checkForLocks(): void
    {
        try {
            $this->logDebug('Checking for command locks');

            // Check for Laravel's cache-based mutex lock
            $mutexName = 'laravel_' . md5(
                    'Illuminate\Console\Scheduling\SchedulingMutex:customer:send-to-get-response'
                );
            $hasSchedulerLock = cache()->has($mutexName);

            // Check for Laravel's file-based mutex lock
            $lockPath = storage_path('framework/schedule-' . sha1('customer:send-to-get-response'));
            $hasFileLock = file_exists($lockPath);

            // If file lock exists, get its age and content
            $fileLockAge = null;
            $fileLockContent = null;
            if ($hasFileLock) {
                $fileLockAge = time() - filemtime($lockPath);
                $fileLockContent = file_get_contents($lockPath);
            }

            $this->logDebug('Lock check results', [
                'has_scheduler_lock'    => $hasSchedulerLock,
                'has_file_lock'         => $hasFileLock,
                'file_lock_age_seconds' => $fileLockAge,
                'file_lock_content'     => $fileLockContent,
            ]);

            // If there's a stale lock (older than 1 hour), attempt to clear it
            if ($hasFileLock && $fileLockAge > 3600) {
                $this->logDebug('Attempting to clear stale file lock');
                @unlink($lockPath);
                $clearedLock = !file_exists($lockPath);
                $this->logDebug('Stale lock clearing result', ['success' => $clearedLock]);
            }
        } catch (\Exception $e) {
            $this->logDebug('Error checking for locks', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Load domain groups from database for the given marketing list
     *
     * @param  MarketingList  $marketingList
     * @return void
     */
    private function loadDomainGroupsForMarketingList(MarketingList $marketingList): void
    {
        $this->currentMarketingList = $marketingList;
        $this->domainGroups = [];

        $limits = MarketingListLimit::where('marketing_list_id', $marketingList->id)->get();

        if ($limits->isEmpty()) {
            $this->logDebug('No marketing list limits found for marketing list', [
                'marketing_list_id'   => $marketingList->id,
                'marketing_list_name' => $marketingList->name ?? 'Unknown',
            ]);
            $this->warn(
                "No domain limits configured for marketing list {$marketingList->id}. Please run the MarketingListLimitSeeder."
            );

            return;
        }

        foreach ($limits as $limit) {
            $this->domainGroups[$limit->group] = [
                'domains' => $limit->domains,
                'limit'   => $limit->limit,
            ];
        }

        $this->logDebug('Loaded domain groups from database', [
            'marketing_list_id' => $marketingList->id,
            'groups_count'      => count($this->domainGroups),
            'groups'            => array_keys($this->domainGroups),
        ]);
    }
}
