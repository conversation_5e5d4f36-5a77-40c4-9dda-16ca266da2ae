<?php

namespace App\Console\Commands;

use App\Enums\OrderStatus;
use App\Models\Order;
use App\Services\ImportService\Order\RefundService;
use App\Traits\CommandHasDateRange;
use Illuminate\Console\Command;

class RepairRefundedAmountForOrdersWithoutPayloads extends Command
{
    use CommandHasDateRange;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'repair:refunded-amount
                            {start? : The start date. If empty, default is 30 days ago.}
                            {end? : The end date. If empty, default is today.}
                            {--source-ref= : The order source reference.}
                            {--order=* : The order ID or list of IDs.}
                            {--statuses= : The payload status.}
                            {--account= : The account ID.}
                            {--without-date-filter : Skip default filter by date.}
                            {--limit= : Limit the number of orders to repair.}
                            {--async : Run the command in the background.}
                            {--debug : Display debug information.}';

    protected $description = 'Update various fields in orders based on payloads.';

    public function handle()
    {
        $this->setDateRange();

        if (!$this->argument('start')) {
            $this->startsAt = $this->startsAt->subDays(30);
        }

        $query = Order::query()
            ->where('status', '!=', OrderStatus::ABANDONED)
            ->where('orders.refunded_amount', '>', 'orders.total_amount')
            ->orderBy('purchased_at', 'desc');

        if ($this->option('order')) {
            $query->whereIn(
                'id',
                collect($this->option('order'))
                    ->map(fn($id) => explode(',', $id))
                    ->flatten()
            );
        } elseif ($this->option('source-ref')) {
            $query->where('source_ref', $this->option('source-ref'));
        } elseif (!$this->option('without-date-filter')) {
            $query->whereBetween('purchased_at', [
                $this->startsAt->startOfDay(),
                $this->endsAt->endOfDay(),
            ]);
        }

        if ($this->option('account')) {
            $query->where('account_id', $this->option('account'));
        }

        if ($this->option('limit')) {
            $query->limit($this->option('limit'));
        }

        $count = $query->count();

        $this->info("Repairing $count orders  ..");
        $this->getOutput()?->progressStart($count);

        $orders = $query->get();

        $refundService = app()->make(RefundService::class);

        foreach ($orders as $order) {
            $order->refunded_amount = $refundService->getRefundedAmountByRefund($order);
            $order->saveQuietly();
        }

        $this->getOutput()?->progressFinish();
        $this->info("Repairing is done.");

        return Command::SUCCESS;
    }
}
