<?php

namespace App\Console\Commands;

use App\Enums\AccountSource;
use App\Models\Account;
use App\Models\OfferReport;
use App\Services\Report\OfferReportService;
use App\Traits\BuildsOfferReports;
use App\Traits\CommandHasDateRange;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class AggregateOfferReports extends Command
{
    use CommandHasDateRange;
    use BuildsOfferReports;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'aggregate:offer-reports
                            {start : The start date or days back.}
                            {end? : The end date or days back. If empty, yesterday will be used.}
                            {--source= : Account source. e.g. buygoods, clickbank, digistore24.}
                            {--fresh : Detach existing reports before aggregating.}
                            {--debug : Dump the report data as it is generated.}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate reports for each offer.';

    public Carbon $reportDate;

    /**
     * Execute the console command.
     * @throws \Exception
     */
    public function handle(): int
    {
        $this->setDateRange(1);

        // If source is set, confirm valid option.
        if ($this->option('source') && !array_key_exists($this->option('source'), AccountSource::keyValueCases())) {
            $this->error("Invalid source: {$this->option('source')}");

            return Command::FAILURE;
        }

        $sources = [
            AccountSource::CLICKBANK,
            AccountSource::DIGISTORE24,
            AccountSource::BUYGOODS,
            AccountSource::CHECKOUTCHAMP,
            AccountSource::AMAZON_SELLER_CENTRAL,
            AccountSource::KONNEKTIVE,
            AccountSource::COMMITTED_COACHES,
        ];

        // Iterate through each day from the start date until the end date.
        for (
            $this->reportDate = $this->startsAt->copy();
            $this->reportDate->lte($this->endsAt);
            $this->reportDate->addDay()
        ) {
            // Process each report source.
            /** @var AccountSource $source */
            foreach ($sources as $source) {
                if ($this->option('source') && $source->value !== $this->option('source')) {
                    continue;
                }

                $this->comment('Aggregating ' . $source->value .
                    " records for date {$this->reportDate->toDateString()}...");

                $service = new OfferReportService($source);


                foreach (Account::bySource($source)->get() as $account) {
                    $this->fresh($account, $this->reportDate);
                    $offers = $service->offersQuery($account, $this->reportDate)->get();

                    if (count($offers)) {
                        $this->info("Aggregating {$source->value} offers for account '{$account->source_ref}'...");
                    } else {
                        $this->warn(
                            "No offers found for {$source->value} account '{$account->source_ref}'" .
                            " with orders purchased on {$this->reportDate->toDateString()}."
                        );
                    }

                    foreach ($offers as $offer) {
                        try {
                            $service->saveOfferReport($offer, $this->reportDate);
                        } catch (\Exception $ex) {
                            $this->error($ex->getMessage());
                        }
                    }
                }
            }
        }

        return Command::SUCCESS;
    }

    public function fresh(Account $account, Carbon $reportDate): void
    {
        if ($this->option('fresh')) {
            $query = OfferReport::where('starts_at', '>=', $reportDate->copy()->startOfDay())
                ->where('ends_at', '<=', $reportDate->copy()->endOfDay())
                ->whereHas('account', fn(Builder $q) => $q->where('accounts.id', $account->id));

            $this->info("Clearing {$query->count()} existing reports.");

            $query->each(fn($report) => $report->delete());
        }
    }
}
