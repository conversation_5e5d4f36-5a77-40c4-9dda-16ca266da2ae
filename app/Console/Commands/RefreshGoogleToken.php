<?php

namespace App\Console\Commands;

use App\Enums\ServiceCredentialType;
use App\Jobs\SyncSnowflakeRecord;
use App\Models\ServiceCredential;
use App\Services\Google\GoogleAuthClient;
use Artel\Support\Iterators\DBIterator;
use Illuminate\Console\Command;

class RefreshGoogleToken extends Command
{
    protected $signature = 'google:token';

    protected $description = 'Create or update google token';

    public function handle()
    {
        /** @var GoogleAuthClient $auth */
        $auth = app()->make(GoogleAuthClient::class);

        $model = ServiceCredential::where('service', ServiceCredentialType::GOOGLE->value)->first();
        $authLink = $auth->getAuthLink();

        if (!$model) {
            $this->info('Go to link: ' . $authLink);
            return;
        }

        if ($this->confirm('Google token already exists. Old token will be removed. Do you wish to continue?', true)) {
            $model->delete();
            $this->info('Old token removed.');
            $this->info('Go to link: ' . $authLink);
        }

        return Command::SUCCESS;
    }
}
