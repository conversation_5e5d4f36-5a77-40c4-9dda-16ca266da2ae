<?php

namespace App\Console\Commands;

use App\Models\Domain;
use App\Models\PageTemplate;
use App\Services\CloudflareService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Arr;
use Symfony\Component\Console\Helper\ProgressBar;

class ImportPageTemplates extends Command
{
    protected $signature = 'import:page-templates';
    protected $description = 'Import page templates from cloudflare';
    protected ?ProgressBar $progressBar = null;


    public function handle(CloudflareService $service)
    {
        $page = 1;
        $this->progressBar = $this->output?->createProgressBar();
        $this->progressBar?->start();

        do {
            [$data, $pagination] = $service->getProjectList($page);
            if (!$this->progressBar?->getMaxSteps()) {
                $this->progressBar?->setMaxSteps($pagination['total_count']);
            }

            foreach ($data as $template) {
                $name = Arr::get($template, 'name');
                $pageTemplate = $this->getPageTemplate($name);

                foreach (Arr::get($template, 'domains') as $domain) {
                    $this->setTemplateToDomain($pageTemplate, $domain);
                }
                $this->progressBar?->advance();
            }

            $page++;
        } while ($pagination['page'] < $pagination['total_pages']);
        $this->progressBar?->finish();

        return Command::SUCCESS;
    }

    private function setTemplateToDomain(PageTemplate $template, string $domain)
    {
        try {
            $domain = Domain::where(['domain' => $domain])->firstOrFail();
            if ($domain->page_template_id !== $template->id) {
                $domain->page_template_id = $template->id;
                $domain->save();
            }
        } catch (ModelNotFoundException $ex) {

        }
    }

    private function getPageTemplate(string $name): PageTemplate
    {
        try {
            $pageTemplate = PageTemplate::where(['name' => $name])->firstOrFail();
        } catch (ModelNotFoundException $ex) {
            $pageTemplate = new PageTemplate();
            $pageTemplate->name = $name;
            $pageTemplate->save();
        }

        return $pageTemplate;
    }
}
