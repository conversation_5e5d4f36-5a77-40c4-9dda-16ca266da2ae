<?php

namespace App\Console\Commands;

use App\Enums\OrderStatus;
use App\Enums\PayloadSource;
use App\Enums\PayloadStatus;
use App\Models\Customer;
use App\Models\Order;
use App\Models\Payload;
use App\Models\User;
use App\Traits\CommandHasDateRange;
use App\Traits\GeneratesCsvReports;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class RepairHubspotDiscrepancy extends Command
{
    use CommandHasDateRange;
    use GeneratesCsvReports;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'repair:hubspot-discrepancy
                            {start? : The start date. If empty, default is 30 days ago.}
                            {end? : The end date. If empty, default is today.}
                            {--limit=25 : Limit the number of samples shown in the results table.}
                            {--order-type=both : Filter by order type: completed, abandoned, or both.}
                            {--csv : Generate CSV reports and upload to S3.}
                            {--email : Send email notification with report links (requires --csv).}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Identify customers that should have been sent to Hubspot but weren\'t based on zerobounce and numverify status';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->setDateRange();

        // If no start date provided, default to 30 days ago
        if (!$this->argument('start')) {
            $this->startsAt = now()->subDays(30)->startOfDay();
        }

        $limit = (int)$this->option('limit');
        $orderType = $this->option('order-type');
        $generateCsv = $this->option('csv');
        $sendEmail = $this->option('email');
        $user = null;

        // Validate order type option
        if (!in_array($orderType, ['completed', 'abandoned', 'both'])) {
            $this->error('Invalid order type. Must be: completed, abandoned, or both');

            return Command::FAILURE;
        }

        // Get user email if CSV or email options are used
        if ($generateCsv || $sendEmail) {
            if ($sendEmail && !$generateCsv) {
                $this->error('The --email option requires --csv to be enabled.');

                return Command::FAILURE;
            }

            $email = $this->askForUserEmail();
            $user = $this->getUserByEmail($email);

            if (!$user) {
                $this->error("User with email '{$email}' not found.");

                return Command::FAILURE;
            }

            $this->info("Reports will be generated for user: {$user->name} ({$user->email})");
            $this->newLine();
        }

        $this->info(
            "Analyzing Hubspot discrepancies from {$this->startsAt->format('Y-m-d')} to {$this->endsAt->format('Y-m-d')}"
        );
        $this->info("Order type filter: {$orderType}");
        $this->info("Sample limit: {$limit}");
        $this->newLine();

        // Build order status filter
        $orderStatuses = $this->getOrderStatusesForFilter($orderType);

        // Get orders in date range with the specified statuses
        $ordersQuery = Order::query()
            ->whereBetween('purchased_at', [$this->startsAt, $this->endsAt])
            ->whereIn('status', $orderStatuses)
            ->with([
                'customer',
                'payloads' => function ($query) {
                    $query->where('source', PayloadSource::HUBSPOT_SEND_CONTACT->value);
                },
            ]);

        $this->info('Analyzing orders...');
        $progressBar = $this->output->createProgressBar($ordersQuery->count());
        $progressBar->start();

        $discrepancies = collect();
        $allDiscrepancies = collect(); // For CSV - all discrepancies
        $totalMissing = 0;
        $totalWithErrors = 0;
        $totalSent = 0;

        $ordersQuery->chunk(100, function ($orders) use (
            &$discrepancies,
            &$allDiscrepancies,
            &$totalMissing,
            &$totalWithErrors,
            &$totalSent,
            $limit,
            $progressBar,
            $generateCsv
        ) {
            foreach ($orders as $order) {
                $this->analyzeOrder(
                    $order,
                    $discrepancies,
                    $allDiscrepancies,
                    $totalMissing,
                    $totalWithErrors,
                    $totalSent,
                    $limit,
                    $generateCsv
                );
                $progressBar->advance();
            }
        });

        $progressBar->finish();
        $this->newLine(2);

        // Display results
        $this->displayResults($discrepancies, $totalMissing, $totalWithErrors, $totalSent);

        // Generate CSV reports if requested
        if ($generateCsv && $user) {
            $this->generateCsvReport($allDiscrepancies, $user, $sendEmail, $orderType);
        }

        return Command::SUCCESS;
    }

    /**
     * Get order statuses based on filter
     */
    private function getOrderStatusesForFilter(string $orderType): array
    {
        return match ($orderType) {
            'completed' => [OrderStatus::COMPLETED->value, OrderStatus::REFUNDED->value],
            'abandoned' => [OrderStatus::ABANDONED->value],
            'both' => [OrderStatus::COMPLETED->value, OrderStatus::REFUNDED->value, OrderStatus::ABANDONED->value],
        };
    }

    /**
     * Analyze an order for Hubspot discrepancies
     */
    private function analyzeOrder(
        Order $order,
        Collection &$discrepancies,
        Collection &$allDiscrepancies,
        int &$totalMissing,
        int &$totalWithErrors,
        int &$totalSent,
        int $limit,
        bool $generateCsv
    ): void {
        $customer = $order->customer;

        // Check if customer should be sent to Hubspot (valid zerobounce OR numverify)
        $shouldBeSent = $customer->zerobounce_status === 'valid' || $customer->numverify_valid;

        if (!$shouldBeSent) {
            return; // Skip customers that shouldn't be sent
        }

        // Check if customer has hubspot_id
        if ($customer->hubspot_id) {
            $totalSent++;
            return; // Customer was successfully sent
        }

        // Customer should be sent but has no hubspot_id - check for errors
        $hubspotPayload = $order->payloads->where('source', PayloadSource::HUBSPOT_SEND_CONTACT->value)->first();

        if ($hubspotPayload && $hubspotPayload->status === PayloadStatus::FAILED->value) {
            $totalWithErrors++;
            $discrepancyType = 'Error';
            $errorLink = $this->generatePayloadErrorLink($hubspotPayload);
        } else {
            $totalMissing++;
            $discrepancyType = 'Missing';
            $errorLink = 'N/A';
        }

        $discrepancyRecord = $this->createDiscrepancyRecord($customer, $order, $discrepancyType, $errorLink);

        if ($discrepancies->count() < $limit) {
            $discrepancies->push($discrepancyRecord);
        }

        if ($generateCsv) {
            $allDiscrepancies->push($this->createCsvRecord($customer, $order, $discrepancyType, $errorLink));
        }
    }

    /**
     * Generate Nova link to payload error
     */
    private function generatePayloadErrorLink(Payload $payload): string
    {
        $appUrl = rtrim(config('app.url'), '/');
        $novaPath = config('nova.path', 'nova');

        return $appUrl . '/' . $novaPath . '/resources/payloads/' . $payload->id;
    }

    /**
     * Create a discrepancy record for display
     */
    private function createDiscrepancyRecord(Customer $customer, Order $order, string $type, string $errorLink): array
    {
        $appUrl = rtrim(config('app.url'), '/');
        $novaPath = config('nova.path', 'nova');

        return [
            'customer_id'       => $customer->id,
            'customer_link'     => $appUrl . '/' . $novaPath . '/resources/customers/' . $customer->id,
            'order_id'          => $order->id,
            'order_link'        => $appUrl . '/' . $novaPath . '/resources/orders/' . $order->id,
            'order_status'      => $order->status,
            'type'              => $type,
            'error_link'        => $errorLink,
            'zerobounce_status' => $customer->zerobounce_status ?? 'N/A',
            'numverify_valid'   => $customer->numverify_valid ? 'Yes' : 'No',
        ];
    }

    /**
     * Create CSV record for customer
     */
    private function createCsvRecord(Customer $customer, Order $order, string $type, string $errorLink): array
    {
        return [
            $customer->first_name ?? '',
            $customer->last_name ?? '',
            $customer->email ?? '',
            $customer->created_at ? $customer->created_at->format('Y-m-d H:i:s') : '',
            $order->id,
            $order->status,
            $order->purchased_at ? $order->purchased_at->format('Y-m-d H:i:s') : '',
            $type,
            $customer->zerobounce_status ?? '',
            $customer->numverify_valid ? 'Yes' : 'No',
            $errorLink,
        ];
    }

    /**
     * Display the results in tables
     */
    private function displayResults(
        Collection $discrepancies,
        int $totalMissing,
        int $totalWithErrors,
        int $totalSent
    ): void {
        if ($discrepancies->isNotEmpty()) {
            $this->info('HUBSPOT DISCREPANCIES (Sample):');
            $this->table(
                [
                    'Customer ID',
                    'Customer Link',
                    'Order ID',
                    'Order Status',
                    'Type',
                    'Error Link',
                    'Zerobounce',
                    'Numverify',
                ],
                $discrepancies->map(function ($item) {
                    return [
                        $item['customer_id'],
                        $item['customer_link'],
                        $item['order_id'],
                        $item['order_status'],
                        $item['type'],
                        $item['error_link'],
                        $item['zerobounce_status'],
                        $item['numverify_valid'],
                    ];
                })->toArray()
            );
            $this->newLine();
        }

        // Display totals
        $this->info('HUBSPOT SENDING SUMMARY:');
        $this->line("Successfully Sent: {$totalSent}");
        $this->line("Missing (No Attempt): {$totalMissing}");
        $this->line("Failed with Errors: {$totalWithErrors}");
        $this->line("Total Issues: " . ($totalMissing + $totalWithErrors));

        if ($totalMissing === 0 && $totalWithErrors === 0) {
            $this->info('No Hubspot discrepancies found in the specified date range.');
        }
    }

    /**
     * Generate CSV report
     */
    private function generateCsvReport(
        Collection $allDiscrepancies,
        User $user,
        bool $sendEmail,
        string $orderType
    ): void {
        if ($allDiscrepancies->isEmpty()) {
            $this->info('No CSV report generated (no discrepancies found).');

            return;
        }

        $this->newLine();
        $this->info('Generating CSV report...');

        $headers = [
            'First Name',
            'Last Name',
            'Email',
            'Date Created',
            'Order ID',
            'Order Status',
            'Order Date',
            'Issue Type',
            'Zerobounce Status',
            'Numverify Valid',
            'Error Link',
        ];

        $filename = $this->generateFilenameWithDateRange("hubspot_discrepancies_{$orderType}");
        $url = $this->generateAndUploadCsvReport($allDiscrepancies, $headers, $filename, $user, $sendEmail);

        if (!$url) {
            $this->error('Failed to generate CSV report.');
        }
    }
}
