<?php

namespace App\Console\Commands;

use App\Enums\OrderStatus;
use App\Jobs\RepairOrder;
use App\Models\Order;
use App\Traits\CommandHasDateRange;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;

class RepairOrders extends Command
{
    use CommandHasDateRange;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'repair:orders
                            {start? : The start date. If empty, default is 30 days ago.}
                            {end? : The end date. If empty, default is today.}
                            {--source-ref= : The order source reference.}
                            {--order=* : The order ID or list of IDs.}
                            {--statuses= : The payload status.}
                            {--account= : The account ID.}
                            {--without-date-filter : Skip default filter by date.}
                            {--dont-skip-repaired : Don\'t skip orders that have already been repaired.}
                            {--limit= : Limit the number of orders to repair.}
                            {--async : Run the command in the background.}
                            {--debug : Display debug information.}';

    private int $repaired = 0;

    protected $description = 'Update various fields in orders based on payloads.';

    public function handle()
    {
        $this->setDateRange();

        if (!$this->argument('start')) {
            $this->startsAt = $this->startsAt->subDays(30);
        }

        $query = Order::query()
            ->where('status', '!=', OrderStatus::ABANDONED)
            ->orderBy('purchased_at', 'desc')
            ->with('payloads', fn($query) => $query->whereIn('source', RepairOrder::PAYLOAD_SOURCES));

        if ($this->option('order')) {
            $query->whereIn(
                'id',
                collect($this->option('order'))
                    ->map(fn($id) => explode(',', $id))
                    ->flatten()
            );
        } elseif ($this->option('source-ref')) {
            $query->where('source_ref', $this->option('source-ref'));
        } elseif (!$this->option('without-date-filter')) {
            $query->whereBetween('purchased_at', [
                $this->startsAt->startOfDay(),
                $this->endsAt->endOfDay(),
            ]);
        }

        if ($this->option('account')) {
            $query->where('account_id', $this->option('account'));
        }

        $count = $query->count();

        $this->info("Repairing $count orders from payloads...");
        $this->getOutput()?->progressStart($count);

        $query->chunk(1000, function (Collection $orders) {
            if ($this->option('limit') && $this->repaired >= $this->option('limit')) {
                return false;
            }

            $orders->each(function (Order $order) {
                $this->getOutput()?->progressAdvance();

                if ($this->option('limit') && $this->repaired >= $this->option('limit')) {
                    return false;
                }

                if (!$this->option('dont-skip-repaired') && $order->getMeta('repaired_at')) {
                    return;
                }

                $this->repaired++;

                if ($this->option('async')) {
                    dispatch(new RepairOrder($order));
                } else {
                    $repairOrder = new RepairOrder($order, $this);
                    $repairOrder->handle();
                }
            });
        });

        $this->getOutput()?->progressFinish();

        $this->info("Repaired $this->repaired orders from payloads.");

        return Command::SUCCESS;
    }
}
