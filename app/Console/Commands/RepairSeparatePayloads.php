<?php

namespace App\Console\Commands;

use App\Enums\PayloadSource;
use App\Models\Payload;
use App\Services\ImportService\Order\ImportOrderService;
use App\Traits\CommandHasDateRange;
use Carbon\Carbon;
use Illuminate\Console\Command;

class RepairSeparatePayloads extends Command
{
    use CommandHasDateRange;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'repair:separate-payloads
                            {start=30 : The start date or days back. If empty, searches last 30 days.}
                            {end? : The end date or days back.}
                            {--source= : The source.}
                            {--status= : The payload status.}
                            {--delete= : Delete processed payloads.}';

    protected $description = 'Separate big payloads.';
    private int $totalProcessed = 0;
    private Carbon|null $currentDate = null;

    public function handle()
    {
        $this->setDateRange();

        $query = Payload::query();

        $query->whereBetween('created_at', [
            $this->startsAt->startOfDay()->toDateTimeString(),
            $this->endsAt->endOfDay()->toDateTimeString(),
        ])
            ->orderBy('created_at');

        if ($this->option('source')) {
            $query->where('source', $this->option('source'));
        } else {
            $query->where('source', PayloadSource::CLICKBANK_ORDERS_API);
        }

        if ($this->option('status')) {
            $query->where(
                'status',
                '=',
                $this->option('status')
            );
        }

        $count = $query->count();

        $this->info("Checking $count payloads...");
        $this->getOutput()?->progressStart($count);

        $page = 1;

        $payloads = $query->paginate(1000, '*', 'page', $page);

        while ($payloads->count()) {
            foreach ($payloads as $payload) {
                if (is_null($this->currentDate)) {
                    $this->currentDate = $payload->created_at;
                    $this->info("\nProcessing payloads for {$this->currentDate->toDateString()}...");
                } elseif ($this->currentDate->ne($payload->created_at)) {
                    $this->currentDate = $payload->created_at;
                    $this->info("\nProcessing payloads for {$this->currentDate->toDateString()}...");
                }

                $this->separateOrders($payload);

                $this->getOutput()?->progressAdvance();
                unset($payload);
            }

            gc_collect_cycles();

            $page++;
            $payloads = $query->paginate(1000, '*', 'page', $page);
        }

        $this->getOutput()?->progressFinish();

        $this->info($this->totalProcessed . " payloads processed.");
        $this->info("Checking payloads finish.");

        return Command::SUCCESS;
    }

    private function separateOrders(Payload $payload): void
    {
        $processed = false;

        switch ($payload->source) {
            case PayloadSource::CLICKBANK_ORDERS_API->value:
                if (isset($payload->payload['orderData'])) {
                    $this->separateProcess($payload->payload['orderData']);
                    $processed = true;
                }

                break;
        }

        if ($processed) {
            $this->totalProcessed++;

            if ($this->option('delete')) {
                $payload->delete();
            }
        }
    }

    private function separateProcess(array $rows): void
    {
        foreach ($rows as $row) {
            $importOrderService = new ImportOrderService(PayloadSource::CLICKBANK_ORDERS_API);

            if (!is_array($row)) {
                $this->error("Invalid order data: " . json_encode($row));
                continue;
            }

            try {
                $account = $importOrderService->getAccount($row);
            } catch (\Exception $e) {
                if (empty($row['receipt'])) {
                    $this->error("Receipt not found for order: " . json_encode($row));
                } else {
                    $this->error("Account not found for order {$row['receipt']}");
                }

                continue;
            }

            try {
                $importOrderService->import($account, $row);
            } catch (\Exception $e) {
                $this->error($e->getMessage());
            }
        }
    }
}
