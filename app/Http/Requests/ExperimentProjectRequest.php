<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class ExperimentProjectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            //
        ];
    }

    public function queryParameters()
    {
        return [
            'page' => ['description' => 'page number'],
            'per_page' => ['description' => 'per_page'],
            'filter.source' => ['description' => 'filter by experiment source'],
            'filter.source_ref' => ['description' => 'filter by experiment source ref'],
            'filter.key' => ['description' => 'filter by experiment project key'],
            'filter.name' => ['description' => 'filter by experiment project name'],
            'include' => ['description' => 'Comma-separated list of related models to include in the response. Enum: experiments, goals'],
        ];
    }
}
