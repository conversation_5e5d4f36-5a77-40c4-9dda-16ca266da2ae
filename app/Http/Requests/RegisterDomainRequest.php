<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class RegisterDomainRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'domain' => 'required',
            'affiliate_request_id' => 'nullable|numeric|required_without:affiliate_id,page_template_id',
            'affiliate_id' => 'nullable|numeric|required_without:affiliate_request_id',
            'page_template_id' => 'nullable|numeric|required_without:affiliate_request_id',
        ];
    }
}