<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class BuygoodsPostbackRequest extends FormRequest
{
    protected $stopOnFirstFailure = true;

    protected $redirect = '/errors/400';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize()
    {
        // Check if the postback key in the request matches the expected value.
        return $this->route('postback_key') === config('services.buygoods.postback_key');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, Rule|array|string>
     */
    public function rules()
    {
        // Don't validate here because BuyGoods sends such variable data we are losing orders if we reject them.
        return [];
    }
}
