<?php

namespace App\Http\Repositories;

use App\Core\Pagination;
use App\Enums\FilterConditions;
use App\Filters\FilterInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * Handle common scenarios for QueryBuilder responses.
 */
abstract class QueryBuilderRepository implements RepositoryInterface
{
    public const LIMIT = 1000;

    protected string $defaultSortField = 'created_at';

    protected ?QueryBuilder $builder = null;

    public array $allowedIncludes = [];

    public function getRepositoryQuery(): ?QueryBuilder
    {
        return $this->builder;
    }

    public function getBuilder(
        ?bool $withFilter = true,
        ?bool $withSort = true,
        ?bool $withIncludes = true,
        Request $request = null
    ): QueryBuilder {
        if ($this->builder) {
            return $this->builder;
        }

        $this->builder = QueryBuilder::for($this->getSchema()->newQuery(), $request)
            ->defaultSort($this->defaultSortField);

        if ($withFilter) {
            $this->builder->allowedFilters($this->allowedFilters());
        }

        if ($withIncludes) {
            $this->builder->allowedIncludes($this->allowedIncludes);
        }

        if ($withSort) {
            $this->builder->allowedSorts($this->allowedSorts());
        }

        return $this->builder;
    }

    public function allowedFilters(...$add_filters): array
    {
        return array_merge([
            AllowedFilter::exact('id'),
            AllowedFilter::scope('date_range'),
        ], array_flatten($add_filters));
    }

    public function allowedSorts(...$add_sorts): array
    {
        return array_merge([
            'id',
            'created_at',
        ], array_flatten($add_sorts));
    }

    abstract public function getSchema(): Model;

    /**
     * @param  int  $id
     * @return Model
     */
    public function show(int $id): Model
    {
        $res = $this->getBuilder(false, false)->where('id', $id)->firstOrFail();
        $this->builder = null;
        return $res;
    }

    public function get(?Pagination $pagination = null, ?int $limit = null): Collection|LengthAwarePaginator
    {
        if (!$this->builder) {
            $this->getBuilder();
        }

        if ($pagination) {
            $res = $this->builder->paginate($pagination->perPage, ['*'], $pagination->key, $pagination->page);
        } else {
            $this->builder->limit($limit ?? self::LIMIT);
            $res = $this->builder->get();
        }

        $this->builder = null;

        return $res;
    }

    public function create(array $data): Model
    {
        $model = $this->getSchema();
        $model->fill($data);
        $model->save();
        return $model;
    }

    public function update(Model $model, array $data): Model
    {
        $model->update($data);
        return $model;
    }

    public function destroy(Model $model, ?bool $force = false): void
    {
        if ($force) {
            $model->forceDelete();
            return;
        }

        $model->delete();
    }

    public function select(array $columns): self
    {
        $this->getBuilder()->select($columns);

        return $this;
    }

    public function with(array $relations): self
    {
        $this->getBuilder()->with($relations);

        return $this;
    }

    public function filter(FilterInterface $filter, ?bool $includeRequestFilter = true): self
    {
        return $this;
    }

    protected function addFilterByCondition($builder, string $field, string $condition, mixed $value): void
    {
        if ($condition === FilterConditions::EQUALS->value) {
            $builder->where($field, $value);
        }

        if ($condition === FilterConditions::CONTAINS->value) {
            $builder->where($field, 'like', '%' . $value . '%');
        }

        if ($condition === FilterConditions::NOT_CONTAINS->value) {
            $builder->where($field, 'not like', '%' . $value . '%');
        }

        if ($condition === FilterConditions::STARTS_WITH->value) {
            $builder->where($field, 'like', $value . '%');
        }

        if ($condition === FilterConditions::NOT_STARTS_WITH->value) {
            $builder->where($field, 'not like', $value . '%');
        }

        if ($condition === FilterConditions::ENDS_WITH->value) {
            $builder->where($field, 'like', '%' . $value);
        }

        if ($condition === FilterConditions::NOT_ENDS_WITH->value) {
            $this->builder->where($field, 'not like', '%' . $value);
        }

        if ($condition === FilterConditions::REGEXP->value) {
            $builder->where($field, 'regexp', $value);
        }

        if ($condition === FilterConditions::NOT_REGEXP->value) {
            $builder->where($field, 'not regexp', $value);
        }
    }
}
