<?php

namespace App\Http\Repositories;

use App\Filters\AffiliateFilter;
use App\Filters\FilterInterface;
use App\Http\Repositories\Filters\Affiliate\FilterByAccount;
use App\Models\Affiliate;
use Illuminate\Database\Eloquent\Model;
use Spatie\QueryBuilder\AllowedFilter;

class AffiliateRepository extends QueryBuilderRepository
{
    public array $allowedIncludes = [
        'accounts',
        'customer',
        'product',
        'affiliate',
        'audits',
        'meta',
    ];

    /**
     * @param  mixed  ...$add_filters
     *
     * @return array
     */
    public function allowedFilters(...$add_filters): array
    {
        return parent::allowedFilters(
            AllowedFilter::exact('user_id'),
            AllowedFilter::exact('source_ref'),
            AllowedFilter::exact('pixels_approved'),
            AllowedFilter::partial('name'),
            AllowedFilter::custom('account_id', new FilterByAccount()),
            AllowedFilter::trashed(),
            $add_filters
        );
    }

    public function allowedSorts(...$add_sorts): array
    {
        return parent::allowedSorts(
            'id',
            'source_ref',
            'total_amount',
            'status',
            'created_at',
            'pixels_updated_at',
            'last_active_at',
            $add_sorts
        );
    }

    public function filter(AffiliateFilter|FilterInterface $filter, ?bool $includeRequestFilter = true): self
    {
        $this->getBuilder($includeRequestFilter);

        if ($filter->sources) {
            $this->builder->whereIn('source_ref', $filter->sources);
        }

        return $this;
    }

    public function getSchema(): Model
    {
        return new Affiliate();
    }
}
