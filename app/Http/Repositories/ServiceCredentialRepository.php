<?php

namespace App\Http\Repositories;

use App\Models\ServiceCredential;
use Illuminate\Database\Eloquent\Model;

class ServiceCredentialRepository extends QueryBuilderRepository
{
    public array $allowedIncludes = [];

    /**
     * @param  mixed  ...$add_filters
     *
     * @return array
     */
    public function allowedFilters(...$add_filters): array
    {
        return parent::allowedFilters(
            $add_filters
        );
    }

    public function allowedSorts(...$add_sorts): array
    {
        return parent::allowedSorts(
            'id',
            'service',
            'created_at',
            $add_sorts
        );
    }

    /**
     * @return Model|ServiceCredential
     */
    public function getSchema(): Model
    {
        return new ServiceCredential();
    }
}
