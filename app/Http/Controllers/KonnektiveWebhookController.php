<?php

namespace App\Http\Controllers;

use App\Enums\PayloadSource;
use App\Http\Requests\KonnektiveWebhookRequest;
use App\Services\ImportService\Order\ImportOrderService;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class KonnektiveWebhookController extends Controller
{
    public function handle(KonnektiveWebhookRequest $request): Response
    {
        if (!config('services.konnektive.webhook_enabled')) {
            return response('OK', 200);
        }

        $data = $request->all();
        $campaignName = Str::lower(Arr::get($data, 'campaignName', ''));

        if (str_contains($campaignName, 'test')) {
            return response('OK', 200);
        }

        $import = new ImportOrderService(PayloadSource::KONNEKTIVE_WEBHOOK);
        $account = $import->getAccount($data);
        $import->import($account, $data);

        return response('OK', 200);
    }
}
