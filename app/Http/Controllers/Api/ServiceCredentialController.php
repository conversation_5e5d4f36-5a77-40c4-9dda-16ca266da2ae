<?php

namespace App\Http\Controllers\Api;

use App\Http\Repositories\ServiceCredentialRepository;
use App\Http\Resources\ServiceCredentialResource;
use App\Models\ServiceCredential;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;

class ServiceCredentialController extends ApiController
{
    public function __construct(ServiceCredentialRepository $repository)
    {
        parent::__construct($repository);
    }

    /**
     * Show
     * @group Service Credentials
     * @authenticated
     * @responseFile 200 resources/docs/api/response/service-credential.json
     *
     * @param  Request  $request
     * @param  int  $id
     * @return ServiceCredentialResource
     */
    public function show(Request $request, int $id): ServiceCredentialResource
    {
        /** @var ServiceCredential $serviceCredential */
        $serviceCredential = $this->repository->show($id);

        // Check if the service credential is publicly available
        if (!$serviceCredential->publicly_available) {
            throw new AccessDeniedHttpException('This service credential is not publicly available');
        }

        return ServiceCredentialResource::make($serviceCredential);
    }
}
