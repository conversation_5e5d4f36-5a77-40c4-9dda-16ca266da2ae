<?php

namespace App\Http\Controllers\Api;

use App\Services\Google\GoogleAuthClient;
use Illuminate\Http\JsonResponse;

class GoogleController extends ApiController
{
    public function link(GoogleAuthClient $client): JsonResponse
    {
        $credentials = $client->fetchToken();

        if ($credentials === false) {
            return response()->json(['link' => $client->getAuthLink()]);
        }

        return response()->json(['link' => '']);
    }
}
