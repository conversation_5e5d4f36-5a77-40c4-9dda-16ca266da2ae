<?php

namespace App\Http\Controllers\Api;

use App\Core\Pagination;
use App\Http\Repositories\TestimonialRepository;
use App\Http\Requests\TestimonialListRequest;
use App\Http\Resources\TestimonialResource;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class TestimonialController extends ApiController
{
    /**
     * @var TestimonialRepository
     */
    public $repository;

    public function __construct(TestimonialRepository $repository)
    {
        parent::__construct($repository);
    }
    
    /**
     * Testimonials list
     * @group Testimonials
     * @authenticated
     * @responseFile 200 resources/docs/api/response/testimonials-list.json
     * @return AnonymousResourceCollection
     */
    public function index(TestimonialListRequest $request): AnonymousResourceCollection
    {
        return TestimonialResource::collection(
            $this->repository->get(Pagination::fromArray($request->all()))
        );
    }
}
