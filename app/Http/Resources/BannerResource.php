<?php

namespace App\Http\Resources;

use App\Models\Banner;
use App\Services\ArchiveService\ArchiveService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

class BannerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     *
     * @return array
     */
    public function toArray($request)
    {
        /** @var Banner|self $this */
        return [
            $this->getKeyName() => $this->getKey(),
            'width'             => $this->width,
            'height'            => $this->height,

            $this->mergeWhen(Auth::user()?->tokenCan('view products') && $this->relationLoaded('product'), [
                'product' => ProductResource::make($this->product),
            ]),

            $this->mergeWhen(
                $this->relationLoaded('media')
                && $this->getMedia('primary')->count(),
                [
                    'images' => ImageResource::collection($this->getMedia('primary'))
                ]
            ),
            'thumb' => $this->getFirstMediaUrl('primary', 'thumb'),
        ];
    }
}
