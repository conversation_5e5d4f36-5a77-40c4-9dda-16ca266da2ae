<?php

namespace App\Http\Resources;

use App\Models\ServiceCredential;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ServiceCredentialResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        /** @var ServiceCredential|self $this */
        return [
            $this->getKeyName() => $this->getKey(),
            'service'            => $this->service,
            'credentials'        => $this->credentials,
            'properties'         => $this->properties,
            'expires_at'         => $this->expires_at?->toDateTimeString(),
            'created_at'         => $this->created_at->toDateTimeString(),
            'updated_at'         => $this->updated_at->toDateTimeString(),
        ];
    }
}
