<?php

namespace App\Http\Resources;

use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     *
     * @return array
     */
    public function toArray($request)
    {
        /** @var Order|self $this */
        return [
            $this->getKeyName() => $this->getKey(),
            'total_amount'      => $this->total_amount->formatByDecimal(),
            'refunded_amount'   => $this->refunded_amount->formatByDecimal(),
            'tax_amount'        => $this->tax_amount->formatByDecimal(),
            'shipping_amount'   => $this->shipping_amount->formatByDecimal(),
            'discount_amount'   => $this->discount_amount->formatByDecimal(),
            'currency'          => $this->currency,
            'tax_rate'          => $this->tax_rate,
            'tax_country'       => $this->tax_country,
            'payment_method'    => $this->payment_method,
            'status'            => $this->status,
            'recurring'         => $this->recurring,
            'upsell_stage'      => $this->upsell_stage,
            'is_initial_stage'  => $this->is_initial_stage,
            'is_test'           => $this->is_test,
            'purchased_at'      => optional($this->purchased_at)->toDateTimeString(),
            'rebills_at'        => optional($this->rebills_at)->toDateTimeString(),
            'refunded_at'       => optional($this->refunded_at)->toDateTimeString(),
            'canceled_at'       => optional($this->canceled_at)->toDateTimeString(),
            'created_at'        => $this->created_at->toDateTimeString(),
            'updated_at'        => $this->updated_at->toDateTimeString(),

            'orderOffers' => OrderOfferResource::collection($this->whenLoaded('orderOffers')),

            $this->mergeWhen(Auth::user()?->tokenCan('view accounts'), [
                'account' => AccountResource::make($this->whenLoaded('account')),
            ]),
            $this->mergeWhen(Auth::user()?->tokenCan('view customers'), [
                'customer' => CustomerResource::make($this->whenLoaded('customer')),
            ]),
            $this->mergeWhen(Auth::user()?->tokenCan('view shipments'), [
                'shipments' => ShipmentResource::collection($this->whenLoaded('shipments')),
            ]),
            $this->mergeWhen(Auth::user()?->tokenCan('view addresses'), [
                'billingAddress'  => AddressResource::make($this->whenLoaded('billingAddress')),
                'shippingAddress' => AddressResource::make($this->whenLoaded('shippingAddress')),
            ]),
        ];
    }
}
