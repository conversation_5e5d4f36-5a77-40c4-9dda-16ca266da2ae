<?php

namespace App\Http\Resources;

use App\Models\AccountAffiliate;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AccountAffiliateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        /** @var AccountAffiliate|self $this */
        return [
            $this->getKeyName() => $this->getKey(),
            'affiliate_source_ref' => $this->affiliate_source_ref,
        ];
    }
}
