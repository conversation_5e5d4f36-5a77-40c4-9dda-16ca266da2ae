APP_NAME="Webseeds App"
APP_ENV=local
APP_KEY=base64:RaVKLIc3SSuRcXtttzdlxmgDEo0jAl0CSc9VdYZ0GpM=
APP_DEBUG=true
APP_URL=https://webseeds.app

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=mysql-test
#DB_HOST=mysql-test-seeds
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

DB_CONNECTION_PRODUCTION=production
DB_HOST_PRODUCTION=mysql-production
DB_READ_HOST_1_PRODUCTION=127.0.0.1
DB_READ_HOST_2_PRODUCTION=127.0.0.1
DB_PORT_PRODUCTION=3306
DB_DATABASE_PRODUCTION=laravel
DB_USERNAME_PRODUCTION=root
DB_PASSWORD_PRODUCTION=

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

SQS_PREFIX=https://sqs.us-east-1.amazonaws.com/285989383109/

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=webseeds
AWS_USE_PATH_STYLE_ENDPOINT=false
AWS_CLOUDFRONT_DISTRIBUTION_ID=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

SENTRY_LARAVEL_DSN=
SENTRY_TRACES_SAMPLE_RATE=1.0

BUYGOODS_POSTBACK_ENABLED=true
BUYGOODS_POSTBACK_KEY=buygoods-test
BUYGOODS_DASHBOARD_USERNAME=
BUYGOODS_DASHBOARD_PASSWORD=

CLICKBANK_API_SECRET=
CLICKBANK_POSTBACK_KEY=clickbank-test
CLICKBANK_POSTBACK_ENABLED=true

DIGISTORE24_IPN_PASSPHRASE=
DIGISTORE24_DEBUG_POSTBACK=
DIGISTORE24_POSTBACK_ENABLED=true
DIGISTORE24_WEBHOOK_KEY=digistore24-test

BASE_CURRENCY=USD

OPEN_EXCHANGE_APP_ID=

DEBUG_EMAIL_TO=
DEBUG_CLICKBANK_POSTBACK=false
DEBUG_BUYGOODS_POSTBACK=false

NOVA_DOMAIN=
NOVA_LICENSE_KEY=

MEDIA_DISK=public
MEDIA_QUEUE=default

LOCAL_ADMIN_NAME="Super Admin"
LOCAL_ADMIN_EMAIL=<EMAIL>
LOCAL_ADMIN_PASSWORD=asdfasdf
LOCAL_ADMIN_API_TOKEN=testApiToken

DEFAULT_EXPERIMENT_DRIVER=Dummy

CONVERT_API_KEY=
CONVERT_API_SECRET=
CONVERT_ACCOUNT_ID=********

SHIPOFFERS_WEBHOOK_KEY=
SHIPOFFERS_DEBUG_WEBHOOK=false

SUCURI_DOMAIN=secret
SUCURI_SECRET=secret
SUCURI_SCANNING_ENABLED=true

MALWARE_NOTIFICATION_EMAIL=<EMAIL>
NETCRAFT_EMAIL=<EMAIL>

CLOUDFLARE_KEY=secret
CLOUDFLARE_ACCOUNT_ID=cloudflare-account-id
CLOUDFLARE_EMAIL=<EMAIL>

HUBSPOT_ACCESS_TOKEN=secret
HUBSPOT_SYNC_ENABLED=false
HUBSPOT_TELLS_CO_EVENT_NAME=event_name

VIRUS_TOTAL_API_KEY=secret
VIRUS_TOTAL_SCANNING_ENABLED=true

MCAFEE_ENABLED=true
NETCRAFT_ENABLED=true
BKAV_ENABLED=true
CMC_ENABLED=true
K7_ENABLED=true
SLACK_ENABLED=true
ACRONIS_ENABLED=true
ANTIY_AVL_ENABLED=true
AVIRA_ENABLED=true
BIT_DEFENDER_ENABLED=true
CYAN_ENABLED=true
DNS8_ENABLED=true
DR_WEB_ENABLED=true
EMSISOFT_ENABLED=trueq
FORCEPOINT_ENABLED=true
FORTINET_ENABLED=true
KASPERSKY_ENABLED=true
LIONIC_ENABLED=true
MALWARE_DOT_COM_ENABLED=true
YANDEX_ENABLED=true

SLACK_BOT_USER_OAUTH_TOKEN=xoxb-secret
SLACK_BOT_USER_DEFAULT_CHANNEL=alerts
SLACK_ALERTS_CHANNEL=alerts
SLACK_MALWARE_SCANNER_CHANNEL=triage-domain-reports
SLACK_MISSING_OFFERS_CHANNEL=triage-missing-offers
SLACK_OFFER_REPORTS_CHANNEL=triage-offer-reports

FAIL_EXPORT_JSON=false

NUMVERIFY_BASE_URL=http://apilayer.net/api/
NUMVERIFY_ACCESS_KEY=secret

VITE_API_BASE_URL=https://webseeds.test/api

ASANA_PERSONAL_TOKEN=
ASANA_WORKSPACE_ID=
ASANA_PROJECT_ID=

GOOGLE_CREDENTIAL_PATH=
GOOGLE_ACCOUNT_ID=

AWS_ADMIN_EMAIL=<EMAIL>

AWS_ROUTE_53_REGION=
AWS_ROUTE_53_ACCESS_KEY_ID=
AWS_ROUTE_53_SECRET_ACCESS_KEY=
AWS_ROUTE_53_ADDRESS_1=
AWS_ROUTE_53_ADDRESS_2=
AWS_ROUTE_53_CITY=
AWS_ROUTE_53_CONTACT_TYPE=
AWS_ROUTE_53_CONTACT_CODE=
AWS_ROUTE_53_EMAIL=
AWS_ROUTE_53_FIRST_NAME=
AWS_ROUTE_53_LAST_NAME=
AWS_ROUTE_53_PHONE=
AWS_ROUTE_53_STATE=
AWS_ROUTE_53_ZIP=

KONNEKTIVE_ENABLED=false

SALESBOUND_LEVEL7_WEBHOOK_ENABLED=false
SALESBOUND_CHECKOUTCHAMP_LOGIN_ID=
SALESBOUND_CHECKOUTCHAMP_PASSWORD=

COMMITTED_COACHES_WEBHOOK_ENABLED=false
COMMITTED_COACHES_WEBHOOK_KEY=
COMMITTED_COACHES_WEBHOOK_URL=
COMMITTED_COACHES_INCLUDED_PRODUCTS=
COMMITTED_COACHES_EXCLUDED_PRODUCTS=
COMMITTED_COACHES_FULFILLED_WEBHOOK_URL=
COMMITTED_COACHES_SQS_ENABLED=false

USPS_CLIENT=
USPS_SECRET=
GEO_IP_SERVICE=
MAXMIND_LICENSE_KEY=

MAROPOST_ENABLED=false
MAROPOST_API_KEY=
MAROPOST_ACCOUNT_ID=

GENDERIZE_API_KEY=

TELLS_CO_WEBHOOK_ENABLED=true
TELLS_CO_WEBHOOK_KEY=secret

SITEMANA_WEBHOOK_ENABLED=true
SITEMANA_WEBHOOK_KEY=secret
