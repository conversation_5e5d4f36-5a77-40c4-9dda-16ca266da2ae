<?php

namespace App\Tests\Traits;

use Google\Service\TagManager\Condition;
use Google\Service\TagManager\Container;
use Google\Service\TagManager\ContainerVersion;
use Google\Service\TagManager\CreateContainerVersionResponse;
use Google\Service\TagManager\ListContainersResponse;
use Google\Service\TagManager\ListWorkspacesResponse;
use Google\Service\TagManager\Tag;
use Google\Service\TagManager\Trigger;
use Google\Service\TagManager\Workspace;
use Google_Service_TagManager_Parameter;

trait MockGoogleTagManagerTrait
{
    public function getContainerList(): ListContainersResponse
    {
        $container = new Container();
        $container->setPublicId('GTM-11111111');
        $container->setContainerId('foo');
        $list = new ListContainersResponse();
        $list->setContainer([$container]);

        return $list;
    }

    public function getContainer(): Container
    {
        $container = new Container();
        $container->setPublicId('GTM-11111111');
        $container->setContainerId('foo');

        return $container;
    }

    public function getWorkspaces(): ListWorkspacesResponse
    {
        $workspace = new Workspace();
        $workspace->setWorkspaceId('bar');
        $list = new ListWorkspacesResponse();
        $list->setWorkspace([$workspace]);

        return $list;
    }

    public function getWorkspace(): Workspace
    {
        $workspace = new Workspace();
        $workspace->setWorkspaceId('bar');

        return $workspace;
    }

    public function getTrigger(): Trigger
    {
        $trigger = new Trigger();
        $trigger->setTriggerId('trigger-foo');

        return $trigger;
    }

    public function getTag(): Tag
    {
        $trigger = new Tag();
        $trigger->setTagId('tag-foo');

        return $trigger;
    }

    public function getTriggerCondition(): Condition
    {
        $condition = new Condition();
        $condition->setType('EQUALS');
        $param1 = new Google_Service_TagManager_Parameter();
        $param1->setKey('arg0');
        $param1->setValue('/');
        $param1->setType('template');

        $param2 = new Google_Service_TagManager_Parameter();
        $param2->setKey('arg1');
        $param2->setValue('/');
        $param2->setType('template');

        $condition->setParameter([$param1, $param2]);

        return $condition;
    }
}