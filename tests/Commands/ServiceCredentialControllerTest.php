<?php

namespace App\Tests\Commands;

use App\Models\Role;
use App\Models\ServiceCredential;
use App\Models\User;
use App\Tests\TestCase;
use Laravel\Sanctum\Sanctum;

class ServiceCredentialControllerTest extends TestCase
{

    private User $user;
    private ServiceCredential $publicServiceCredential;
    private ServiceCredential $privateServiceCredential;

    public function setUp(): void
    {
        parent::setUp();

        // Create a user with appropriate role
        $this->user = User::factory()->create();
        $role = Role::where('name', Role::ADMIN)->first();
        if (!$role) {
            $role = Role::create(['name' => Role::ADMIN]);
        }
        $this->user->assignRole($role);

        // Create test service credentials
        $this->publicServiceCredential = ServiceCredential::factory()->publiclyAvailable()->create([
            'service' => 'test-public',
            'properties' => ['test' => 'value'],
        ]);

        $this->privateServiceCredential = ServiceCredential::factory()->private()->create([
            'service' => 'test-private',
            'properties' => ['test' => 'value'],
        ]);
    }

    /** @test */
    public function it_can_retrieve_publicly_available_service_credential_with_valid_token()
    {
        // Create token with proper abilities
        $token = $this->user->createToken('test', ['view service credentials']);
        Sanctum::actingAs($this->user, ['view service credentials']);

        $response = $this->getJson("/api/service-credentials/{$this->publicServiceCredential->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'service',
                    'credentials',
                    'properties',
                    'expires_at',
                    'created_at',
                    'updated_at',
                ]
            ])
            ->assertJson([
                'data' => [
                    'id' => $this->publicServiceCredential->id,
                    'service' => 'test-public',
                    'properties' => ['test' => 'value'],
                ]
            ]);

        // Ensure credentials are exposed but refresh_token is not
        $response->assertJsonPath('data.credentials', $this->publicServiceCredential->credentials);
        $response->assertJsonMissing(['refresh_token']);
    }

    /** @test */
    public function it_cannot_retrieve_private_service_credential_even_with_valid_token()
    {
        Sanctum::actingAs($this->user, ['view service credentials']);

        $response = $this->getJson("/api/service-credentials/{$this->privateServiceCredential->id}");

        $response->assertStatus(403)
            ->assertJson([
                'message' => 'This service credential is not publicly available'
            ]);
    }

    /** @test */
    public function it_cannot_retrieve_service_credential_without_proper_token_scope()
    {
        // Create token without proper abilities
        Sanctum::actingAs($this->user, ['view customers']); // Wrong scope

        $response = $this->getJson("/api/service-credentials/{$this->publicServiceCredential->id}");

        $response->assertStatus(403);
    }

    /** @test */
    public function it_cannot_retrieve_service_credential_without_authentication()
    {
        $response = $this->getJson("/api/service-credentials/{$this->publicServiceCredential->id}");

        $response->assertStatus(401);
    }

    /** @test */
    public function it_returns_404_for_non_existent_service_credential()
    {
        Sanctum::actingAs($this->user, ['view service credentials']);

        $response = $this->getJson("/api/service-credentials/99999");

        $response->assertStatus(404);
    }

    /** @test */
    public function it_handles_expired_tokens_properly()
    {
        // Create an expired token
        $token = $this->user->createToken('test', ['view service credentials'], now()->subDay());
        
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token->plainTextToken,
        ])->getJson("/api/service-credentials/{$this->publicServiceCredential->id}");

        $response->assertStatus(401);
    }

    /** @test */
    public function it_validates_token_abilities_correctly()
    {
        // Test with multiple abilities where one matches
        Sanctum::actingAs($this->user, ['view customers', 'view service credentials', 'view accounts']);

        $response = $this->getJson("/api/service-credentials/{$this->publicServiceCredential->id}");

        $response->assertStatus(200);
    }

    /** @test */
    public function it_ensures_no_index_route_is_available()
    {
        Sanctum::actingAs($this->user, ['view service credentials']);

        $response = $this->getJson("/api/service-credentials");

        $response->assertStatus(404); // Route should not exist
    }

    /** @test */
    public function it_exposes_credentials_but_not_refresh_token()
    {
        // Create a service credential with both credentials and refresh_token
        $credentialWithRefreshToken = ServiceCredential::factory()->publiclyAvailable()->create([
            'service' => 'oauth-service',
            'credentials' => ['access_token' => 'access123', 'token_type' => 'Bearer'],
            'refresh_token' => 'refresh456',
            'properties' => ['scope' => 'read'],
        ]);

        Sanctum::actingAs($this->user, ['view service credentials']);

        $response = $this->getJson("/api/service-credentials/{$credentialWithRefreshToken->id}");

        $response->assertStatus(200)
            ->assertJsonPath('data.credentials', ['access_token' => 'access123', 'token_type' => 'Bearer'])
            ->assertJsonMissing(['refresh_token']);

        // Ensure the response doesn't contain refresh_token anywhere
        $this->assertStringNotContainsString('refresh456', $response->getContent());
    }
}
