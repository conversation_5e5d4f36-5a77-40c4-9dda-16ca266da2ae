<?php

namespace App\Tests\Commands;

use App\Enums\AccountSource;
use App\Enums\PayloadSource;
use App\Models\Account;
use App\Models\Order;
use App\Models\Payload;
use App\Tests\TestCase;
use Config;
use DB;
use Event;
use Illuminate\Support\Facades\Artisan;

class RepairTrackingUtmTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();

        Config::set('database.validate_models', false);
    }

    /** @test */
    public function saveTrackingUtm()
    {
        Event::fake();
        //Buygoods
        $bgPayload = $this->getFixtureByPath('BuygoodsIpnStrategyTest/import.json');
        $bgPayload['vid1'] = 'affiliate||TestMedium||test.com||FOO_BAR||';
        $bgAccount = Account::factory()->create(['source' => AccountSource::BUYGOODS->value]);
        $bgOrder = Order::factory()->create(['account_id' => $bgAccount->id]);
        $payload1 = Payload::factory()->create([
            'source' => PayloadSource::BUYGOODS_IPN->value,
            'payload' => json_encode($bgPayload),
        ]);
        $payload1->orders()->save($bgOrder);

        $this->assertCount(0, $bgOrder->tracking);
        $this->assertCount(0, $bgOrder->trackingUtms);

        Artisan::call('repair:tracking-utm');

        $bgOrder->refresh();
        $this->assertCount(11, $bgOrder->tracking);
        $this->assertCount(4, $bgOrder->trackingUtms);

        //Digistore
        $data = ['trackingkey' => 'affiliate||TestMedium||test.com||FOO_BAR||'];
        $dsAccount = Account::factory()->create(['source' => AccountSource::DIGISTORE24->value]);
        $dsOrder = Order::factory()->create(['account_id' => $dsAccount->id]);
        $payload2 = Payload::factory()->create([
            'source' => PayloadSource::DIGISTORE_IPN->value,
            'payload' => json_encode($data),
        ]);
        $payload2->orders()->save($dsOrder);

        $this->assertCount(0, $dsOrder->tracking);
        $this->assertCount(0, $dsOrder->trackingUtms);

        Artisan::call('repair:tracking-utm');

        $dsOrder->refresh();
        $this->assertCount(1, $dsOrder->tracking);
        $this->assertCount(4, $dsOrder->trackingUtms);

        //Clickbank
        $data = $this->getFixtureByPath('/ImportOrderServiceTest/cb_ins_sale.json');
        $cbAccount = Account::factory()->create(['source' => AccountSource::CLICKBANK->value]);
        $cbOrder = Order::factory()->create(['account_id' => $cbAccount->id]);
        $payload2 = Payload::factory()->create([
            'source' => PayloadSource::CLICKBANK_INS->value,
            'payload' => json_encode($data),
        ]);
        $payload2->orders()->save($cbOrder);

        $this->assertCount(0, $cbOrder->tracking);
        $this->assertCount(0, $cbOrder->trackingUtms);

        Artisan::call('repair:tracking-utm');

        $cbOrder->refresh();
        $this->assertCount(4, $cbOrder->tracking);
        $this->assertCount(4, $cbOrder->trackingUtms);
    }
}
