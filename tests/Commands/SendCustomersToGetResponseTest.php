<?php

namespace App\Tests\Commands;

use App\Console\Commands\SendCustomersToGetResponse;
use App\Enums\MarketingPlatform;
use App\Models\MarketingList;
use App\Models\MarketingListLimit;
use App\Tests\TestCase;
use Illuminate\Support\Facades\Storage;

class SendCustomersToGetResponseTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();

        // Mock the S3 storage
        Storage::fake('s3');
    }

    /** @test */
    public function it_loads_domain_groups_from_database_for_get_response_lists()
    {
        // Create a GetResponse marketing list
        $marketingList = MarketingList::factory()->create([
            'platform' => MarketingPlatform::GET_RESPONSE->value,
            'list_id'  => 'test-list-123',
        ]);

        // Create marketing list limits
        $limits = [
            ['group' => 'group1', 'domains' => ['gmail.com'], 'limit' => 50],
            ['group' => 'group2', 'domains' => ['yahoo.com', 'aol.com'], 'limit' => 30],
            ['group' => 'group3', 'domains' => ['hotmail.com', 'outlook.com'], 'limit' => 25],
        ];

        foreach ($limits as $limitData) {
            MarketingListLimit::factory()->create([
                'marketing_list_id' => $marketingList->id,
                'group'             => $limitData['group'],
                'domains'           => $limitData['domains'],
                'limit'             => $limitData['limit'],
            ]);
        }

        // Test the domain group loading directly
        $command = new SendCustomersToGetResponse();

        // Use reflection to access the private method
        $reflection = new \ReflectionClass($command);
        $method = $reflection->getMethod('loadDomainGroupsForMarketingList');
        $method->setAccessible(true);

        // Call the method
        $method->invoke($command, $marketingList);

        // Use reflection to access the private property
        $domainGroupsProperty = $reflection->getProperty('domainGroups');
        $domainGroupsProperty->setAccessible(true);
        $domainGroups = $domainGroupsProperty->getValue($command);

        // Verify domain groups were loaded correctly
        $this->assertCount(3, $domainGroups);
        $this->assertArrayHasKey('group1', $domainGroups);
        $this->assertArrayHasKey('group2', $domainGroups);
        $this->assertArrayHasKey('group3', $domainGroups);

        $this->assertEquals(['gmail.com'], $domainGroups['group1']['domains']);
        $this->assertEquals(50, $domainGroups['group1']['limit']);

        $this->assertEquals(['yahoo.com', 'aol.com'], $domainGroups['group2']['domains']);
        $this->assertEquals(30, $domainGroups['group2']['limit']);

        $this->assertEquals(['hotmail.com', 'outlook.com'], $domainGroups['group3']['domains']);
        $this->assertEquals(25, $domainGroups['group3']['limit']);
    }

    /** @test */
    public function it_returns_empty_domain_groups_when_no_limits_configured()
    {
        // Create a GetResponse marketing list without limits
        $marketingList = MarketingList::factory()->create([
            'platform' => MarketingPlatform::GET_RESPONSE->value,
            'list_id'  => 'test-list-no-limits',
        ]);

        // Verify no limits exist for this marketing list
        $limits = MarketingListLimit::where('marketing_list_id', $marketingList->id)->get();
        $this->assertCount(0, $limits);

        // This test verifies that when no limits are configured,
        // the seeder should be run to populate them
        $this->assertTrue(true); // This test passes if no limits exist
    }

    /** @test */
    public function it_loads_domain_groups_with_correct_limits()
    {
        // Create a GetResponse marketing list
        $marketingList = MarketingList::factory()->create([
            'platform' => MarketingPlatform::GET_RESPONSE->value,
            'list_id'  => 'test-list-limits',
        ]);

        // Create a marketing list limit with a specific limit for testing
        MarketingListLimit::factory()->create([
            'marketing_list_id' => $marketingList->id,
            'group'             => 'gmail_group',
            'domains'           => ['gmail.com'],
            'limit'             => 1, // Very low limit for testing
        ]);

        // Test the domain group loading directly
        $command = new SendCustomersToGetResponse();

        // Use reflection to access the private method
        $reflection = new \ReflectionClass($command);
        $method = $reflection->getMethod('loadDomainGroupsForMarketingList');
        $method->setAccessible(true);

        // Call the method
        $method->invoke($command, $marketingList);

        // Use reflection to access the private property
        $domainGroupsProperty = $reflection->getProperty('domainGroups');
        $domainGroupsProperty->setAccessible(true);
        $domainGroups = $domainGroupsProperty->getValue($command);

        // Verify domain groups were loaded correctly
        $this->assertCount(1, $domainGroups);
        $this->assertArrayHasKey('gmail_group', $domainGroups);
        $this->assertEquals(['gmail.com'], $domainGroups['gmail_group']['domains']);
        $this->assertEquals(1, $domainGroups['gmail_group']['limit']);
    }

    /** @test */
    public function it_can_find_domain_group_by_domain()
    {
        // Create a GetResponse marketing list
        $marketingList = MarketingList::factory()->create([
            'platform' => MarketingPlatform::GET_RESPONSE->value,
            'list_id'  => 'test-list-domain-lookup',
        ]);

        // Create marketing list limits
        MarketingListLimit::factory()->create([
            'marketing_list_id' => $marketingList->id,
            'group'             => 'gmail_group',
            'domains'           => ['gmail.com'],
            'limit'             => 50,
        ]);

        MarketingListLimit::factory()->create([
            'marketing_list_id' => $marketingList->id,
            'group'             => 'yahoo_group',
            'domains'           => ['yahoo.com', 'aol.com'],
            'limit'             => 30,
        ]);

        // Test the domain group loading and lookup
        $command = new SendCustomersToGetResponse();

        // Use reflection to access private methods
        $reflection = new \ReflectionClass($command);

        $loadMethod = $reflection->getMethod('loadDomainGroupsForMarketingList');
        $loadMethod->setAccessible(true);
        $loadMethod->invoke($command, $marketingList);

        $lookupMethod = $reflection->getMethod('getGroupByDomain');
        $lookupMethod->setAccessible(true);

        // Test domain lookups
        $this->assertEquals('gmail_group', $lookupMethod->invoke($command, 'gmail.com'));
        $this->assertEquals('yahoo_group', $lookupMethod->invoke($command, 'yahoo.com'));
        $this->assertEquals('yahoo_group', $lookupMethod->invoke($command, 'aol.com'));
        $this->assertNull($lookupMethod->invoke($command, 'unknown.com'));
    }
}
