<?php

namespace App\Tests\Commands\MonitorData;


use App\Events\OrderSaved;
use App\Models\Account;
use App\Models\Order;
use App\Services\DataMonitor\DataMonitorService;
use App\Services\DataMonitor\Monitors\TransactionAmountOrderTotalMismatch;
use App\Tests\TestCase;

class TransactionAmountOrderTotalMismatchTest extends TestCase
{
    /** @test */
    public function it_finds_transactions_with_amount_that_mismatches_order_total()
    {
        $account = Account::factory()->create();
        $order = Order::factory()->create([
            'account_id' => $account->id,
            'total_amount' => money(100.00),
        ]);
        $order->total_amount = money(200.00);
        $order->saveQuietly();

        $dataMonitor = new DataMonitorService();
        $dataMonitor->run(TransactionAmountOrderTotalMismatch::class);

        $this->assertCount(1, $dataMonitor->monitorStrategy->monitor()->get());
    }
}
