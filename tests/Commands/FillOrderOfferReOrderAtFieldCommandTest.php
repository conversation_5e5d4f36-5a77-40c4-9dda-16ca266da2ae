<?php

namespace App\Tests\Commands;

use App\Facades\HubSpot;
use App\Models\Customer;
use App\Models\Offer;
use App\Models\Order;
use App\Models\OrderOffer;
use App\Services\HubSpot\HubspotCustomerService;
use App\Tests\TestCase;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Config;
use Mockery\MockInterface;

class FillOrderOfferReOrderAtFieldCommandTest extends TestCase
{
    public function testHandleSuccess()
    {
        Config::set('services.hubspot.sync_enabled', true);
        $customer = Customer::factory()->create(['numverify_valid' => true, 'zerobounce_status' => true]);
        $order = Order::factory()->create([
            'customer_id'  => $customer->id,
            'purchased_at' => now(),
        ]);

        $mock = $this->partialMock(
            HubspotCustomerService::class,
            function (MockInterface $mock) {
                $mock->shouldReceive('createHubspotContact')->andReturn(['id' => 1]);
            }
        );

        app()->instance(HubspotCustomerService::class, $mock);

        HubSpot::shouldReceive('searchProduct');
        HubSpot::shouldReceive('getAssociations');
        HubSpot::shouldReceive('createLineItem')->andReturn(['id' => 1]);
        HubSpot::shouldReceive('createProduct')->andReturn(['id' => 1]);
        HubSpot::shouldReceive('updateDeal')->andReturn(['id' => 1]);

        $oo1 = OrderOffer::factory()->create(
            ['order_id' => $order->id, 'quantity' => 1, 'hubspot_id' => 1, 're_order_at' => null]
        );
        $oo2 = OrderOffer::factory()->create(
            ['order_id' => $order->id, 'quantity' => 3, 'hubspot_id' => 2, 're_order_at' => null]
        );

        Artisan::call('order-offer:fill-re-order-at');

        $oo1->refresh();
        $oo2->refresh();

        $this->assertEquals(now()->addDays(30), $oo1->re_order_at);
        $this->assertEquals(now()->addDays(90), $oo2->re_order_at);
    }

    public function testParentOrderSuccess()
    {
        Config::set('services.hubspot.sync_enabled', true);
        $customer = Customer::factory()->create(['numverify_valid' => true, 'zerobounce_status' => true]);
        $parent = Order::factory()->create([
            'customer_id'  => $customer->id,
            'source_ref'   => 'PARENT',
            'purchased_at' => now()->subDays(),
        ]);

        $upsell1 = Order::factory()->create([
            'source_ref'      => 'UPSELL-1',
            'parent_order_id' => $parent->id,
            'customer_id'     => $customer->id,
            'purchased_at'    => now(),
        ]);

        $upsell2 = Order::factory()->create([
            'source_ref'      => 'UPSELL-2',
            'parent_order_id' => $parent->id,
            'customer_id'     => $customer->id,
            'purchased_at'    => now()->subDays(2),
        ]);

        $mock = $this->partialMock(
            HubspotCustomerService::class,
            function (MockInterface $mock) {
                $mock->shouldReceive('createHubspotContact')->andReturn(['id' => 1]);
            }
        );
        $offer1 = Offer::factory()->create();
        $offer2 = Offer::factory()->create();

        app()->instance(HubspotCustomerService::class, $mock);

        HubSpot::shouldReceive('searchProduct');
        HubSpot::shouldReceive('getAssociations');
        HubSpot::shouldReceive('createLineItem')->andReturn(['id' => 1]);
        HubSpot::shouldReceive('createProduct')->andReturn(['id' => 1]);
        HubSpot::shouldReceive('updateDeal')->andReturn(['id' => 1]);

        $ooParent = OrderOffer::factory()->create(
            [
                'order_id'    => $parent->id,
                'offer_id'    => $offer1->id,
                'quantity'    => 1,
                'hubspot_id'  => 1,
                're_order_at' => null,
            ]
        );
        $ooUpsell1 = OrderOffer::factory()->create(
            [
                'order_id'    => $upsell1->id,
                'offer_id'    => $offer1->id,
                'quantity'    => 3,
                'hubspot_id'  => 2,
                're_order_at' => null,
            ]
        );
        $ooUpsell2 = OrderOffer::factory()->create(
            [
                'order_id'    => $upsell2->id,
                'offer_id'    => $offer2->id,
                'quantity'    => 2,
                'hubspot_id'  => 2,
                're_order_at' => null,
            ]
        );

        Artisan::call('order-offer:fill-re-order-at');

        $ooParent->refresh();
        $ooUpsell1->refresh();
        $ooUpsell2->refresh();

        $this->assertEquals(now()->subDays()->addDays(4 * 30), $ooParent->re_order_at);
        $this->assertEquals(now()->subDays(2)->addDays(2 * 30), $ooUpsell2->re_order_at);
        $this->assertNull($ooUpsell1->re_order_at);
    }
}
