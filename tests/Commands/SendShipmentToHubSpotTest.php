<?php

namespace App\Tests\Commands;

use App\Console\Commands\SendShipmentToHubspot;
use App\Enums\ShipmentStatus;
use App\Facades\HubSpot;
use App\Jobs\HubspotUpdateDealShipment;
use App\Models\Offer;
use App\Models\Order;
use App\Models\OrderOffer;
use App\Models\OrderShipment;
use App\Models\Shipment;
use App\Tests\TestCase;
use Carbon\Carbon;
use Cknow\Money\Money;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Queue;

class SendShipmentToHubSpotTest extends TestCase
{
    public function testNotDispatchJobStatusNotShipped()
    {
        Config::set('services.hubspot.sync_enabled', true);
        Queue::fake();
        $shipment = Shipment::factory()->create(['status' => ShipmentStatus::CANCELED->value]);
        $order = Order::factory()->create();
        $order->shipments()->save($shipment);
        $command = app()->make(SendShipmentToHubspot::class);
        $command->handle();
        Queue::assertNotPushed(HubspotUpdateDealShipment::class);
    }

    public function testDispatchJob()
    {
        Config::set('services.hubspot.sync_enabled', true);
        Queue::fake();
        $shipment = Shipment::factory()->create([
            'status' => ShipmentStatus::SHIPPED->value,
            'created_at' => Carbon::parse('2024-02-01')->addDay()
        ]);
        $order = Order::factory()->create();
        $order->shipments()->save($shipment);
        /** @var SendShipmentToHubspot $command */
        $command = app()->make(SendShipmentToHubspot::class);
        $command->handle();
        Queue::assertPushed(HubspotUpdateDealShipment::class);
    }

    public function testSendTwoOrders()
    {
        Config::set('services.hubspot.sync_enabled', true);
        Hubspot::shouldReceive('updateDeal')->twice()->andReturn(['dealId' => 1]);
        $shipment = Shipment::factory()->create([
            'status' => ShipmentStatus::SHIPPED->value,
            'created_at' => Carbon::parse('2024-02-01')->addDay()
        ]);
        $offer = Offer::factory()->create();
        $order = Order::factory()->create();
        $oo = new OrderOffer();
        $oo->order_id = $order->id;
        $oo->offer_id = $offer->id;
        $oo->hubspot_id = 1111111;
        $oo->name = 'Foo';
        $oo->amount = Money::USD('10.00');
        $oo->saveQuietly();

        $order2 = Order::factory()->create();
        $oo = new OrderOffer();
        $oo->order_id = $order2->id;
        $oo->offer_id = $offer->id;
        $oo->hubspot_id = 1111111;
        $oo->name = 'Bar';
        $oo->amount = Money::USD('10.00');
        $oo->saveQuietly();

        $orderShipment = new OrderShipment();
        $orderShipment->order_id = $order->id;
        $orderShipment->shipment_id = $shipment->id;
        $orderShipment->save();
        $orderShipment = new OrderShipment();
        $orderShipment->order_id = $order2->id;
        $orderShipment->shipment_id = $shipment->id;
        $orderShipment->save();
        $shipment = Shipment::find($shipment->id);
        $this->assertCount(2, $shipment->orders);
        $command = app()->make(SendShipmentToHubspot::class);
        $command->handle();
    }
}