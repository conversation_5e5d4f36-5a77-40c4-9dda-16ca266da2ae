<?php

namespace App\Tests\Commands;

use App\Enums\MarketingPlatform;
use App\Jobs\Marketing\SendCustomerToAweberJob;
use App\Jobs\Marketing\SendCustomerToGetResponseJob;
use App\Jobs\Marketing\SendCustomerToMaropostJob;
use App\Models\Account;
use App\Models\Customer;
use App\Models\MarketingList;
use App\Models\MarketingListLimit;
use App\Models\Order;
use Carbon\Carbon;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Cache;
use App\Tests\TestCase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Queue;

class SendCustomerToMarketingPlatformTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();
        Cache::flush();
    }

    /** @test */
    public function it_sends_customers_to_the_correct_marketing_jobs()
    {
        Queue::fake();
        Config::set("services.marketing.command_enabled", true);
        $platforms = [
           // MarketingPlatform::MAROPOST->value => SendCustomerToMaropostJob::class,
            MarketingPlatform::GET_RESPONSE->value => SendCustomerToGetResponseJob::class,
            MarketingPlatform::AWEBER->value => SendCustomerToAweberJob::class,
        ];

        $customer = Customer::factory()->create([
            'email' => '<EMAIL>',
            'zerobounce_status' => 'valid',
        ]);
        $customer->marketingLists()->detach();
        $account = Account::factory()->create();
        Order::factory()->create([
            'account_id' => $account->id,
            'customer_id' => $customer->id
        ]);

        foreach ($platforms as $platform => $jobClass) {
            $marketingList[$platform] = MarketingList::factory()->create([
                'platform' => $platform,
                'marketable_id' => $account->id,
                'marketable_type' => 'accounts',
                'sync_status' => 'automatic'
            ]);

            MarketingListLimit::factory()->create([
                'marketing_list_id' => $marketingList[$platform]->id,
                'domains' => ['example.com'],
                'limit' => 5,
            ]);
        }

        $this->artisan('customer:send-to-marketing-platform');

        foreach ($platforms as $platform => $jobClass) {
            Queue::assertPushed($jobClass, function ($job) use ($customer) {
                return $job->customer->id === $customer->id;
            });
            $limitGroup = MarketingListLimit::where(['marketing_list_id' => $marketingList[$platform]->id])->first();
            $this->assertEquals(1, Cache::get($platform . '_' . $marketingList[$platform]->id . '_' . $limitGroup->id));
            $listDailyCountKey = 'daily_count_list_' . $marketingList[$platform]->id . '_' . now()->format('Y-m-d');
            $this->assertEquals(1, Cache::get($listDailyCountKey));
        }
    }
    /** @test */
    public function it_sends_customers_to_the_correct_marketing_jobs_with_filter_by_date()
    {
        Queue::fake();
        Config::set("services.marketing.command_enabled", true);
        $platforms = [
            //MarketingPlatform::MAROPOST->value => SendCustomerToMaropostJob::class,
            MarketingPlatform::GET_RESPONSE->value => SendCustomerToGetResponseJob::class,
            MarketingPlatform::AWEBER->value => SendCustomerToAweberJob::class,
        ];
        $account = Account::factory()->create();

        $customer = Customer::factory()->create([
            'email' => '<EMAIL>',
            'zerobounce_status' => 'valid',
            'created_at' => Carbon::now()
        ]);
        $customer->marketingLists()->detach();
        Order::factory()->create([
            'account_id' => $account->id,
            'customer_id' => $customer->id
        ]);

        $customer2 = Customer::factory()->create([
            'email' => '<EMAIL>',
            'zerobounce_status' => 'valid',
            'created_at' => Carbon::now()->subMonth()
        ]);
        $customer2->marketingLists()->detach();
        Order::factory()->create([
            'account_id' => $account->id,
            'customer_id' => $customer2->id
        ]);

        foreach ($platforms as $platform => $jobClass) {
            $marketingList[$platform] = MarketingList::factory()->create([
                'platform' => $platform,
                'marketable_id' => $account->id,
                'marketable_type' => 'accounts',
            ]);

            MarketingListLimit::factory()->create([
                'marketing_list_id' => $marketingList[$platform]->id,
                'domains' => ['example.com'],
                'limit' => 5,
            ]);
        }

        $start = Carbon::now()->subWeek()->format('Y-m-d');
        $end = Carbon::now()->format('Y-m-d');
        $this->artisan('customer:send-to-marketing-platform ' . $start .' ' . $end);

        foreach ($platforms as $platform => $jobClass) {
            Queue::assertPushed($jobClass, function ($job) use ($customer) {
                return $job->customer->id === $customer->id;
            });
            $limitGroup = MarketingListLimit::where(['marketing_list_id' => $marketingList[$platform]->id])->first();
            $this->assertEquals(1, Cache::get($platform . '_' . $marketingList[$platform]->id . '_' . $limitGroup->id));
        }
    }

    /** @test */
    public function it_skips_customers_when_limit_is_reached()
    {
        Queue::fake();

        $platform = MarketingPlatform::GET_RESPONSE->value;
        $customer = Customer::factory()->create([
            'email' => '<EMAIL>',
            'zerobounce_status' => 'valid',
        ]);
        $customer->marketingLists()->detach();

        $account = Account::factory()->create();
        Order::factory()->create([
            'account_id' => $account->id,
            'customer_id' => $customer->id
        ]);
        $marketingList = MarketingList::factory()->create([
            'platform' => $platform,
            'marketable_id' => $account->id,
            'marketable_type' => 'accounts',
        ]);

        $limitGroup = MarketingListLimit::factory()->create([
            'marketing_list_id' => $marketingList->id,
            'domains' => ['example.com'],
            'limit' => 1,
        ]);

        Cache::put($platform . '_' . $marketingList->id . '_' . $limitGroup->id, 1);

        $this->artisan('customer:send-to-marketing-platform');
        Queue::assertNotPushed(SendCustomerToGetResponseJob::class);
    }
    public function test_command_respects_daily_limit_and_does_not_dispatch_jobs()
    {
        Bus::fake();
        $platform = MarketingPlatform::GET_RESPONSE;
        foreach (MarketingList::get() as $marketingList) {
            $marketingList->delete();
        }
        $marketingList = MarketingList::factory()->create([
            'platform' => $platform->value,
        ]);

        Customer::factory()->count(10)->create(['zerobounce_status' => 'valid'])->each(function ($customer) use ($marketingList) {
            $customer->marketingLists()->detach($marketingList->id);
        });

        $listDailyCountKey = 'daily_count_list_' . $marketingList->id . '_' . now()->format('Y-m-d');

        Cache::set($listDailyCountKey, 250);

        Artisan::call('customer:send-to-marketing-platform');

        Bus::assertNotDispatched(SendCustomerToGetResponseJob::class);
    }}