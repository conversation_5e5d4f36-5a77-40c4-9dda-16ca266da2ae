<?php

namespace Commands;

use App\Console\Commands\RepairMarketingListDiscrepancy;
use App\Enums\MarketingListSyncStatus;
use App\Enums\MarketingPlatform;
use App\Enums\OrderStatus;
use App\Models\Account;
use App\Models\Brand;
use App\Models\Customer;
use App\Models\MarketingList;
use App\Models\Offer;
use App\Models\Order;
use App\Models\OrderOffer;
use App\Models\Product;
use App\Tests\TestCase;
use Carbon\Carbon;
use Cknow\Money\Money;
use Config;
use Illuminate\Support\Facades\Artisan;

class RepairMarketingListDiscrepancyTest extends TestCase
{
    private Customer $customer;
    private Account $account;
    private Brand $brand;
    private Product $product;
    private Offer $offer;
    private Order $order;

    public function setUp(): void
    {
        parent::setUp();

        Config::set('database.validate_models', false);

        // Create test data
        $this->brand = Brand::factory()->create();
        $this->account = Account::factory()->create(['brand_id' => $this->brand->id]);
        $this->customer = Customer::factory()->create(['zerobounce_status' => 'valid']);
        $this->product = Product::factory()->create();
        $this->offer = Offer::factory()->create([
            'account_id' => $this->account->id,
            'price' => Money::parse('100'),
        ]);

        // Attach product to offer
        $this->offer->products()->attach($this->product);

        $this->order = Order::factory()->create([
            'customer_id' => $this->customer->id,
            'account_id' => $this->account->id,
            'status' => OrderStatus::COMPLETED->value,
            'purchased_at' => Carbon::now()->subDays(5),
        ]);

        // Create OrderOffer record instead of just attaching
        OrderOffer::factory()->create([
            'order_id' => $this->order->id,
            'offer_id' => $this->offer->id,
            'quantity' => 1,
            'amount' => Money::parse('100'),
        ]);
    }

    /** @test */
    public function command_runs_successfully_with_no_discrepancies()
    {
        // Update the order to be within the default date range (last 30 days)
        $this->order->update(['purchased_at' => Carbon::now()->subDays(5)]);

        // Use artisan() helper instead of Artisan::call()
        $this->artisan('repair:marketing-list-discrepancy')
            ->expectsOutput('No discrepancies found in the specified date range.')
            ->assertExitCode(0);
    }

    /** @test */
    public function command_identifies_aweber_discrepancy()
    {
        // Update the order to be within the default date range (last 30 days)
        $this->order->update(['purchased_at' => Carbon::now()->subDays(5)]);

        // Create Aweber marketing list for the product
        MarketingList::factory()->create([
            'platform' => MarketingPlatform::AWEBER->value,
            'list_id' => 'aweber-list-123',
            'marketable_type' => Product::class,
            'marketable_id' => $this->product->id,
            'sync_status' => MarketingListSyncStatus::AUTOMATIC->value,
            'options' => ['order_status' => OrderStatus::COMPLETED->value],
        ]);

        $exitCode = Artisan::call(RepairMarketingListDiscrepancy::class, ['--limit' => 10]);

        $this->assertEquals(0, $exitCode);
        $output = Artisan::output();
        $this->assertStringContainsString('AWEBER DISCREPANCIES', $output);
        $this->assertStringContainsString('Aweber: 1', $output);
    }

    /** @test */
    public function command_identifies_getresponse_discrepancy()
    {
        // Update the order to be within the default date range (last 30 days)
        $this->order->update(['purchased_at' => Carbon::now()->subDays(5)]);

        // Create GetResponse marketing list for the brand
        MarketingList::factory()->create([
            'platform' => MarketingPlatform::GET_RESPONSE->value,
            'list_id' => 'getresponse-list-456',
            'marketable_type' => Brand::class,
            'marketable_id' => $this->brand->id,
            'sync_status' => MarketingListSyncStatus::AUTOMATIC->value,
            'options' => ['order_status' => OrderStatus::COMPLETED->value],
        ]);

        $exitCode = Artisan::call(RepairMarketingListDiscrepancy::class, ['--limit' => 10]);

        $this->assertEquals(0, $exitCode);
        $output = Artisan::output();
        $this->assertStringContainsString('GETRESPONSE DISCREPANCIES', $output);
        $this->assertStringContainsString('GetResponse: 1', $output);
    }

    /** @test */
    public function command_does_not_identify_discrepancy_when_customer_already_in_marketing_list()
    {
        // Update the order to be within the default date range (last 30 days)
        $this->order->update(['purchased_at' => Carbon::now()->subDays(5)]);

        // Create marketing list
        $marketingList = MarketingList::factory()->create([
            'platform' => MarketingPlatform::AWEBER->value,
            'list_id' => 'aweber-list-123',
            'marketable_type' => Product::class,
            'marketable_id' => $this->product->id,
            'sync_status' => MarketingListSyncStatus::AUTOMATIC->value,
            'options' => ['order_status' => OrderStatus::COMPLETED->value],
        ]);

        // Add customer to marketing list
        $this->customer->marketingLists()->attach($marketingList, ['subscribed_at' => now()]);

        $exitCode = Artisan::call(RepairMarketingListDiscrepancy::class);

        $this->assertEquals(0, $exitCode);
        $this->assertStringContainsString('No discrepancies found', Artisan::output());
    }

    /** @test */
    public function command_ignores_customers_with_invalid_zerobounce_status()
    {
        // Update the order to be within the default date range (last 30 days)
        $this->order->update(['purchased_at' => Carbon::now()->subDays(5)]);

        // Update customer to have invalid zerobounce status
        $this->customer->update(['zerobounce_status' => 'invalid']);

        // Create marketing list
        MarketingList::factory()->create([
            'platform' => MarketingPlatform::AWEBER->value,
            'list_id' => 'aweber-list-123',
            'marketable_type' => Product::class,
            'marketable_id' => $this->product->id,
            'sync_status' => MarketingListSyncStatus::AUTOMATIC->value,
            'options' => ['order_status' => OrderStatus::COMPLETED->value],
        ]);

        $exitCode = Artisan::call(RepairMarketingListDiscrepancy::class);

        $this->assertEquals(0, $exitCode);
        $this->assertStringContainsString('No discrepancies found', Artisan::output());
    }

    /** @test */
    public function command_respects_date_range()
    {
        // Create marketing list
        MarketingList::factory()->create([
            'platform' => MarketingPlatform::AWEBER->value,
            'list_id' => 'aweber-list-123',
            'marketable_type' => Product::class,
            'marketable_id' => $this->product->id,
            'sync_status' => MarketingListSyncStatus::AUTOMATIC->value,
            'options' => ['order_status' => OrderStatus::COMPLETED->value],
        ]);

        // Run command with date range that excludes the order
        $startDate = Carbon::now()->subDays(2)->format('Y-m-d');
        $endDate = Carbon::now()->subDays(1)->format('Y-m-d');

        $exitCode = Artisan::call(RepairMarketingListDiscrepancy::class, [
            'start' => $startDate,
            'end' => $endDate,
        ]);
        
        $this->assertEquals(0, $exitCode);
        $this->assertStringContainsString('No discrepancies found', Artisan::output());
    }

    /** @test */
    public function command_handles_both_platforms_simultaneously()
    {
        // Update the order to be within the default date range (last 30 days)
        $this->order->update(['purchased_at' => Carbon::now()->subDays(5)]);

        // Create marketing lists for both platforms
        MarketingList::factory()->create([
            'platform' => MarketingPlatform::AWEBER->value,
            'list_id' => 'aweber-list-123',
            'marketable_type' => Product::class,
            'marketable_id' => $this->product->id,
            'sync_status' => MarketingListSyncStatus::AUTOMATIC->value,
            'options' => ['order_status' => OrderStatus::COMPLETED->value],
        ]);

        MarketingList::factory()->create([
            'platform' => MarketingPlatform::GET_RESPONSE->value,
            'list_id' => 'getresponse-list-456',
            'marketable_type' => Brand::class,
            'marketable_id' => $this->brand->id,
            'sync_status' => MarketingListSyncStatus::AUTOMATIC->value,
            'options' => ['order_status' => OrderStatus::COMPLETED->value],
        ]);

        $exitCode = Artisan::call(RepairMarketingListDiscrepancy::class, ['--limit' => 10]);

        $this->assertEquals(0, $exitCode);
        $output = Artisan::output();
        $this->assertStringContainsString('AWEBER DISCREPANCIES', $output);
        $this->assertStringContainsString('GETRESPONSE DISCREPANCIES', $output);
        $this->assertStringContainsString('Aweber: 1', $output);
        $this->assertStringContainsString('GetResponse: 1', $output);
        $this->assertStringContainsString('Combined Total: 2', $output);
    }
}
