<?php

namespace App\Tests\Commands;

use App\Enums\OrderStatus;
use App\Enums\PayloadSource;
use App\Enums\PayloadStatus;
use App\Models\Account;
use App\Models\Brand;
use App\Models\Customer;
use App\Models\Offer;
use App\Models\Order;
use App\Models\OrderOffer;
use App\Models\Payload;
use App\Models\Product;
use App\Tests\TestCase;
use Carbon\Carbon;
use Cknow\Money\Money;
use Config;

class RepairHubspotDiscrepancyTest extends TestCase
{
    private Customer $customer;
    private Account $account;
    private Brand $brand;
    private Product $product;
    private Offer $offer;
    private Order $order;

    public function setUp(): void
    {
        parent::setUp();

        Config::set('database.validate_models', false);

        // Create test data
        $this->brand = Brand::factory()->create();
        $this->account = Account::factory()->create(['brand_id' => $this->brand->id]);
        $this->customer = Customer::factory()->create([
            'zerobounce_status' => 'valid',
            'numverify_valid' => true,
            'hubspot_id' => null, // No hubspot_id initially
        ]);
        $this->product = Product::factory()->create();
        $this->offer = Offer::factory()->create([
            'account_id' => $this->account->id,
            'price' => Money::parse('100'),
        ]);
        
        // Attach product to offer
        $this->offer->products()->attach($this->product);

        $this->order = Order::factory()->create([
            'customer_id' => $this->customer->id,
            'account_id' => $this->account->id,
            'status' => OrderStatus::COMPLETED->value,
            'purchased_at' => Carbon::now()->subDays(5),
        ]);

        // Create OrderOffer record
        OrderOffer::factory()->create([
            'order_id' => $this->order->id,
            'offer_id' => $this->offer->id,
            'quantity' => 1,
            'amount' => Money::parse('100'),
        ]);
    }

    /** @test */
    public function command_runs_successfully_with_no_discrepancies()
    {
        $this->artisan('repair:hubspot-discrepancy')
            ->assertExitCode(0);
    }

    /** @test */
    public function command_runs_successfully_with_basic_options()
    {
        $this->artisan('repair:hubspot-discrepancy', ['--limit' => 10])
            ->expectsOutput('Order type filter: both')
            ->expectsOutput('Sample limit: 10')
            ->assertExitCode(0);
    }

    /** @test */
    public function command_filters_by_completed_orders_only()
    {
        $this->artisan('repair:hubspot-discrepancy', ['--order-type' => 'completed', '--limit' => 10])
            ->expectsOutput('Order type filter: completed')
            ->assertExitCode(0);
    }

    /** @test */
    public function command_filters_by_abandoned_orders_only()
    {
        $this->artisan('repair:hubspot-discrepancy', ['--order-type' => 'abandoned', '--limit' => 10])
            ->expectsOutput('Order type filter: abandoned')
            ->assertExitCode(0);
    }

    /** @test */
    public function command_validates_order_type_option()
    {
        $this->artisan('repair:hubspot-discrepancy', ['--order-type' => 'invalid'])
            ->expectsOutput('Invalid order type. Must be: completed, abandoned, or both')
            ->assertExitCode(1);
    }

    /** @test */
    public function command_respects_date_range()
    {
        // Run command with date range that excludes the order
        $startDate = Carbon::now()->subDays(2)->format('Y-m-d');
        $endDate = Carbon::now()->subDays(1)->format('Y-m-d');

        $this->artisan('repair:hubspot-discrepancy', [
            'start' => $startDate,
            'end' => $endDate,
        ])
            ->expectsOutput('No Hubspot discrepancies found in the specified date range.')
            ->assertExitCode(0);
    }

    /** @test */
    public function command_validates_csv_and_email_options()
    {
        // Test that --email option requires --csv
        $this->artisan('repair:hubspot-discrepancy', ['--email' => true])
            ->expectsOutput('The --email option requires --csv to be enabled.')
            ->assertExitCode(1);
    }

    /** @test */
    public function command_validates_user_email_when_csv_option_used()
    {
        // Test that command validates user email exists when --csv is used
        $this->artisan('repair:hubspot-discrepancy', ['--csv' => true])
            ->expectsQuestion('Please enter your email address to receive the report links:', '<EMAIL>')
            ->expectsOutput("User with email '<EMAIL>' not found.")
            ->assertExitCode(1);
    }

    /** @test */
    public function command_accepts_valid_user_email_for_csv_option()
    {
        // Create a test user
        $user = \App\Models\User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Test User',
        ]);

        // Test that command accepts valid user email and proceeds with analysis
        $this->artisan('repair:hubspot-discrepancy', ['--csv' => true])
            ->expectsQuestion('Please enter your email address to receive the report links:', '<EMAIL>')
            ->expectsOutput('Reports will be generated for user: Test User (<EMAIL>)')
            ->assertExitCode(0);
    }
}
