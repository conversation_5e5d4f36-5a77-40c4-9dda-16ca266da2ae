<?php

namespace App\Tests\Commands;

use App\Enums\OrderStatus;
use App\Models\Order;
use App\Models\Shipment;
use App\Tests\TestCase;
use Illuminate\Support\Facades\Artisan;

class RepairShippedOrdersCommandTest extends TestCase
{
    public function testRepairShippedOrdersCommandTestSuccess()
    {
        $shipment = Shipment::factory()->create();
        $order1 = Order::factory()->create(['status' => OrderStatus::PENDING_SHIPMENT->value]);
        $order1->shipments()->save($shipment, ['created_at' => now()->subDays(8)]);

        $order2 = Order::factory()->create(['status' => OrderStatus::PENDING_SHIPMENT->value]);
        $order2->shipments()->save($shipment, ['created_at' => now()]);

        $order3 = Order::factory()->create(['status' => OrderStatus::PENDING_SHIPMENT->value]);

        $order4 = Order::factory()->create(['status' => OrderStatus::INCOMPLETE->value]);
        $order4->shipments()->save($shipment, ['created_at' => now()->subDays(8)]);

        Artisan::call('repair:shipped-orders');

        $order1->refresh();
        $this->assertEquals($order1->status, OrderStatus::COMPLETED->value);

        $order2->refresh();
        $this->assertEquals($order2->status, OrderStatus::PENDING_SHIPMENT->value);

        $order3->refresh();
        $this->assertEquals($order3->status, OrderStatus::PENDING_SHIPMENT->value);

        $order4->refresh();
        $this->assertEquals($order4->status, OrderStatus::INCOMPLETE->value);
    }
}
