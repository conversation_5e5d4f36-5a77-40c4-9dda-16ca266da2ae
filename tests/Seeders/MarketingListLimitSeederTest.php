<?php

namespace App\Tests\Seeders;

use App\Enums\MarketingPlatform;
use App\Models\MarketingList;
use App\Models\MarketingListLimit;
use App\Tests\TestCase;
use Database\Seeders\MarketingListLimitSeeder;

class MarketingListLimitSeederTest extends TestCase
{
    /** @test */
    public function it_creates_limits_for_get_response_marketing_lists_only()
    {
        // Create marketing lists for different platforms
        $getResponseList1 = MarketingList::factory()->create([
            'platform' => MarketingPlatform::GET_RESPONSE->value,
            'list_id' => 'gr-list-1',
        ]);

        $getResponseList2 = MarketingList::factory()->create([
            'platform' => MarketingPlatform::GET_RESPONSE->value,
            'list_id' => 'gr-list-2',
        ]);

        $aweberList = MarketingList::factory()->create([
            'platform' => MarketingPlatform::AWEBER->value,
            'list_id' => 'aweber-list-1',
        ]);

        $maropostList = MarketingList::factory()->create([
            'platform' => MarketingPlatform::MAROPOST->value,
            'list_id' => 'maropost-list-1',
        ]);

        // Ensure no limits exist initially
        $this->assertEquals(0, MarketingListLimit::count());

        // Run the seeder
        $seeder = new MarketingListLimitSeeder();
        $seeder->setCommand($this->createMock(\Illuminate\Console\Command::class));
        $seeder->run();

        // Check that limits were created only for GetResponse lists
        $this->assertEquals(8, MarketingListLimit::count()); // 4 groups × 2 GetResponse lists

        // Verify limits for GetResponse list 1
        $limits1 = MarketingListLimit::where('marketing_list_id', $getResponseList1->id)->get();
        $this->assertCount(4, $limits1);
        
        $groupNames = $limits1->pluck('group')->toArray();
        $this->assertContains('group1', $groupNames);
        $this->assertContains('group2', $groupNames);
        $this->assertContains('group3', $groupNames);
        $this->assertContains('group4', $groupNames);

        // Verify limits for GetResponse list 2
        $limits2 = MarketingListLimit::where('marketing_list_id', $getResponseList2->id)->get();
        $this->assertCount(4, $limits2);

        // Verify no limits were created for other platforms
        $this->assertEquals(0, MarketingListLimit::where('marketing_list_id', $aweberList->id)->count());
        $this->assertEquals(0, MarketingListLimit::where('marketing_list_id', $maropostList->id)->count());
    }

    /** @test */
    public function it_creates_correct_domain_groups_and_limits()
    {
        $getResponseList = MarketingList::factory()->create([
            'platform' => MarketingPlatform::GET_RESPONSE->value,
            'list_id' => 'test-list',
        ]);

        // Run the seeder
        $seeder = new MarketingListLimitSeeder();
        $seeder->setCommand($this->createMock(\Illuminate\Console\Command::class));
        $seeder->run();

        $limits = MarketingListLimit::where('marketing_list_id', $getResponseList->id)->get();

        // Check group1 (Gmail)
        $group1 = $limits->where('group', 'group1')->first();
        $this->assertNotNull($group1);
        $this->assertEquals(['gmail.com'], $group1->domains);
        $this->assertEquals(50, $group1->limit);

        // Check group2 (Yahoo and others)
        $group2 = $limits->where('group', 'group2')->first();
        $this->assertNotNull($group2);
        $this->assertEquals(['yahoo.com', 'aol.com', 'verizon.com', 'sbcglobal.net', 'comcast.com'], $group2->domains);
        $this->assertEquals(50, $group2->limit);

        // Check group3 (Microsoft)
        $group3 = $limits->where('group', 'group3')->first();
        $this->assertNotNull($group3);
        $this->assertEquals(['hotmail.com', 'outlook.com', 'live.com', 'msn.com'], $group3->domains);
        $this->assertEquals(25, $group3->limit);

        // Check group4 (Apple)
        $group4 = $limits->where('group', 'group4')->first();
        $this->assertNotNull($group4);
        $this->assertEquals(['icloud.com', 'mac.com', 'me.com'], $group4->domains);
        $this->assertEquals(25, $group4->limit);
    }

    /** @test */
    public function it_skips_marketing_lists_that_already_have_limits()
    {
        $getResponseList = MarketingList::factory()->create([
            'platform' => MarketingPlatform::GET_RESPONSE->value,
            'list_id' => 'existing-limits-list',
        ]);

        // Create an existing limit
        MarketingListLimit::factory()->create([
            'marketing_list_id' => $getResponseList->id,
            'group' => 'existing_group',
            'domains' => ['example.com'],
            'limit' => 10,
        ]);

        $initialCount = MarketingListLimit::count();

        // Run the seeder
        $seeder = new MarketingListLimitSeeder();
        $seeder->setCommand($this->createMock(\Illuminate\Console\Command::class));
        $seeder->run();

        // Should not create new limits since one already exists
        $this->assertEquals($initialCount, MarketingListLimit::count());
    }

    /** @test */
    public function it_handles_no_get_response_lists_gracefully()
    {
        // Create only non-GetResponse lists
        MarketingList::factory()->create([
            'platform' => MarketingPlatform::AWEBER->value,
            'list_id' => 'aweber-only',
        ]);

        MarketingList::factory()->create([
            'platform' => MarketingPlatform::MAROPOST->value,
            'list_id' => 'maropost-only',
        ]);

        // Run the seeder
        $seeder = new MarketingListLimitSeeder();
        $seeder->setCommand($this->createMock(\Illuminate\Console\Command::class));
        $seeder->run();

        // Should not create any limits
        $this->assertEquals(0, MarketingListLimit::count());
    }

    /** @test */
    public function it_creates_limits_for_multiple_get_response_lists_independently()
    {
        $list1 = MarketingList::factory()->create([
            'platform' => MarketingPlatform::GET_RESPONSE->value,
            'list_id' => 'list-1',
        ]);

        $list2 = MarketingList::factory()->create([
            'platform' => MarketingPlatform::GET_RESPONSE->value,
            'list_id' => 'list-2',
        ]);

        // Run the seeder
        $seeder = new MarketingListLimitSeeder();
        $seeder->setCommand($this->createMock(\Illuminate\Console\Command::class));
        $seeder->run();

        // Each list should have 4 limits (one for each group)
        $this->assertEquals(4, MarketingListLimit::where('marketing_list_id', $list1->id)->count());
        $this->assertEquals(4, MarketingListLimit::where('marketing_list_id', $list2->id)->count());

        // Total should be 8
        $this->assertEquals(8, MarketingListLimit::count());
    }
}
