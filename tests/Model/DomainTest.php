<?php

namespace App\Tests\Model;

use App\Enums\OperationStatus;
use App\Enums\PayloadSource;
use App\Enums\PayloadStatus;
use App\Exceptions\CloudflareException;
use App\Models\AffiliateRequest;
use App\Models\Domain;
use App\Models\DomainRegistration;
use App\Models\Payload;
use App\Models\Policies\Domain\HasRoute53Registration;
use App\Models\Policies\Domain\HasZone;
use App\Services\CloudflareService;
use App\Services\HubSpot\HubspotPayloadService;
use App\Tests\TestCase;
use Mockery;
use Mockery\MockInterface;

class DomainTest extends TestCase
{
    public function testHasRoute53RegistrationPolicy()
    {
        $domain = Domain::factory()->create();
        $this->assertFalse($domain->valid([HasRoute53Registration::class]));

        $request = AffiliateRequest::factory()->create(['domain_id' => $domain->id]);
        $this->assertFalse($domain->valid([HasRoute53Registration::class]));

        $domainRegistration = DomainRegistration::factory()->create(['affiliate_request_id' => $request->id]);
        $this->assertFalse($domain->valid([HasRoute53Registration::class]));

        $domainRegistration->status = OperationStatus::SUCCESSFUL->value;
        $domainRegistration->save();
        $this->assertTrue($domain->valid([HasRoute53Registration::class]));
    }

    public function testHasZonePolicy()
    {
        $mock = Mockery::mock(CloudflareService::class, function (MockInterface $mock) {
            $mock->shouldReceive('getDomain')->andReturn(['id' => 'foo']);
        });
        app()->instance(CloudflareService::class, $mock);
        $domain = Domain::factory()->create(['source_ref' => null]);
        $this->assertFalse($domain->valid([HasZone::class]));
        $domain = Domain::factory()->create(['source_ref' => 'foo']);
        $this->assertTrue($domain->valid([HasZone::class]));
    }

    public function testHasZonePolicySuccessByName()
    {
        $mock = Mockery::mock(CloudflareService::class, function (MockInterface $mock) {
            $mock->shouldReceive('getDomain')->andThrow(CloudflareException::class);
            $mock->shouldReceive('getDomainByName')->andReturn(['id' => 'bar']);
        });
        app()->instance(CloudflareService::class, $mock);
        $domain = Domain::factory()->create(['source_ref' => 'foo']);
        $this->assertTrue($domain->valid([HasZone::class]));
        $domain->refresh();
        $this->assertEquals('bar', $domain->source_ref);
    }

    public function testHasZonePolicyFail()
    {
        $mock = Mockery::mock(CloudflareService::class, function (MockInterface $mock) {
            $mock->shouldReceive('getDomain')->andThrow(CloudflareException::class);
            $mock->shouldReceive('getDomainByName')->andReturn(null);
        });
        app()->instance(CloudflareService::class, $mock);
        $domain = Domain::factory()->create(['source_ref' => 'foo']);
        $this->assertFalse($domain->valid([HasZone::class]));
    }
}
