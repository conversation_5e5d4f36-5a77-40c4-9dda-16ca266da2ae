<?php

namespace App\Tests\Model;

use App\Models\Brand;
use App\Models\Domain;
use App\Models\DomainProduct;
use App\Models\Pixel;
use App\Models\Policies\Pixel\CanBeAddedTag;
use App\Models\Policies\Pixel\CanBeDeletedTag;
use App\Models\Policies\Pixel\CanPauseTag;
use App\Models\Product;
use App\Models\User;
use App\Tests\TestCase;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Event;
use Laravel\Sanctum\Sanctum;

class PixelTest extends TestCase
{
    public function testAddTagToGTM()
    {
        Event::fake();
        Config::set('services.pixel.gtm_enabled', true);
        $user = User::factory()->create(['name' => 'Test Gtm']);
        Sanctum::actingAs($user);
        $product = Product::factory()->create(['code' => 'AA', 'gtm' => 'GTM-11111']);
        $domain = Domain::factory()->create();

        $domainProduct = new DomainProduct();
        $domainProduct->domain_id = $domain->id;
        $domainProduct->product_id = $product->id;
        $domainProduct->save();
        $domain->load(['products']);

        $pixel = Pixel::factory()->create([
            'domain_id' => $domain->id,
            'code' => '<script>console.log(\'test\')</script>',
            'enabled' => false,
            'approved_at' => Carbon::now(),
            'gtm_tag_ref' => '16',
            'gtm_trigger_ref' => '15'
        ]);

        $this->assertTrue($pixel->valid([CanBeDeletedTag::class]));
        Config::set('services.pixel.gtm_enabled', false);

        $this->assertFalse($pixel->valid([CanBeDeletedTag::class]));
    }

    public function testCanBeAddedTagPolicy()
    {
        Event::fake();
        Config::set('services.pixel.gtm_enabled', true);
        $pixel = Pixel::factory()->create(['gtm_tag_ref' => '1', 'gtm_trigger_ref' => '1']);
        $this->assertFalse($pixel->valid([CanBeAddedTag::class]));

        $pixel = Pixel::factory()->create(['enabled' => false, 'approved_at' => Carbon::now()]);
        $this->assertFalse($pixel->valid([CanBeAddedTag::class]));

        $pixel = Pixel::factory()->create(['enabled' => true, 'approved_at' => Carbon::now()]);
        $this->assertFalse($pixel->valid([CanBeAddedTag::class]));

        $pixel = Pixel::factory()->create(['enabled' => false, 'approved_at' => Carbon::now()]);
        $pixel->enabled = true;
        $this->assertTrue($pixel->valid([CanBeAddedTag::class]));

        Config::set('services.pixel.gtm_enabled', false);
        $this->assertFalse($pixel->valid([CanBeAddedTag::class]));
    }

    public function testCanPauseTagPolicy()
    {
        Event::fake();
        Config::set('services.pixel.gtm_enabled', true);
        $pixel = Pixel::factory()->create([
            'enabled' => false,
            'approved_at' => Carbon::now(),
            'gtm_tag_ref' => null,
            'gtm_trigger_ref' => null,
            'domain_id' => Domain::factory()->create(['domain' => 'foo.com'])
        ]);
        $pixel->enabled = true;
        $this->assertFalse($pixel->valid([CanPauseTag::class]));

        $pixel = Pixel::factory()->create([
            'enabled' => true,
            'approved_at' => Carbon::now(),
            'gtm_tag_ref' => '1',
            'gtm_trigger_ref' => '1',
            'domain_id' => Domain::factory()->create(['domain' => 'foo1.com'])
        ]);
        $this->assertFalse($pixel->valid([CanPauseTag::class]));

        $pixel = Pixel::factory()->create([
            'enabled' => true,
            'approved_at' => Carbon::now(),
            'gtm_tag_ref' => '1',
            'gtm_trigger_ref' => '1',
            'domain_id' => Domain::factory()->create(['domain' => 'foo2.com'])
        ]);
        $pixel->approved_at = null;
        $this->assertTrue($pixel->valid([CanPauseTag::class]));

        $pixel = Pixel::factory()->create([
            'enabled' => true,
            'approved_at' => Carbon::now(),
            'gtm_tag_ref' => '1',
            'gtm_trigger_ref' => '1',
            'domain_id' => Domain::factory()->create(['domain' => 'foo3.com'])
        ]);
        $pixel->enabled = false;
        $this->assertTrue($pixel->valid([CanPauseTag::class]));

        $pixel = Pixel::factory()->create([
            'enabled' => false,
            'approved_at' => Carbon::now(),
            'gtm_tag_ref' => '1',
            'gtm_trigger_ref' => '1',
            'domain_id' => Domain::factory()->create(['domain' => 'foo4.com'])
        ]);
        $pixel->enabled = true;
        $this->assertTrue($pixel->valid([CanPauseTag::class]));

        Config::set('services.pixel.gtm_enabled', false);
        $this->assertFalse($pixel->valid([CanPauseTag::class]));
    }
    public function testGetBrand()
    {
        Event::fake();
        $brand = Brand::factory()->create();
        $product = Product::factory()->create(['brand_id' => $brand->id]);
        $pixel = Pixel::factory()->create([
            'product_id' => $product->id,
            'enabled' => false,
            'approved_at' => Carbon::now(),
            'gtm_tag_ref' => null,
            'gtm_trigger_ref' => null,
            'domain_id' => Domain::factory()->create(['domain' => 'foo.com'])
        ]);

        $this->assertNotNull($pixel->brand);
        $this->assertEquals($brand->id, $pixel->brand->id);
    }
}
