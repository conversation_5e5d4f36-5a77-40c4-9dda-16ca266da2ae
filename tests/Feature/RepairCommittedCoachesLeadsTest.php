<?php

namespace App\Tests\Feature;

use App\Enums\AccountSource;
use App\Enums\OrderStatus;
use App\Enums\PayloadSource;
use App\Enums\PayloadStatus;
use App\Models\Account;
use App\Models\Customer;
use App\Models\Offer;
use App\Models\Order;
use App\Models\OrderOffer;
use App\Models\Payload;
use App\Tests\TestCase;
use Illuminate\Support\Facades\Http;

class RepairCommittedCoachesLeadsTest extends TestCase
{

    protected Offer $offer;
    protected array $skus;
    protected Account $account;

    public function setUp(): void
    {
        parent::setUp();

        config(['services.committed_coaches.webhook_enabled' => true]);
        config(['services.committed_coaches.webhook_url' => 'http://foo.com/']);
        config(['services.committed_coaches.included_products' => []]);
        config(['services.committed_coaches.excluded_products' => []]);

        Http::fake();

        $this->account = Account::factory()->create(['source' => AccountSource::CLICKBANK->value]);
        $this->offer = Offer::factory()->create();

        $this->skus = [];
        foreach ($this->offer->products as $product) {
            $this->skus = array_merge($this->skus, $product->skus->pluck('sku')->toArray());
        }
    }

    /** @test */
    public function it_fails_when_webhook_is_disabled(): void
    {
        config(['services.committed_coaches.webhook_enabled' => false]);

        $this->artisan('repair:committed-coaches-leads')
            ->expectsOutput('CommittedCoaches webhook is not enabled. Please check your configuration.')
            ->assertExitCode(1);
    }

    /** @test */
    public function it_handles_no_orders_to_process(): void
    {
        $this->artisan('repair:committed-coaches-leads')
            ->expectsOutput('No orders found that need CommittedCoaches leads.')
            ->assertSuccessful();
    }

    /** @test */
    public function it_processes_orders_without_committed_coaches_payloads(): void
    {
        // Create an order without a CommittedCoaches lead payload
        $order = $this->createValidOrder();

        $this->artisan('repair:committed-coaches-leads', ['--debug' => true])
            ->expectsOutput("Would send lead for order {$order->id} ({$order->source_ref})")
            ->expectsOutput('Sent: 1 leads')
            ->assertSuccessful();
    }

    /** @test */
    public function it_skips_orders_that_already_have_committed_coaches_payloads(): void
    {
        $order = $this->createValidOrder();

        // Create a CommittedCoaches lead payload for this order
        $payload = Payload::factory()->create([
            'source' => PayloadSource::COMMITTED_COACHES_LEAD->value,
            'source_ref' => $order->source_ref,
            'status' => PayloadStatus::SUCCESS->value,
        ]);
        $payload->orders()->attach($order);

        $this->artisan('repair:committed-coaches-leads', ['--debug' => true])
            ->expectsOutput('No orders found that need CommittedCoaches leads.')
            ->assertSuccessful();
    }

    /** @test */
    public function it_skips_orders_from_committed_coaches_account(): void
    {
        $ccAccount = Account::factory()->create(['source' => AccountSource::COMMITTED_COACHES->value]);
        $order = Order::factory()->create([
            'account_id' => $ccAccount->id,
            'status' => OrderStatus::COMPLETED->value,
            'purchased_at' => now()->subDays(2),
        ]);

        OrderOffer::factory()->create([
            'offer_id' => $this->offer->id,
            'order_id' => $order->id,
        ]);

        $this->artisan('repair:committed-coaches-leads', ['--debug' => true])
            ->expectsOutput('No orders found that need CommittedCoaches leads.')
            ->assertSuccessful();
    }

    /** @test */
    public function it_skips_non_completed_orders(): void
    {
        $order = Order::factory()->create([
            'account_id' => $this->account->id,
            'status' => OrderStatus::INCOMPLETE->value,
            'purchased_at' => now()->subDays(2),
        ]);

        OrderOffer::factory()->create([
            'offer_id' => $this->offer->id,
            'order_id' => $order->id,
        ]);

        $this->artisan('repair:committed-coaches-leads', ['--debug' => true])
            ->expectsOutput('No orders found that need CommittedCoaches leads.')
            ->assertSuccessful();
    }

    /** @test */
    public function it_respects_date_range_filter(): void
    {
        // Create order outside date range (older than 7 days)
        $oldOrder = $this->createValidOrder(['purchased_at' => now()->subDays(10)]);

        // Create order within date range
        $recentOrder = $this->createValidOrder(['purchased_at' => now()->subDays(3)]);

        $this->artisan('repair:committed-coaches-leads', ['--debug' => true])
            ->expectsOutput("Would send lead for order {$recentOrder->id} ({$recentOrder->source_ref})")
            ->expectsOutput('Processed: 1 orders')
            ->assertSuccessful();
    }

    /** @test */
    public function it_respects_custom_date_range(): void
    {
        // Create order 5 days ago (within the week limit)
        $order = $this->createValidOrder(['purchased_at' => now()->subDays(5)]);

        $startDate = now()->subDays(10)->format('Y-m-d');
        $endDate = now()->subDays(3)->format('Y-m-d');

        $this->artisan('repair:committed-coaches-leads', [
            'start' => $startDate,
            'end' => $endDate,
            '--debug' => true
        ])
            ->expectsOutput("Would send lead for order {$order->id} ({$order->source_ref})")
            ->expectsOutput('Processed: 1 orders')
            ->assertSuccessful();
    }

    /** @test */
    public function it_respects_order_id_filter(): void
    {
        $order1 = $this->createValidOrder();
        $order2 = $this->createValidOrder();

        $this->artisan('repair:committed-coaches-leads', [
            '--order' => [$order1->id],
            '--debug' => true
        ])
            ->expectsOutput("Would send lead for order {$order1->id} ({$order1->source_ref})")
            ->expectsOutput('Processed: 1 orders')
            ->assertSuccessful();
    }

    /** @test */
    public function it_respects_source_ref_filter(): void
    {
        $order = $this->createValidOrder();

        $this->artisan('repair:committed-coaches-leads', [
            '--source-ref' => $order->source_ref,
            '--debug' => true
        ])
            ->expectsOutput("Would send lead for order {$order->id} ({$order->source_ref})")
            ->expectsOutput('Processed: 1 orders')
            ->assertSuccessful();
    }

    /** @test */
    public function it_respects_limit_option(): void
    {
        $this->createValidOrder();
        $this->createValidOrder();
        $this->createValidOrder();

        $this->artisan('repair:committed-coaches-leads', [
            '--limit' => 2,
            '--debug' => true
        ])
            ->expectsOutput('Processing limited to 2 orders.')
            ->expectsOutput('Processed: 2 orders')
            ->assertSuccessful();
    }

    /** @test */
    public function it_skips_orders_with_excluded_products(): void
    {
        config(['services.committed_coaches.excluded_products' => $this->skus]);

        $order = $this->createValidOrder();

        $this->artisan('repair:committed-coaches-leads', ['--debug' => true])
            ->expectsOutput("Skipped order {$order->id} ({$order->source_ref}) - doesn't meet criteria")
            ->expectsOutput('Skipped: 1 orders')
            ->assertSuccessful();
    }

    /** @test */
    public function it_actually_sends_leads_when_not_in_debug_mode(): void
    {
        $order = $this->createValidOrder();

        $this->artisan('repair:committed-coaches-leads')
            ->expectsOutput('Sent: 1 leads')
            ->assertSuccessful();

        // Verify payload was created
        $this->assertDatabaseHas('payloads', [
            'source' => PayloadSource::COMMITTED_COACHES_LEAD->value,
            'source_ref' => $order->source_ref,
        ]);

        // Verify HTTP request was made
        Http::assertSent(function ($request) {
            return $request->url() === config('services.committed_coaches.webhook_url')
                && $request['event'] === 'lead';
        });
    }

    private function createValidOrder(array $attributes = []): Order
    {
        $order = Order::factory()->create(array_merge([
            'account_id' => $this->account->id,
            'status' => OrderStatus::COMPLETED->value,
            'customer_id' => Customer::factory()->create(['phone' => fake()->phoneNumber])->id,
            'purchased_at' => now()->subDays(2),
        ], $attributes));

        OrderOffer::factory()->create([
            'offer_id' => $this->offer->id,
            'order_id' => $order->id,
        ]);

        return $order->fresh(['account', 'offers.products.skus', 'customer']);
    }
}
