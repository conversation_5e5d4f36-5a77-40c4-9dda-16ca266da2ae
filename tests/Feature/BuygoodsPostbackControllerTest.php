<?php

namespace App\Tests\Feature;

use App\Enums\PayloadSource;
use App\Enums\PayloadStatus;
use App\Models\Payload;
use App\Tests\TestCase;
use Illuminate\Support\Facades\Config;

class BuygoodsPostbackControllerTest extends TestCase
{
    public function testWebhookAccountIdError()
    {
        Config::set('services.buygoods.postback_key', 'foo');
        Config::set('services.buygoods.postback_enabled', true);
        $data = $this->getFixtureByPath('BuygoodsIpnStrategyTest/neworder-first.json');
        $data['account_id'] = ['7532', 'foo'];
        $res = $this->call('POST', route('postbacks.buygoods', ['postback_key' => 'foo']), $data);
        $res->assertStatus(500);
        $actual = Payload::where(['source' => PayloadSource::BUYGOODS_IPN->value, 'source_ref' => 'FOOBAR'])->first();
        $this->assertNotNull($actual);
        $this->assertEquals(PayloadStatus::FAILED->value, $actual->status);
    }
}
