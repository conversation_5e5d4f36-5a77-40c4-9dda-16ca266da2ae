<?php

namespace App\Tests\Feature;

use App\Enums\AccountSource;
use App\Enums\OrderStatus;
use App\Enums\PayloadSource;
use App\Events\OrderCreated;
use App\Listeners\CommittedCoachesLead;
use App\Models\Account;
use App\Models\Customer;
use App\Models\Offer;
use App\Models\Order;
use App\Models\OrderOffer;
use App\Tests\TestCase;
use Event;
use Http;

class CommittedCoachesLeadTest extends TestCase
{
    protected Offer $offer;
    protected array $skus;

    public function setUp(): void
    {
        parent::setUp();

        config(['services.committed_coaches.webhook_enabled' => true]);
        config(['services.committed_coaches.webhook_url' => 'http://foo.com/']);

        Http::fake();
        Event::fake();

        $this->offer = Offer::factory()->create();

        $this->skus = [];
        foreach ($this->offer->products as $product) {
            $this->skus = array_merge($this->skus, $product->skus->pluck('sku')->toArray());
        }
    }

    /** @test */
    public function webhook_listening(): void
    {
        Event::assertListening(OrderCreated::class, CommittedCoachesLead::class);
    }

    /** @test */
    public function its_only_continues_when_enabled(): void
    {
        config(['services.committed_coaches.included_products' => $this->skus]);
        config(['services.committed_coaches.webhook_enabled' => false]);
        $account = Account::factory()->create(['source' => AccountSource::CLICKBANK->value]);
        $order = Order::factory()->create(['account_id' => $account->id]);

        $orderOffer = OrderOffer::factory()->create([
            'offer_id' => $this->offer->id,
            'order_id' => $order->id,
        ]);

        $this->assertDatabaseMissing('payloads', [
            'source'     => PayloadSource::COMMITTED_COACHES_LEAD->value,
            'source_ref' => $orderOffer->order->source_ref,
        ]);

        config(['services.committed_coaches.webhook_enabled' => true]);

        $orderOffer = OrderOffer::factory()->create([
            'offer_id' => $this->offer->id,
            'order_id' => $order->id,
        ]);

        $event = new OrderCreated($orderOffer->order);
        $listener = new CommittedCoachesLead();

        $listener->handle($event);

        $this->assertDatabaseHas('payloads', [
            'source'     => PayloadSource::COMMITTED_COACHES_LEAD->value,
            'source_ref' => $orderOffer->order->source_ref,
        ]);
    }

    /** @test */
    public function its_only_continues_if_product_matches(): void
    {
        $account = Account::factory()->create(['source' => AccountSource::CLICKBANK->value]);
        $order = Order::factory()->create(['account_id' => $account->id]);
        $orderOffer = OrderOffer::factory()->create(['order_id' => $order->id]);

        config(['services.committed_coaches.included_products' => ['invalid_sku']]);

        $this->assertDatabaseMissing('payloads', [
            'source'     => PayloadSource::COMMITTED_COACHES_LEAD->value,
            'source_ref' => $orderOffer->order->source_ref,
        ]);

        config(['services.committed_coaches.included_products' => $this->skus]);

        $orderOffer = OrderOffer::factory()->create([
            'offer_id' => $this->offer->id,
            'order_id' => $order->id,
        ]);

        $event = new OrderCreated($orderOffer->order);
        $listener = new CommittedCoachesLead();

        $listener->handle($event);

        $this->assertDatabaseHas('payloads', [
            'source'     => PayloadSource::COMMITTED_COACHES_LEAD->value,
            'source_ref' => $orderOffer->order->source_ref,
        ]);
    }

    /** @test */
    public function it_only_continues_when_order_completed(): void
    {
        $account = Account::factory()->create(['source' => AccountSource::CLICKBANK->value]);
        config(['services.committed_coaches.included_products' => $this->skus]);

        $orderOffer = OrderOffer::factory()->create([
            'offer_id' => $this->offer->id,
            'order_id' => Order::factory()->create([
                'status'     => OrderStatus::CANCELED->value,
                'account_id' => $account->id,
            ])->id,
        ]);

        $event = new OrderCreated($orderOffer->order);
        $listener = new CommittedCoachesLead();

        $listener->handle($event);

        $this->assertDatabaseMissing('payloads', [
            'source'     => PayloadSource::COMMITTED_COACHES_LEAD->value,
            'source_ref' => $orderOffer->order->source_ref,
        ]);

        $orderOffer = OrderOffer::factory()->create([
            'offer_id' => $this->offer->id,
            'order_id' => Order::factory()->create([
                'status'     => OrderStatus::COMPLETED->value,
                'account_id' => $account->id,
            ])->id,
        ]);

        $event = new OrderCreated($orderOffer->order);
        $listener = new CommittedCoachesLead();

        $listener->handle($event);

        $this->assertDatabaseHas('payloads', [
            'source'     => PayloadSource::COMMITTED_COACHES_LEAD->value,
            'source_ref' => $orderOffer->order->source_ref,
        ]);
    }

    /** @test */
    public function it_only_continues_if_order_is_not_older_than_a_week(): void
    {
        $account = Account::factory()->create(['source' => AccountSource::CLICKBANK->value]);
        config(['services.committed_coaches.included_products' => $this->skus]);

        $orderOffer = OrderOffer::factory()->create([
            'offer_id' => $this->offer->id,
            'order_id' => Order::factory()->create([
                'account_id'   => $account->id,
                'customer_id'  => Customer::factory()->create([
                    'phone' => fake()->phoneNumber,
                ])->id,
                'purchased_at' => now()->subWeek()->subDay(),
            ])->id,
        ]);

        $event = new OrderCreated($orderOffer->order);
        $listener = new CommittedCoachesLead();

        $listener->handle($event);

        $this->assertDatabaseMissing('payloads', [
            'source'     => PayloadSource::COMMITTED_COACHES_LEAD->value,
            'source_ref' => $orderOffer->order->source_ref,
        ]);

        $orderOffer = OrderOffer::factory()->create([
            'offer_id' => $this->offer->id,
            'order_id' => Order::factory()->create([
                'account_id'   => $account->id,
                'customer_id'  => Customer::factory()->create([
                    'phone' => fake()->phoneNumber,
                ])->id,
                'purchased_at' => now()->subDay(),
            ])->id,
        ]);

        $event = new OrderCreated($orderOffer->order);
        $listener = new CommittedCoachesLead();

        $listener->handle($event);

        $this->assertDatabaseHas('payloads', [
            'source'     => PayloadSource::COMMITTED_COACHES_LEAD->value,
            'source_ref' => $orderOffer->order->source_ref,
        ]);
    }

    /** @test */
    public function it_sends_leads_when_using_exclusion_only_mode(): void
    {
        $account = Account::factory()->create(['source' => AccountSource::CLICKBANK->value]);

        // Create another offer with different SKUs for testing exclusion
        $excludedOffer = Offer::factory()->create();
        $excludedSkus = [];
        foreach ($excludedOffer->products as $product) {
            $excludedSkus = array_merge($excludedSkus, $product->skus->pluck('sku')->toArray());
        }

        // Set up exclusion-only mode: empty included_products, some excluded_products
        config(['services.committed_coaches.included_products' => []]);
        config(['services.committed_coaches.excluded_products' => $excludedSkus]);

        // Test 1: Order with non-excluded SKUs should be sent
        $orderOffer = OrderOffer::factory()->create([
            'offer_id' => $this->offer->id,
            'order_id' => Order::factory()->create([
                'account_id'   => $account->id,
                'customer_id'  => Customer::factory()->create([
                    'phone' => fake()->phoneNumber,
                ])->id,
                'purchased_at' => now(),
            ])->id,
        ]);

        $event = new OrderCreated($orderOffer->order);
        $listener = new CommittedCoachesLead();
        $listener->handle($event);

        $this->assertDatabaseHas('payloads', [
            'source'     => PayloadSource::COMMITTED_COACHES_LEAD->value,
            'source_ref' => $orderOffer->order->source_ref,
        ]);

        // Test 2: Order with excluded SKUs should NOT be sent
        $excludedOrderOffer = OrderOffer::factory()->create([
            'offer_id' => $excludedOffer->id,
            'order_id' => Order::factory()->create([
                'account_id'   => $account->id,
                'customer_id'  => Customer::factory()->create([
                    'phone' => fake()->phoneNumber,
                ])->id,
                'purchased_at' => now(),
            ])->id,
        ]);

        $excludedEvent = new OrderCreated($excludedOrderOffer->order);
        $listener->handle($excludedEvent);

        $this->assertDatabaseMissing('payloads', [
            'source'     => PayloadSource::COMMITTED_COACHES_LEAD->value,
            'source_ref' => $excludedOrderOffer->order->source_ref,
        ]);
    }

    /** @test */
    public function it_handles_empty_string_in_included_products_as_exclusion_only_mode(): void
    {
        $account = Account::factory()->create(['source' => AccountSource::CLICKBANK->value]);

        // Set up with empty string in included_products (common when env var is empty)
        config(['services.committed_coaches.included_products' => ['']]);
        config(['services.committed_coaches.excluded_products' => []]);

        $orderOffer = OrderOffer::factory()->create([
            'offer_id' => $this->offer->id,
            'order_id' => Order::factory()->create([
                'account_id'   => $account->id,
                'customer_id'  => Customer::factory()->create([
                    'phone' => fake()->phoneNumber,
                ])->id,
                'purchased_at' => now(),
            ])->id,
        ]);

        $event = new OrderCreated($orderOffer->order);
        $listener = new CommittedCoachesLead();
        $listener->handle($event);

        $this->assertDatabaseHas('payloads', [
            'source'     => PayloadSource::COMMITTED_COACHES_LEAD->value,
            'source_ref' => $orderOffer->order->source_ref,
        ]);
    }

    /** @test */
    public function it_respects_exclusions_even_when_product_is_included(): void
    {
        $account = Account::factory()->create(['source' => AccountSource::CLICKBANK->value]);

        // Set up conflicting inclusion and exclusion for the same SKU
        // Exclusion should take precedence
        config(['services.committed_coaches.included_products' => $this->skus]);
        config(['services.committed_coaches.excluded_products' => $this->skus]);

        $orderOffer = OrderOffer::factory()->create([
            'offer_id' => $this->offer->id,
            'order_id' => Order::factory()->create([
                'account_id'   => $account->id,
                'customer_id'  => Customer::factory()->create([
                    'phone' => fake()->phoneNumber,
                ])->id,
                'purchased_at' => now(),
            ])->id,
        ]);

        $event = new OrderCreated($orderOffer->order);
        $listener = new CommittedCoachesLead();
        $listener->handle($event);

        // Should NOT be sent because exclusion takes precedence
        $this->assertDatabaseMissing('payloads', [
            'source'     => PayloadSource::COMMITTED_COACHES_LEAD->value,
            'source_ref' => $orderOffer->order->source_ref,
        ]);
    }

    /** @test */
    public function it_posts_json_to_the_webhook(): void
    {
        $account = Account::factory()->create(['source' => AccountSource::CLICKBANK->value]);
        config(['services.committed_coaches.included_products' => $this->skus]);
        config(['services.committed_coaches.accounts' => [$this->offer->account_id]]);

        $orderOffer = OrderOffer::factory()->create([
            'offer_id' => $this->offer->id,
            'order_id' => Order::factory()->create([
                'account_id'   => $account->id,
                'customer_id'  => Customer::factory()->create([
                    'phone' => fake()->phoneNumber,
                ])->id,
                'purchased_at' => now(),
            ])->id,
        ]);

        $event = new OrderCreated($orderOffer->order);
        $listener = new CommittedCoachesLead();

        $listener->handle($event);

        Http::assertSent(function ($request) use ($orderOffer) {
            return $request->url() === config('services.committed_coaches.webhook_url')
                && $request['event'] == 'lead'
                && $request['customer']['email'] == $orderOffer->order->customer->email
                && $request['customer']['phone'] == $orderOffer->order->customer->phone;
        });
    }
}
