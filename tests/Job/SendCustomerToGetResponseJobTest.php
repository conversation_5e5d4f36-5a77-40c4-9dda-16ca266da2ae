<?php

namespace App\Tests\Job;

use App\Enums\MarketingPlatform;
use App\Jobs\Marketing\SendCustomerToGetResponseJob;
use App\Jobs\ValidateCustomerPhoneJob;
use App\Models\Customer;
use App\Models\MarketingList;
use App\Services\Aweber\AweberClient;
use App\Services\GetResponse\GetResponseClient;
use App\Tests\TestCase;
use Illuminate\Support\Facades\Config;
use Mockery\MockInterface;

class SendCustomerToGetResponseJobTest extends TestCase
{
    public function testValidateCustomerPhoneWithCodeSuccess()
    {
        Config::set('services.get_response.enabled', true);
        $customer = Customer::factory()->create(['gender' => 'male']);
        $ml1 = MarketingList::factory()->create([
            'platform' => MarketingPlatform::GET_RESPONSE->value,
            'list_id' => 'foo'
        ]);
        $ml2 = MarketingList::factory()->create([
            'platform' => MarketingPlatform::GET_RESPONSE->value,
            'list_id' => 'foo'
        ]);
        $mock = $this->partialMock(
            GetResponseClient::class,
            function (MockInterface $mock) {
                $mock->shouldReceive('sendContact');
            }
        );
        app()->instance(GetResponseClient::class, $mock);

        $job = new SendCustomerToGetResponseJob($customer, $ml1);
        $job->handle(app()->make(GetResponseClient::class));
        $ml1->refresh();
        $ml2->refresh();
        $this->assertEquals($customer->id, $ml1->customerMarketingList()->first()->customer_id);
        $this->assertEquals($customer->id, $ml2->customerMarketingList()->first()->customer_id);
    }
}
