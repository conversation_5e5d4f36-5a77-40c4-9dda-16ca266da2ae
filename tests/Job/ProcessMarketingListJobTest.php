<?php

namespace App\Tests\Job;

use App\Dto\MarketingListDataDto;
use App\Enums\MarketingPlatform;
use App\Events\EmailValidatedSuccessfully;
use App\Jobs\Marketing\ProcessMarketingListJob;
use App\Jobs\Marketing\SendCustomerToAweberJob;
use App\Jobs\ValidateCustomerEmailJob;
use App\Models\Account;
use App\Models\Brand;
use App\Models\Customer;
use App\Models\MarketingList;
use App\Models\Offer;
use App\Models\Order;
use App\Models\OrderOffer;
use App\Models\Policies\User\CanBeSentToAweber;
use App\Models\Product;
use App\Services\Aweber\AweberClient;
use App\Services\Maropost\MaropostService;
use App\Tests\TestCase;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Queue;
use Mockery\MockInterface;

class ProcessMarketingListJobTest extends TestCase
{
    public function testValidateCustomerEmailJob()
    {
        Queue::fake();
        $c1 = Customer::factory()->create(['zerobounce_status' => 'valid']);
        $c2 = Customer::factory()->create(['zerobounce_status' => 'valid']);
        $brand = Brand::factory()->create();
        $account = Account::factory()->create(['brand_id' => $brand->id]);
        $order = Order::factory()->create([
            'account_id' => $account->id,
            'customer_id' => $c1->id,
        ]);

        Order::factory()->create(['customer_id' => $c2->id, 'account_id' => Account::factory()->create()->id]);

        $ml = MarketingList::factory()->create([
            'platform' => MarketingPlatform::AWEBER->value,
            'marketable_type' => 'brands',
            'marketable_id' => $brand->id,
        ]);

        $job = new ProcessMarketingListJob($ml, null, null, 5, 250);
        $job->handle();

        Queue::assertPushed(SendCustomerToAweberJob::class);
    }
}
