<?php

namespace App\Tests\Job;

use App\Jobs\HubSpotWebhookJob;
use App\Models\Address;
use App\Tests\TestCase;

class HubspotWebhookTest extends TestCase
{
    public function testHandleUpdateORCreateAddress()
    {
        $job = new HubSpotWebhookJob([]);
        $address = Address::factory()->create(['address' => 'Foo', 'address2' => 'Bar']);
        $job->updateOrCreateAddress(
            $address->customer,
            [
                'propertyName' => 'address',
                'propertyValue' => $address->full_address,
            ]
        );
        $address->refresh();
        $this->assertEquals('Foo', $address->address);
        $this->assertEquals('Bar', $address->address2);
        $this->assertEquals('Foo, Bar', $address->full_address);

        $job->updateOrCreateAddress(
            $address->customer,
            [
                'propertyName' => 'address',
                'propertyValue' => 'Fake address',
            ]
        );
        $address->refresh();
        $this->assertEquals('Fake address', $address->address);
        $this->assertEquals('Bar', $address->address2);
        $this->assertEquals('Fake address, Bar', $address->full_address);


        $job->updateOrCreateAddress(
            $address->customer,
            [
                'propertyName' => 'city',
                'propertyValue' => 'Fake city',
            ]
        );
        $address->refresh();
        $this->assertEquals('Fake city', $address->city);
    }
}
