<?php

namespace App\Tests\Job\SQS;

use App\Enums\MarketingPlatform;
use App\Enums\PayloadSource;
use App\Enums\PayloadStatus;
use App\Jobs\SQS\MaropostWebhookJob;
use App\Models\MarketingList;
use App\Models\Payload;
use App\Tests\TestCase;
use Illuminate\Support\Facades\Http;

class MaropostWebhookJobTest extends TestCase
{
    public function testHandleSuccess()
    {
        $data = $this->getFixtureByPath('/payloads/maropost/unsubscribe-event.json');

        MarketingList::factory()->create([
            'platform' => MarketingPlatform::MAROPOST->value,
            'list_id' => '1234',
            'optizmo_list_id' => 'foo-optizmo-list-id'
        ]);

        Http::fake([
            'https://collect.optoutsystem.com/client-optout/collect' => Http::response([])
        ]);

        $job = new MaropostWebhookJob(['payload' => $data]);
        $job->handle();

        $actual = Payload::where([
            'source' => PayloadSource::MAROPOST_WEBHOOK->value
        ])->first();
        $this->assertNotNull($actual);
        $this->assertEquals($actual->status, PayloadStatus::SUCCESS->value);
    }
}
