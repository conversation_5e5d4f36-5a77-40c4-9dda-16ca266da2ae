{"data": {"attributes": {"last_dns_records": [{"rname": "dns.cloudflare.com", "retry": 2400, "refresh": 10000, "minimum": 1800, "value": "beth.ns.cloudflare.com", "expire": 604800, "ttl": 1800, "serial": 2314269465, "type": "SOA"}, {"type": "NS", "value": "beth.ns.cloudflare.com", "ttl": 21600}, {"type": "A", "value": "104.21.29.208", "ttl": 300}, {"type": "AAAA", "value": "2606:4700:3032::6815:1dd0", "ttl": 300}, {"type": "NS", "value": "jack.ns.cloudflare.com", "ttl": 21600}, {"type": "A", "value": "172.67.149.205", "ttl": 300}, {"type": "AAAA", "value": "2606:4700:3033::ac43:95cd", "ttl": 300}], "jarm": "27d3ed3ed0003ed00042d43d00041df04c41293ba84f6efe3a613b22f983e6", "whois": "Admin City: REDACTED FOR PRIVACY\nAdmin Country: REDACTED FOR PRIVACY\nAdmin Organization: REDACTED FOR PRIVACY\nAdmin Postal Code: REDACTED FOR PRIVACY\nAdmin State/Province: REDACTED FOR PRIVACY\nCreation Date: 2022-03-25T21:16:56Z\nDNSSEC: unsigned\nDomain Name: getsightcare.org\nDomain Status: clientTransferProhibited https://icann.org/epp#clientTransferProhibited\nName Server: beth.ns.cloudflare.com\nName Server: jack.ns.cloudflare.com\nRegistrant City: 1f8f4166599d23ee\nRegistrant Country: CA\nRegistrant Email: f651612a2f356ad3s@\nRegistrant Fax Ext: 1f8f4166599d23ee\nRegistrant Fax: 1f8f4166599d23ee\nRegistrant Name: 1f8f4166599d23ee\nRegistrant Organization: 5fa648b96eeb677f\nRegistrant Phone Ext: 1f8f4166599d23ee\nRegistrant Phone: 1f8f4166599d23ee\nRegistrant Postal Code: 1f8f4166599d23ee\nRegistrant State/Province: 07ac7e47d3a73f45\nRegistrant Street: 1f8f4166599d23ee\nRegistrar Abuse Contact Email: <EMAIL>\nRegistrar Abuse Contact Phone: *************\nRegistrar IANA ID: 895\nRegistrar URL: https://domains.google.com\nRegistrar WHOIS Server: http://whois.google.com\nRegistrar: Google LLC\nRegistry Admin ID: REDACTED FOR PRIVACY\nRegistry Domain ID: 3a7d4f2b669947ecbe1e58580b17c678-LROR\nRegistry Expiry Date: 2024-03-25T21:16:56Z\nRegistry Registrant ID: REDACTED FOR PRIVACY\nRegistry Tech ID: REDACTED FOR PRIVACY\nTech City: REDACTED FOR PRIVACY\nTech Country: REDACTED FOR PRIVACY\nTech Organization: REDACTED FOR PRIVACY\nTech Postal Code: REDACTED FOR PRIVACY\nTech State/Province: REDACTED FOR PRIVACY\nUpdated Date: 2023-06-20T13:00:35Z", "last_https_certificate_date": 1691663275, "tags": [], "popularity_ranks": {"Statvoo": {"timestamp": 1684169889, "rank": 516550}, "Alexa": {"timestamp": 1684083487, "rank": 516550}, "Cisco Umbrella": {"timestamp": 1686070692, "rank": 882382}}, "last_analysis_date": 1691663268, "last_dns_records_date": 1691663275, "last_analysis_stats": {"harmless": 67, "malicious": 0, "suspicious": 0, "undetected": 21, "timeout": 0}, "creation_date": 1648243016, "whois_date": 1690899498, "reputation": -55, "registrar": "Google LLC", "last_analysis_results": {"Bkav": {"category": "undetected", "result": "unrated", "method": "blacklist", "engine_name": "Bkav"}, "CMC Threat Intelligence": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "CMC Threat Intelligence"}, "Snort IP sample list": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Snort IP sample list"}, "0xSI_f33d": {"category": "undetected", "result": "unrated", "method": "blacklist", "engine_name": "0xSI_f33d"}, "ViriBack": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "ViriBack"}, "PhishLabs": {"category": "undetected", "result": "unrated", "method": "blacklist", "engine_name": "PhishLabs"}, "K7AntiVirus": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "K7AntiVirus"}, "CINS Army": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "CINS Army"}, "Quttera": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "<PERSON><PERSON><PERSON>"}, "PrecisionSec": {"category": "undetected", "result": "unrated", "method": "blacklist", "engine_name": "PrecisionSec"}, "OpenPhish": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "OpenPhish"}, "VX Vault": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "VX Vault"}, "ArcSight Threat Intelligence": {"category": "undetected", "result": "unrated", "method": "blacklist", "engine_name": "ArcSight Threat Intelligence"}, "Scantitan": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Scantitan"}, "AlienVault": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "<PERSON><PERSON><PERSON>"}, "Sophos": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "<PERSON>ph<PERSON>"}, "Phishtank": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Phishtank"}, "Cyan": {"category": "undetected", "result": "unrated", "method": "blacklist", "engine_name": "<PERSON><PERSON>"}, "Spam404": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Spam404"}, "SecureBrain": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "SecureBrain"}, "CRDF": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "CRDF"}, "Fortinet": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Fortinet"}, "alphaMountain.ai": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "alphaMountain.ai"}, "Lionic": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "<PERSON><PERSON>"}, "Cyble": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Cyble"}, "Seclookup": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Seclookup"}, "Xcitium Verdict Cloud": {"category": "undetected", "result": "unrated", "method": "blacklist", "engine_name": "Xcitium Verdict Cloud"}, "Google Safebrowsing": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Google Safebrowsing"}, "SafeToOpen": {"category": "undetected", "result": "unrated", "method": "blacklist", "engine_name": "SafeToOpen"}, "ADMINUSLabs": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "ADMINUSLabs"}, "ESTsecurity": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "ESTsecurity"}, "Juniper Networks": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Juniper Networks"}, "Heimdal Security": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Heimdal Security"}, "AutoShun": {"category": "undetected", "result": "unrated", "method": "blacklist", "engine_name": "AutoShun"}, "Trustwave": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Trustwave"}, "AICC (MONITORAPP)": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "AICC (MONITORAPP)"}, "CyRadar": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "CyRadar"}, "Dr.Web": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Dr.<PERSON>"}, "Emsisoft": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Emsisoft"}, "Abusix": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Abusix"}, "Webroot": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Webroot"}, "Avira": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "<PERSON><PERSON><PERSON>"}, "securolytics": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "securolytics"}, "Antiy-AVL": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Antiy-AVL"}, "AlphaSOC": {"category": "undetected", "result": "unrated", "method": "blacklist", "engine_name": "AlphaSOC"}, "Acronis": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "<PERSON><PERSON><PERSON><PERSON>"}, "Quick Heal": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Quick Heal"}, "URLQuery": {"category": "undetected", "result": "unrated", "method": "blacklist", "engine_name": "URLQuery"}, "Viettel Threat Intelligence": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Viettel Threat Intelligence"}, "DNS8": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "DNS8"}, "benkow.cc": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "benkow.cc"}, "EmergingThreats": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "EmergingThreats"}, "Chong Lua Dao": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "<PERSON>ng Lua <PERSON>"}, "Yandex Safebrowsing": {"category": "malicious", "result": "clean", "method": "blacklist", "engine_name": "Yandex Safebrowsing"}, "Lumu": {"category": "undetected", "result": "unrated", "method": "blacklist", "engine_name": "<PERSON><PERSON>"}, "zvelo": {"category": "undetected", "result": "unrated", "method": "blacklist", "engine_name": "zvelo"}, "Kaspersky": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "<PERSON><PERSON><PERSON>"}, "Segasec": {"category": "undetected", "result": "unrated", "method": "blacklist", "engine_name": "Segasec"}, "Sucuri SiteCheck": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Su<PERSON>ri <PERSON>"}, "desenmascara.me": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "desenmascara.me"}, "CrowdSec": {"category": "undetected", "result": "unrated", "method": "blacklist", "engine_name": "CrowdSec"}, "Cluster25": {"category": "undetected", "result": "unrated", "method": "blacklist", "engine_name": "Cluster25"}, "SOCRadar": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "SOCRadar"}, "URLhaus": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "URLhaus"}, "PREBYTES": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "PREBYTES"}, "StopForumSpam": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "StopForumSpam"}, "Blueliv": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Blueliv"}, "Netcraft": {"category": "undetected", "result": "unrated", "method": "blacklist", "engine_name": "Netcraft"}, "ZeroCERT": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "ZeroCERT"}, "Phishing Database": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Phishing Database"}, "MalwarePatrol": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "MalwarePatrol"}, "IPsum": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "IPsum"}, "Malwared": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Malwared"}, "BitDefender": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "BitDefender"}, "GreenSnow": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "GreenSnow"}, "G-Data": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "G-Data"}, "VIPRE": {"category": "undetected", "result": "unrated", "method": "blacklist", "engine_name": "VIPRE"}, "SCUMWARE.org": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "SCUMWARE.org"}, "PhishFort": {"category": "undetected", "result": "unrated", "method": "blacklist", "engine_name": "PhishFort"}, "malwares.com URL checker": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "malwares.com URL checker"}, "Forcepoint ThreatSeeker": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Forcepoint ThreatSeeker"}, "Criminal IP": {"category": "undetected", "result": "unrated", "method": "blacklist", "engine_name": "Criminal IP"}, "Certego": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "<PERSON><PERSON><PERSON>"}, "ESET": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "ESET"}, "Threatsourcing": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Threatsourcing"}, "ThreatHive": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "ThreatHive"}, "Bfore.Ai PreCrime": {"category": "harmless", "result": "clean", "method": "blacklist", "engine_name": "Bfore.Ai PreCrime"}}, "last_update_date": 1687266035, "last_modification_date": 1691663277, "tld": "org", "last_https_certificate": {"size": 1299, "public_key": {"ec": {"oid": "secp256r1", "pub": "3059301306072a8648ce3d020106082a8648ce3d03010703420004d9a5ddfcbb7e4bc89cf7825a25bcb5c28d668a0dec80560f177d5a7b1c170641c650802240798102f75fda67aad961108c39db2b25ce7f64a320eff800e52dc5"}, "algorithm": "EC"}, "thumbprint_sha256": "aaa4d5858682e9a05f146a208959cccd3eefec208ec2190c358eaabfce625117", "cert_signature": {"signature": "3045022100fc88e301c12c0831f56e05482c23a7a4740cb59c920a92a72dfc16b60b82fa8902205585a1d3cb6251e36f5070093e4ffd86ebf62f398d882568a0ad3fe4e4bf7757", "signature_algorithm": "1.2.840.10045.4.3.2"}, "validity": {"not_after": "2024-04-16 23:59:59", "not_before": "2023-04-17 00:00:00"}, "version": "V3", "extensions": {"certificate_policies": ["2.23.140.1.2.2"], "extended_key_usage": ["serverAuth", "clientAuth"], "authority_key_identifier": {"keyid": "a5ce37eaebb0750e946788b445fad9241087961f"}, "subject_alternative_name": ["getsightcare.org"], "subject_key_identifier": "77b99e620abce743eb77f4254c67f94e2d31387d", "crl_distribution_points": ["http://crl3.digicert.com/CloudflareIncECCCA-3.crl", "http://crl4.digicert.com/CloudflareIncECCCA-3.crl"], "key_usage": ["digitalSignature"], "1.3.6.1.4.1.11129.2.4.2": "0482016a0168007600eecdd064d5db1acec55cb79db4cd13a23287467cbcecde", "CA": false, "ca_information_access": {"CA Issuers": "http://cacerts.digicert.com/CloudflareIncECCCA-3.crt", "OCSP": "http://ocsp.digicert.com"}}, "thumbprint": "fdf7a412a2339c501536c1592e2eeb4edc7fe8f0", "serial_number": "bf2354944d908b72d9d52c8a69b0cf1", "issuer": {"C": "US", "CN": "Cloudflare Inc ECC CA-3", "O": "Cloudflare, Inc."}, "subject": {"C": "US", "L": "San Francisco", "CN": "getsightcare.org", "O": "Cloudflare, Inc.", "ST": "California"}}, "categories": {"Forcepoint ThreatSeeker": "nutrition", "Xcitium Verdict Cloud": "media sharing", "BitDefender": "misc"}, "total_votes": {"harmless": 0, "malicious": 1}}, "type": "domain", "id": "getsightcare.org", "links": {"self": "https://www.virustotal.com/api/v3/domains/getsightcare.org"}}}