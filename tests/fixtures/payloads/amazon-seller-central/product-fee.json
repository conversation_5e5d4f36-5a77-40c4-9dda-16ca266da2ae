{"FeesEstimateResult": {"FeesEstimateIdentifier": {"MarketplaceId": "ATVPDKIKX0DER", "SellerId": "A2NEXAMPLETF53", "IdType": "ASIN", "IdValue": "B00X4WHP5E", "IsAmazonFulfilled": true, "PriceToEstimateFees": {"ListingPrice": {"CurrencyCode": "USD", "Amount": 25.0}, "Shipping": {"CurrencyCode": "USD", "Amount": 5.0}, "Points": {"PointsNumber": 0}}, "Identifier": "request-1"}, "FeesEstimate": {"TotalFeesEstimate": {"CurrencyCode": "USD", "Amount": 7.5}, "FeeDetailList": [{"FeeType": "ReferralFee", "FeeAmount": {"CurrencyCode": "USD", "Amount": 3.75}, "FeePromotion": {"CurrencyCode": "USD", "Amount": 0.0}, "TaxAmount": {"CurrencyCode": "USD", "Amount": 0.0}, "FinalFee": {"CurrencyCode": "USD", "Amount": 3.75}, "IncludedFee": {"CurrencyCode": "USD", "Amount": 3.75}}, {"FeeType": "FBAFees", "FeeAmount": {"CurrencyCode": "USD", "Amount": 3.75}, "FeePromotion": {"CurrencyCode": "USD", "Amount": 0.0}, "TaxAmount": {"CurrencyCode": "USD", "Amount": 0.0}, "FinalFee": {"CurrencyCode": "USD", "Amount": 3.75}, "IncludedFee": {"CurrencyCode": "USD", "Amount": 3.75}}]}, "Status": "Success"}}