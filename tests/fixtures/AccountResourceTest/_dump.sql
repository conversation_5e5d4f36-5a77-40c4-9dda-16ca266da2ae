INSERT INTO users (id, name, email, phone, company, password, remember_token, meta, last_login_at, email_verified_at, created_at, updated_at, deleted_at)
VALUES
  (1, 'First user', '<EMAIL>', '*********', 'First', '$2y$10$BnFIdw7VLK8ly6uGhWhnFePnrdukDIiQ6YOqUYfH06.dNm5Kp6cay', null, '[]', '2016-10-20 11:05:00', '2016-10-20 11:05:00', '2016-10-20 11:05:00', '2016-10-20 11:05:00', null);

INSERT INTO accounts (id, source, source_ref, name, default_commission_percent, default_commission_flat, enabled, api_key, meta, created_at, updated_at, deleted_at)
VALUES
  (1, 'X3', 'X3', 'First', 1, 1, 1, 'test', '{}', '2016-10-20 11:05:00', '2016-10-20 11:05:00', null);

INSERT INTO affiliates (id, source_ref, user_id, name, enabled, meta, last_active_at, created_at, updated_at, deleted_at)
VALUES
  (1, 'X3', 1, 'First', 1, '{}', '2016-10-20 11:05:00', '2016-10-20 11:05:00', '2016-10-20 11:05:00', null);

INSERT INTO customers (id, email, account_id, first_name, last_name, phone, meta, created_at, updated_at, deleted_at, hubspot_id)
VALUES
  (1, '<EMAIL>', 1, 'Test', 'Customer', 'eyJpdiI6Ikp6WDloclVxZ29DK003NE9mZy9RTFE9PSIsInZhbHVlIjoickVQM29HZjRLb0QwWi9iTURuaVRWUT09IiwibWFjIjoiZjhmNmQzZTM0MTc3NjFlMzYwNWUwYWQ4YTM4NjM1MDQ5NGRmMWVlNjYxZDViM2M2ZDgwODI0MmFmMTExYzI2MSIsInRhZyI6IiJ9', '{}', '2016-10-20 11:05:00', '2016-10-20 11:05:00', null, null),
  (2, '<EMAIL>', 1, 'Test 2', 'Customer 2', 'eyJpdiI6Ikp6WDloclVxZ29DK003NE9mZy9RTFE9PSIsInZhbHVlIjoickVQM29HZjRLb0QwWi9iTURuaVRWUT09IiwibWFjIjoiZjhmNmQzZTM0MTc3NjFlMzYwNWUwYWQ4YTM4NjM1MDQ5NGRmMWVlNjYxZDViM2M2ZDgwODI0MmFmMTExYzI2MSIsInRhZyI6IiJ9', '{}', '2016-10-20 12:05:00', '2016-10-20 12:05:00', null, null);

INSERT INTO addresses (id, customer_id, type, is_primary, first_name, last_name, company, address, address2, address3, city, state, country, postal_code, created_at, updated_at, deleted_at)
VALUES
  (1, 1, 'billing', 1, 'Test', 'Customer', 'Test Company', 'eyJpdiI6IkVYY20vZlZsM0lQMXJxSmtlSllxRkE9PSIsInZhbHVlIjoiYURRaGZEZ055ZHFONXREVk5JL2N0UT09IiwibWFjIjoiODYyYjE3MzI5YWRiZjMzNjU2ODYzZDIzZTM0Zjc4ODFkNWQ0MWI2YTVkZjM0OTA1ZGU2MzE1NDM4ZDYwMTVjNyIsInRhZyI6IiJ9', null, null, 'Omsk', 'OmskOblast', 'RS', 12345, '2016-10-20 11:05:00', '2016-10-20 11:05:00', null),
  (2, 2, 'shipping', 1, 'Test 2', 'Customer 2', 'Test Company 2', 'eyJpdiI6IkVYY20vZlZsM0lQMXJxSmtlSllxRkE9PSIsInZhbHVlIjoiYURRaGZEZ055ZHFONXREVk5JL2N0UT09IiwibWFjIjoiODYyYjE3MzI5YWRiZjMzNjU2ODYzZDIzZTM0Zjc4ODFkNWQ0MWI2YTVkZjM0OTA1ZGU2MzE1NDM4ZDYwMTVjNyIsInRhZyI6IiJ9', null, null, 'Omsk', 'OmskOblast', 'RS', 12345,  '2016-10-20 11:05:00', '2016-10-20 11:05:00', null);

INSERT INTO orders (id, source_ref, account_id, affiliate_id, customer_id, billing_address_id, shipping_address_id, parent_order_id, total_amount, base_currency_total_amount, refunded_amount, base_currency_refunded_amount, tax_amount, base_currency_tax_amount, shipping_amount, base_currency_shipping_amount, shipping_cost, affiliate_commission, base_currency_affiliate_commission, merchant_fee, base_currency_merchant_fee, cost_of_goods, discount_amount, base_currency_discount_amount, chargeback_fee, currency, tax_rate, tax_country, payment_method, status, ip_address, recurring, is_test, meta, purchased_at, rebills_at, refunded_at, canceled_at, created_at, updated_at, deleted_at)
VALUES
  (1, 'J12KLKT205', 1, 1, 1, 1, null, null, 10000, 10000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'USD', 0.0000, 'US', 'CreditCard', 'completed', null, 0, 0, '{"utm_medium": "234", "utm_source": "345", "utm_campaign": "123"}', '2016-10-20 11:05:00', null, null, null, '2016-10-20 11:05:00', '2016-10-20 11:05:00', null),
  (2, 'J12KLKT206', 1, 1, 2, null, 2, null, 20000, 20000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'USD', 0.0000, 'US', 'CreditCard', 'canceled', null, 0, 0, '{"utm_medium": "234", "utm_source": "345", "utm_campaign": "123"}', '2016-10-20 12:05:00', null, null, null, '2016-10-20 12:05:00', '2016-10-20 12:05:00', null),
  (3, 'J12KLKT207', 1, 1, 2, null, 2, null, 20000, 20000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'USD', 0.0000, 'US', 'CreditCard', 'canceled', null, 0, 0, '{"utm_medium": "234", "utm_source": "345", "utm_campaign": "123"}', '2016-10-20 12:05:00', null, null, null, '2016-10-20 12:05:00', '2016-10-20 12:05:00', null),
  (4, 'J12KLKT208', 1, 1, 2, null, null, null, 20000, 20000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'USD', 0.0000, 'US', 'CreditCard', 'canceled', null, 0, 0, '{"utm_medium": "234", "utm_source": "345", "utm_campaign": "123"}', '2016-10-20 12:05:00', null, null, null, '2016-10-20 12:05:00', '2016-10-20 12:05:00', null);

INSERT INTO products (id, parent_product_id, sku, code, name, description, type, default_price, cost_of_goods, currency, meta, created_at, updated_at, deleted_at)
VALUES
  (1, null, 'PTST', 'P-TEST', 'Test Product', null, 'digital', 0, 10000, 'USD', '{}', '2016-10-20 11:05:00', '2016-10-20 11:05:00', null),
  (2, null, 'PTST2', 'P-TEST-2', 'Test Product 2', null, 'digital', 0, 20000, 'USD', '{}', '2016-10-20 12:05:00', '2016-10-20 12:05:00', null);

INSERT INTO offers (id, source_ref, account_id, product_id, name, description, buy_url, commission_percent, commission_flat, quantity, type, recurring, enabled, price, currency, meta, created_at, updated_at, deleted_at)
VALUES
  (1, 'TST', 1, 1, 'Test Offer', null, null, 75.00, 0, 1, 'upsell', 0, 1, 10000, 'USD', null, '2016-10-20 11:05:00', '2016-10-20 11:05:00', null),
  (2, 'TST2', 1, 2, 'Test Offer 2', null, null, 75.00, 0, 1, 'initial', 0, 2, 20000, 'USD', null, '2016-10-20 12:05:00', '2016-10-20 12:05:00', null);

INSERT INTO order_offers (id, order_id, offer_id, quantity, amount, name, created_at, updated_at, hubspot_id)
VALUES
  (1, 1, 1, 1, 10000, 'Test Order Offer', '2016-10-20 11:05:00', '2016-10-20 11:05:00', null),
  (2, 2, 2, 1, 20000, 'Test Order Offer 2', '2016-10-20 12:05:00', '2016-10-20 12:05:00', null);
