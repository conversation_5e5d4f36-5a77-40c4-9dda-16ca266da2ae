<?php

namespace App\Tests\Unit\OfferReportService;

use App\Enums\AccountSource;
use App\Enums\OrderStatus;
use App\Models\Account;
use App\Models\BuygoodsOfferReport;
use App\Models\Offer;
use App\Models\OfferReport;
use App\Models\Order;
use App\Models\OrderOffer;
use App\Services\Report\Exceptions\OfferReportException;
use App\Services\Report\OfferReportService;
use App\Tests\TestCase;
use Illuminate\Support\Carbon;

class OfferReportServiceTest extends TestCase
{
    public function testOffersQuery()
    {
        $account = Account::factory()->create();
        $offer1 = Offer::factory()->create(['account_id' => $account->id]);
        $offer2 = Offer::factory()->create(['account_id' => $account->id]);
        $offer3 = Offer::factory()->create(['account_id' => $account->id, 'enabled' => false]);
        $offer4 = Offer::factory()->create(['account_id' => Account::factory()->create()->id]);
        $offer5 = Offer::factory()->create(['account_id' => $account->id]);

        $orderDate = Carbon::now()->subDays(2);

        $order1 = Order::factory()->create([
            'status' => OrderStatus::COMPLETED,
            'is_test' => false,
            'purchased_at' => $orderDate,
        ]);
        OrderOffer::factory()->create(['order_id' => $order1->id, 'offer_id' => $offer1->id]);

        $order2 = Order::factory()->create([
            'status' => OrderStatus::REFUNDED,
            'is_test' => false,
            'purchased_at' => $orderDate,
        ]);
        OrderOffer::factory()->create(['order_id' => $order2->id, 'offer_id' => $offer2->id]);

        $order3 = Order::factory()->create([
            'status' => OrderStatus::CANCELED,
            'is_test' => false,
            'purchased_at' => $orderDate,
        ]);
        OrderOffer::factory()->create(['order_id' => $order3->id, 'offer_id' => $offer5->id]);

        $order4 = Order::factory()->create([
            'status' => OrderStatus::COMPLETED,
            'is_test' => false,
            'purchased_at' => Carbon::now(),
        ]);
        OrderOffer::factory()->create(['order_id' => $order4->id, 'offer_id' => $offer5->id]);

        $order5 = Order::factory()->create([
            'status' => OrderStatus::REFUNDED,
            'is_test' => false,
            'purchased_at' => $orderDate,
        ]);
        OrderOffer::factory()->create(['order_id' => $order5->id, 'offer_id' => $offer4->id]);

        // Create a test order (should be ignored)
        $testOrder = Order::factory()->create([
            'status' => OrderStatus::COMPLETED,
            'is_test' => true,
            'purchased_at' => $orderDate,
        ]);
        OrderOffer::factory()->create(['order_id' => $testOrder->id, 'offer_id' => $offer2->id]);


        $service = new OfferReportService(AccountSource::BUYGOODS);
        $reportDate = Carbon::now()->subDays(2);

        $offersQuery = $service->offersQuery($account, $reportDate);
        $offers = $offersQuery->get();

        $this->assertCount(2, $offers);
        $this->assertTrue($offers->contains($offer1));
        $this->assertTrue($offers->contains($offer2));
        $this->assertFalse($offers->contains($offer3));
        $this->assertFalse($offers->contains($offer4));
        $this->assertFalse($offers->contains($offer5));
    }

    public function testFirstOrNewOfferReportCreatesNewReport()
    {
        // Create an offer
        $offer = Offer::factory()->create();
        $reportDate = Carbon::now()->subDays(2);

        $bgReport = BuygoodsOfferReport::factory()->create([
            'offer_id' => $offer->id,
            'starts_at' => $reportDate->copy()->startOfDay(),
            'ends_at' => $reportDate->copy()->endOfDay(),
        ]);

        $service = new OfferReportService(AccountSource::BUYGOODS);

        $offerReport = $service->firstOrNewOfferReport($offer, $reportDate);

        $this->assertNotNull($offerReport);
        $this->assertEquals($offer->id, $offerReport->offer_id);
        $this->assertEquals($bgReport->id, $offerReport->model_id);
        $this->assertEquals(
            $reportDate->copy()->startOfDay()->format('Y-m-d H:i:s'),
            $offerReport->starts_at->format('Y-m-d H:i:s')
        );
        $this->assertEquals(
            $reportDate->copy()->endOfDay()->format('Y-m-d H:i:s'),
            $offerReport->ends_at->format('Y-m-d H:i:s')
        );
    }

    public function testFirstOrNewOfferReportReturnsExistingReport()
    {
        $offer = Offer::factory()->create();
        $reportDate = Carbon::now()->subDays(2);
        $bgReport = BuygoodsOfferReport::factory()->create([
            'offer_id' => $offer->id,
            'starts_at' => $reportDate->copy()->startOfDay(),
            'ends_at' => $reportDate->copy()->endOfDay(),
        ]);
        $existingReport = OfferReport::factory()->create([
            'offer_id' => $offer->id,
            'starts_at' => $reportDate->copy()->startOfDay(),
            'ends_at' => $reportDate->copy()->endOfDay(),
        ]);
        $bgReport->offerReport()->save($existingReport);

        $service = new OfferReportService(AccountSource::BUYGOODS);

        $offerReport = $service->firstOrNewOfferReport($offer, $reportDate);

        $this->assertEquals($existingReport->id, $offerReport->id);
    }
}
