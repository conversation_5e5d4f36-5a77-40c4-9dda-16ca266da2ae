<?php

namespace App\Tests\Unit\Address;

use App\Dto\AddressDto;
use App\Dto\ShippingAddressDto;
use App\Enums\ServiceCredentialType;
use App\Models\Country;
use App\Models\ServiceCredential;
use App\Services\Address\AddressService;
use App\Services\Address\USPSClient;
use App\Tests\TestCase;
use Database\Seeders\CountriesSeeder;
use GuzzleHttp\Psr7\Response as GuzzleResponse;
use Illuminate\Http\Client\Response as HttpResponse;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Mockery;
use Mo<PERSON>y\MockInterface;
use Symfony\Component\HttpFoundation\Response;
use Torann\GeoIP\Facades\GeoIP;

class USPSClientTest extends TestCase
{
    public function test_fetch_access_token_success()
    {
        Config::set('services.usps.client', 'foo');
        Config::set('services.usps.secret', 'bar');

        $mockedResponse = new GuzzleResponse(Response::HTTP_OK, [], json_encode([
            'access_token' => 'foo',
            'expires_in' => 28799,
        ]));

        $data = [
            "grant_type" => "client_credentials",
            "client_id" => 'foo',
            "client_secret" => 'bar',
            "scope" => 'addresses',
        ];
        Http::shouldReceive('withHeaders')
            ->andReturnSelf();
        Http::shouldReceive('post')
            ->with('https://api.usps.com/oauth2/v3/token', $data)
            ->andReturn(new HttpResponse($mockedResponse));

        /** @var USPSClient $client */
        $client = app()->make(USPSClient::class);
        $client->fetchAccessToken();
        $this->assertNotNull($client->token);
        $actual = ServiceCredential::where(['service' => ServiceCredentialType::USPS->value])->first();
        $this->assertNotNull($actual);
        $this->assertEquals(['access_token' => 'foo', 'expires_in' => 28799], $actual->credentials);
        $this->assertEquals(now()->subMinutes(5)->addSeconds(28799), $actual->expires_at);
    }
    public function test_get_existing_access_token_success()
    {
        Config::set('services.usps.client', 'foo');
        Config::set('services.usps.secret', 'bar');

        ServiceCredential::create([
            'service' => ServiceCredentialType::USPS->value,
            'expires_at' => now()->subMinutes(5)->addSeconds(28799),
            'credentials' => [
                'access_token' => 'foo',
                'expires_in' => 28799,
            ]
        ]);

        /** @var USPSClient $client */
        $client = app()->make(USPSClient::class);
        $client->getAccessToken();
        $this->assertNotNull($client->token);
        $this->assertEquals(['access_token' => 'foo', 'expires_in' => 28799], $client->token);
    }
    public function test_existing_access_token_is_expired()
    {
        Config::set('services.usps.client', 'foo');
        Config::set('services.usps.secret', 'bar');

        $sc = ServiceCredential::create([
            'service' => ServiceCredentialType::USPS->value,
            'expires_at' => now()->subMinutes(60),
            'credentials' => [
                'access_token' => 'old',
                'expires_in' => 28799,
            ]
        ]);

        $mockedResponse = new GuzzleResponse(Response::HTTP_OK, [], json_encode([
            'access_token' => 'foo',
            'expires_in' => 28799,
        ]));

        $data = [
            "grant_type" => "client_credentials",
            "client_id" => 'foo',
            "client_secret" => 'bar',
            "scope" => 'addresses',
        ];
        Http::shouldReceive('withHeaders')
            ->andReturnSelf();
        Http::shouldReceive('post')
            ->with('https://api.usps.com/oauth2/v3/token', $data)
            ->andReturn(new HttpResponse($mockedResponse));

        /** @var USPSClient $client */
        $client = app()->make(USPSClient::class);
        $client->getAccessToken();
        $this->assertNotNull($client->token);
        $this->assertEquals(['access_token' => 'foo', 'expires_in' => 28799], $client->token);
        $this->assertDatabaseMissing('service_credentials', ['id' => $sc->id]);
    }
}