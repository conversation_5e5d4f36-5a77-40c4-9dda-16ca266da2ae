<?php

namespace App\Tests\Unit\ImportService;

use App\Dto\ImportOrderDto;
use App\Enums\AccountSource;
use App\Enums\OfferType;
use App\Enums\OrderStatus;
use App\Enums\PayloadSource;
use App\Enums\PayloadStatus;
use App\Exceptions\BuygoodsException;
use App\Exceptions\ImportOrderException;
use App\Models\Account;
use App\Models\Address;
use App\Models\ChargebackFee;
use App\Models\Country;
use App\Models\Customer;
use App\Models\Offer;
use App\Models\Order;
use App\Models\OrderOffer;
use App\Models\Payload;
use App\Models\Product;
use App\Models\Refund;
use App\Models\Tracking;
use App\Models\TrackingUtm;
use App\Services\Address\AddressService;
use App\Services\Address\AddressServiceContract;
use App\Services\ImportService\Order\ImportOrderService;
use App\Services\ImportService\Strategies\BuygoodsIpnStrategy;
use App\Services\PayloadService;
use App\Tests\TestCase;
use Cknow\Money\Money;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Money\Exception\ParserException;

class BuygoodsIpnStrategyTest extends TestCase
{
    use ExpectedDto;

    public function setUp(): void
    {
        parent::setUp();
        Config::set('services.konnektive.enabled', false);
    }

    public function testGetImportOrderDto()
    {
        $source = 'buygoods_ipn';
        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);

        $strategy = new BuygoodsIpnStrategy();
        $strategy->setAccount($account);
        $payload = $this->getJsonFixture('import.json');
        $actual = $strategy->getImportOrderDto($payload);

        $expected = new ImportOrderDto([
            'order'           => $this->expectedOrderDto($source),
            'customer'        => $this->expectedCustomerDto($account, $source),
            'affiliate'       => $this->expectedAffiliateDto($source),
            'offers'          => [$this->expectedOfferDto($source)],
            'shippingAddress' => $this->expectedShippingAddressDto($source),
            'address'         => $this->expectedAddressDto($source),
            'meta'            => $this->expectedMetaDto(PayloadSource::BUYGOODS_IPN->value, $source),
            'tracking'        => $this->expectedTrackingDto(PayloadSource::BUYGOODS_IPN->value, $source),
        ]);

        $this->assertEquals($expected->affiliate, $actual->affiliate, 'Affiliate dto');
        $this->assertEquals($expected->customer, $actual->customer, 'Customer dto');
        $this->assertEquals($expected->order, $actual->order, 'Order dto');
        $this->assertEquals($expected->meta, $actual->meta, 'Meta dto');
        $this->assertEquals($expected->tracking, $actual->tracking, 'Tracking dto');
        $this->assertEquals($expected->offers, $actual->offers, 'Offers dto');
        $this->assertEquals($expected, $actual);

        $payload = Payload::query()->where('hash', $strategy->getPayload()->hash)->first();
        $this->assertNotNull($payload);
        $this->assertEquals(
            $strategy->getPayload()->toArray(),
            Arr::except($payload->toArray(), ['response', 'error', 'replayed_at', 'retry_attempts', 'retry_at'])
        );
    }

    public function testGetImportOrderDtoWithIncorrectTrackingSubId()
    {
        $source = 'buygoods_ipn';
        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);

        $strategy = new BuygoodsIpnStrategy();
        $strategy->setAccount($account);
        $payload = $this->getJsonFixture('import-with-subids.json');
        $actual = $strategy->getImportOrderDto($payload);

        $expected = new ImportOrderDto([
            'order'           => $this->expectedOrderDto($source),
            'customer'        => $this->expectedCustomerDto($account, $source),
            'affiliate'       => $this->expectedAffiliateDto($source),
            'offers'          => [$this->expectedOfferDto($source)],
            'shippingAddress' => $this->expectedShippingAddressDto($source),
            'address'         => $this->expectedAddressDto($source),
            'meta'            => $this->expectedMetaDto(PayloadSource::BUYGOODS_IPN->value, $source),
            'tracking'        => $this->expectedTrackingDto(PayloadSource::BUYGOODS_IPN->value, 'buygoods_ipn_w_subids'),
        ]);

        $this->assertEquals($expected->affiliate, $actual->affiliate, 'Affiliate dto');
        $this->assertEquals($expected->customer, $actual->customer, 'Customer dto');
        $this->assertEquals($expected->order, $actual->order, 'Order dto');
        $this->assertEquals($expected->meta, $actual->meta, 'Meta dto');
        $this->assertEquals($expected->tracking, $actual->tracking, 'Tracking dto');
        $this->assertEquals($expected->offers, $actual->offers, 'Offers dto');
        $this->assertEquals($expected, $actual);

        $payload = Payload::query()->where('hash', $strategy->getPayload()->hash)->first();
        $this->assertNotNull($payload);
        $this->assertEquals(
            $strategy->getPayload()->toArray(),
            Arr::except($payload->toArray(), ['response', 'error', 'replayed_at', 'retry_attempts', 'retry_at'])
        );
    }

    public function testGetImportOrderDtoWithEqualSidAndSubid()
    {
        $source = 'buygoods_ipn';
        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);

        $strategy = new BuygoodsIpnStrategy();
        $strategy->setAccount($account);
        $payload = $this->getJsonFixture('import-sid-equals-subid.json');
        $actual = $strategy->getImportOrderDto($payload);

        $expected = new ImportOrderDto([
            'order'           => $this->expectedOrderDto($source),
            'customer'        => $this->expectedCustomerDto($account, $source),
            'affiliate'       => $this->expectedAffiliateDto($source),
            'offers'          => [$this->expectedOfferDto($source)],
            'shippingAddress' => $this->expectedShippingAddressDto($source),
            'address'         => $this->expectedAddressDto($source),
            'meta'            => $this->expectedMetaDto(PayloadSource::BUYGOODS_IPN->value, $source),
            'tracking'        => $this->expectedTrackingDto(PayloadSource::BUYGOODS_IPN->value, 'buygoods_ipn_eq_sid'),
        ]);

        $this->assertEquals($expected->affiliate, $actual->affiliate, 'Affiliate dto');
        $this->assertEquals($expected->customer, $actual->customer, 'Customer dto');
        $this->assertEquals($expected->order, $actual->order, 'Order dto');
        $this->assertEquals($expected->meta, $actual->meta, 'Meta dto');
        $this->assertEquals($expected->tracking, $actual->tracking, 'Tracking dto');
        $this->assertEquals($expected->offers, $actual->offers, 'Offers dto');
        $this->assertEquals($expected, $actual);

        $payload = Payload::query()->where('hash', $strategy->getPayload()->hash)->first();
        $this->assertNotNull($payload);
        $this->assertEquals(
            $strategy->getPayload()->toArray(),
            Arr::except($payload->toArray(), ['response', 'error', 'replayed_at', 'retry_attempts', 'retry_at'])
        );
    }

    public function testImportOrder()
    {
        $source = 'buygoods_ipn';
        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);
        Offer::factory()->create(['account_id' => $account->id, 'source_ref' => '2_FS6_097']);

        $importService = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $payload = $this->getJsonFixture('import.json');
        $importService->import($account, $payload);

        $dto = new ImportOrderDto([
            'order'           => $this->expectedOrderDto($source),
            'customer'        => $this->expectedCustomerDto($account, $source),
            'affiliate'       => $this->expectedAffiliateDto($source),
            'offers'          => [$this->expectedOfferDto($source)],
            'shippingAddress' => $this->expectedShippingAddressDto($source),
        ]);
        $actual = Order::where(['source_ref' => $dto->order->sourceRef])->first();
        $this->assertNotNull($actual);
        $this->assertEquals($dto->order->paymentMethod, $actual->payment_method);
        $this->assertEquals($dto->order->totalAmount, $actual->total_amount);
        $this->assertEquals($dto->order->status, $actual->status);
        $this->assertCount(1, $actual->orderOffers);

        $customer = $actual->customer;
        $this->assertNotNull($customer);
        $this->assertEquals($dto->customer->email, $customer->email);
        $this->assertEquals('+********', $customer->phone);
        $this->assertCount(2, $customer->addresses);

        $affiliate = $actual->affiliate;
        $this->assertNotNull($affiliate);
        $this->assertEquals($dto->affiliate->sourceRef, $affiliate->accounts->first()->pivot->affiliate_source_ref);
        $this->assertEquals($dto->affiliate->name, $affiliate->name);
    }

    public function testImportOrderInvalidProductCodeName()
    {
        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);

        $importService = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $payload = $this->getJsonFixture('import.json');
        $payload['product_codename'] = $payload['product_name'];
        $payload['action_type'] = 'abandon';
        $importService->import($account, $payload);
        $actual = Payload::where(['source' => PayloadSource::BUYGOODS_IPN->value, 'source_ref' => 'foo-bar'])->first();
        $this->assertNotNull($actual);
        $this->assertEquals(PayloadStatus::FAILED->value, $actual->status);
        $this->assertEquals('Product code is incorrect: ' . $payload['product_name'], $actual->error['message']);
    }

    public function testImportOrderAbondonEventFirstOrderPerOffer()
    {
        $source = 'buygoods_ipn_abandon';
        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);

        Offer::factory()->create([
            'type'       => OfferType::INITIAL->value,
            'account_id' => $account->id,
            'source_ref' => '2_FS6_097',
            'price'      => Money::USD(20.00)
        ]);

        $importService = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $payload = $this->getJsonFixture('abandon.json');
        $importService->import($account, $payload);

        $dto = new ImportOrderDto([
            'order'     => $this->expectedOrderDto($source),
            'customer'  => $this->expectedCustomerDto($account, $source),
            'affiliate' => $this->expectedAffiliateDto($source),
            'offers'    => [$this->expectedOfferDto($source)],
            'address'   => $this->expectedAddressDto($source),
        ]);

        $customer = Customer::where(['email' => $dto->customer->email])->first();
        $this->assertNotNull($customer);
        $this->assertEquals($dto->customer->email, $customer->email);
        $this->assertEquals('+1' . $dto->customer->phone, $customer->phone);
        $this->assertCount(2, $customer->addresses);

        $address = $customer->addresses->first();
        $this->assertEquals($dto->address->firstName, $address->first_name);
        $this->assertEquals($dto->address->address, $address->address);
        $this->assertEquals($dto->address->state, $address->state);

        $actual = Order::where(['customer_id' => $customer->id])->first();
        $this->assertNotNull($actual);
        $this->assertEquals($dto->order->totalAmount, $actual->total_amount);
        $this->assertEquals($dto->order->status, $actual->status);
        $this->assertCount(1, $actual->orderOffers);

        $affiliate = $actual->affiliate;
        $this->assertNull($affiliate);
    }

    public function testImportOrderAbondonEventEmptyProductCode()
    {
        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);

        $importService = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $payload = $this->getJsonFixture('abandon.json');
        $payload['product_codename'] = null;
        $importService->import($account, $payload);

        $payloadSourceRef = $importService->getPayload()->source_ref;
        $actual = Payload::where(['source_ref' => $payloadSourceRef])->first();
        $this->assertNotNull($actual);
        $this->assertEquals(PayloadStatus::FAILED->value, $actual->status);
    }

    public function testImportOrderAbondonEventSecondOrder()
    {
        $source = 'buygoods_ipn_abandon';
        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);

        $order = Order::factory()->create();
        $offer = Offer::factory()->create([
            'type'       => OfferType::INITIAL->value,
            'account_id' => $account->id,
            'source_ref' => '2_FS6_097',
            'price'      => Money::USD(97.00),
        ]);

        OrderOffer::factory()->create(['order_id' => $order->id, 'offer_id' => $offer->id]);

        $importService = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $payload = $this->getJsonFixture('abandon.json');
        $importService->import($account, $payload);

        $dto = new ImportOrderDto([
            'order'     => $this->expectedOrderDto($source),
            'customer'  => $this->expectedCustomerDto($account, $source),
            'affiliate' => $this->expectedAffiliateDto($source),
            'offers'    => [$this->expectedOfferDto($source)],
            'address'   => $this->expectedAddressDto($source),
        ]);

        $customer = Customer::where(['email' => $dto->customer->email])->first();
        $this->assertNotNull($customer);
        $this->assertEquals($dto->customer->email, $customer->email);
        $this->assertEquals('+1' . $dto->customer->phone, $customer->phone);
        $this->assertCount(2, $customer->addresses);

        $address = $customer->addresses->first();
        $this->assertEquals($dto->address->firstName, $address->first_name);
        $this->assertEquals($dto->address->address, $address->address);
        $this->assertEquals($dto->address->state, $address->state);

        $actual = Order::where(['customer_id' => $customer->id])->first();
        $this->assertNotNull($actual);
        $this->assertEquals($dto->order->totalAmount, $actual->total_amount);
        $this->assertEquals($dto->order->status, $actual->status);
        $this->assertCount(1, $actual->orderOffers);

        $affiliate = $actual->affiliate;
        $this->assertNull($affiliate);
    }

    public function testImportAbandonEventAndRunPayload()
    {
        $source = 'buygoods_ipn_abandon';
        $account = Account::factory()->create([
            'source'     => AccountSource::BUYGOODS->value,
            'source_ref' => '8117'
        ]);

        $order = Order::factory()->create();
        $offer = Offer::factory()->create([
            'type'       => OfferType::INITIAL->value,
            'account_id' => $account->id,
            'source_ref' => '2_FS6_097',
            'price'      => Money::USD(20.00),
        ]);

        OrderOffer::factory()->create(['order_id' => $order->id, 'offer_id' => $offer->id]);

        $importService = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $data = $this->getJsonFixture('abandon.json');
        $importService->import($account, $data);

        $dto = new ImportOrderDto([
            'order'     => $this->expectedOrderDto($source),
            'customer'  => $this->expectedCustomerDto($account, $source),
            'affiliate' => $this->expectedAffiliateDto($source),
            'offers'    => [$this->expectedOfferDto($source)],
            'address'   => $this->expectedAddressDto($source),
        ]);

        $customer = Customer::where(['email' => $dto->customer->email])->first();
        $actual = Order::where(['customer_id' => $customer->id])->first();

        $payload = $actual->payloads->first();

        /** @var PayloadService $payloadService */
        $payloadService = app()->make(PayloadService::class);
        $payloadService->run($payload);

        $actual = Payload::where(['hash' => $payload->hash])->count();
        $this->assertEquals(1, $actual);
    }

    public function testImportOrderRefundEvent()
    {
        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);
        Offer::factory()->create(['account_id' => $account->id, 'source_ref' => '2_FS6_097']);

        Order::factory()->create([
            'source_ref'   => 'FOO-REFUND',
            'account_id'   => $account->id,
            'total_amount' => Money::parse('97.00')
        ]);

        $importService = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $payload = $this->getJsonFixture('refund.json');
        $importService->import($account, $payload);

        $actual = Order::where(['source_ref' => 'FOO-REFUND'])->first();
        $this->assertNotNull($actual);
        $this->assertEquals(Money::parse('97.00'), $actual->refunded_amount);

        $this->assertEquals(OrderStatus::REFUNDED->value, $actual->status);
        $payload = $actual->payloads()->where(['source' => PayloadSource::BUYGOODS_IPN->value])->first();
        $this->assertNotNull($payload);
        $this->assertNotNull($payload->refunds()->first());
    }

    public function testImportOrderEmptyCodeName()
    {
        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);
        Offer::factory()->create(['account_id' => $account->id, 'source_ref' => '2_FS6_097']);

        /** @var BuygoodsIpnStrategy $strategy */
        $strategy = app()->make(BuygoodsIpnStrategy::class);
        $strategy->setAccount($account);
        $payload = $this->getJsonFixture('import.json');
        $payload['product_codename'] = null;
        $payload['buy_url'] = null;
        $this->expectException(BuygoodsException::class);
        $strategy->getImportOrderDto($payload);
    }

    public function testImportOrderWithTwoProduct()
    {
        Config::set('services.hubspot.sync_enabled', false);
        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);
        $offerFirst = Offer::factory()->create([
            'account_id' => $account->id,
            'source_ref' => '1_BS1_069',
            'price'      => Money::USD('69.0'),
        ]);

        $offerSecond = Offer::factory()->create([
            'account_id' => $account->id,
            'source_ref' => '1_BS3_177',
            'price'      => Money::USD('177.0'),
        ]);

        $importService = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $payload1 = $this->getJsonFixture('neworder-first.json');
        $importService->import($account, $payload1);

        $actual = Order::where(['source_ref' => $payload1['order_id_global']])->first();
        $this->assertNotNull($actual);
        $this->assertCount(1, $actual->orderOffers);
        $this->assertEquals($offerFirst->id, $actual->orderOffers->first()->offer_id);

        $importService->import($account, $payload1);
        $actual->refresh();
        $this->assertCount(1, $actual->orderOffers);

        $payload2 = $this->getJsonFixture('neworder-second.json');
        $importService->import($account, $payload2);
        $actual->refresh();
        $this->assertCount(2, $actual->orderOffers);
        $this->assertEquals($offerSecond->id, $actual->orderOffers->last()->offer_id);

        $importService->import($account, $payload2);
        $actual->refresh();
        $this->assertCount(2, $actual->orderOffers);

        $payloads = Payload::where([
            'source_ref' => $payload1['order_id_global'],
            'source'     => PayloadSource::BUYGOODS_IPN->value,
        ])->get();

        $this->assertCount(2, $payloads);
        $this->assertCount(1, $payloads->last()->orders);
        $this->assertCount(1, $payloads->last()->customers);
        $this->assertCount(1, $payloads->first()->orders);
        $this->assertCount(1, $payloads->first()->customers);
    }

    public function testImportOrderSecondTimeUpdateTotalAmountValue()
    {
        $source = 'buygoods_ipn';
        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);
        Offer::factory()->create(['account_id' => $account->id, 'source_ref' => '2_FS6_097']);

        $importService = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $payload = $this->getJsonFixture('import.json');
        $importService->import($account, $payload);

        $dto = new ImportOrderDto([
            'order'           => $this->expectedOrderDto($source),
            'customer'        => $this->expectedCustomerDto($account, $source),
            'affiliate'       => $this->expectedAffiliateDto($source),
            'offers'          => [$this->expectedOfferDto($source)],
            'shippingAddress' => $this->expectedShippingAddressDto($source),
        ]);
        $actual = Order::where(['source_ref' => $dto->order->sourceRef])->first();
        $this->assertNotNull($actual);
        $this->assertEquals($dto->order->totalAmount, $actual->total_amount);

        $payload["total"] = "$150";
        $payload["merchant_commission"] = 0;
        $importService->import($account, $payload);
        $actual->refresh();
        $this->assertEquals(Money::parse('150.00'), $actual->total_amount);
        $this->assertEquals(Money::parse('9.25'), $actual->merchant_fee);
    }

    public function testDoubleRefund()
    {
        $account = Account::factory()->create([
            'source'     => AccountSource::BUYGOODS->value,
            'source_ref' => 'acc-foo',
        ]);

        $product = Product::factory()->create();
        $offer = Offer::factory()->create([
            'account_id' => $account->id,
            'source_ref' => '2_FS3_117',
        ]);
        $offer->productAttach($product->id, 1);
        $newOrderData = $this->getJsonFixture('neworder-rfnd.json');
        $importService = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $importService->import($account, $newOrderData);
        $actual = Order::where(['source_ref' => 'FOO_RFND'])->first();
        $this->assertNotNull($actual);
        $this->assertEquals(Money::USD('146.25'), $actual->total_amount);

        $rfndFirst = $this->getJsonFixture('order_rfnd_1.json');
        $importService->import($account, $rfndFirst);
        $actual = Order::where(['source_ref' => 'FOO_RFND'])->first();
        $this->assertEquals(OrderStatus::COMPLETED->value, $actual->status);
        $this->assertEquals(Money::USD('117.00'), $actual->refunded_amount);
        $this->assertNotNull($actual->refunded_at);

        $refunds = $actual->refunds;
        $this->assertCount(1, $refunds);
        $refund = $refunds->first();
        $this->assertEquals(Money::USD('117.00'), $refund->amount);

        $rfndSecond = $this->getJsonFixture('order_rfnd_2.json');
        $importService->import($account, $rfndSecond);
        $actual = Order::where(['source_ref' => 'FOO_RFND'])->first();
        $this->assertEquals(OrderStatus::REFUNDED->value, $actual->status);
        $this->assertEquals(Money::USD('146.25'), $actual->refunded_amount);
        $this->assertNotNull($actual->refunded_at);

        $refunds = $actual->refunds;
        $this->assertCount(2, $refunds);
        $refund = $refunds->first();
        $this->assertEquals(Money::USD('117.00'), $refund->amount);
        $refund = $refunds->last();
        $this->assertEquals(Money::USD('29.25'), $refund->amount);
    }

    public function testReplayPayloadFixRefunds()
    {
        $account = Account::factory()->create([
            'source'     => AccountSource::BUYGOODS->value,
            'source_ref' => 'acc-foo',
        ]);

        $product = Product::factory()->create();
        $offer = Offer::factory()->create([
            'account_id' => $account->id,
            'source_ref' => '2_FS3_117',
        ]);
        $offer->productAttach($product->id, 1);
        $newOrderData = $this->getJsonFixture('neworder-rfnd.json');
        $importService = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $importService->import($account, $newOrderData);
        $actual = Order::where(['source_ref' => 'FOO_RFND'])->first();
        $this->assertNotNull($actual);
        $this->assertEquals(Money::USD('146.25'), $actual->total_amount);

        $rfndFirst = $this->getJsonFixture('order_rfnd_1.json');
        $importService->import($account, $rfndFirst);
        $actual = Order::where(['source_ref' => 'FOO_RFND'])->first();
        $this->assertEquals(OrderStatus::COMPLETED->value, $actual->status);
        $this->assertEquals(Money::USD('117.00'), $actual->refunded_amount);
        $this->assertNotNull($actual->refunded_at);
        $refunds = $actual->refunds;
        $this->assertCount(1, $refunds);
        $refund = $refunds->first();
        $this->assertEquals(Money::USD('117.00'), $refund->amount);
        $this->assertEquals('2024-03-06 17:07:42', $refund->source_refunded_at->format('Y-m-d H:i:s'));
        $this->assertEquals('2024-03-06 12:07:42', $refund->refunded_at->format('Y-m-d H:i:s'));

        /** @var PayloadService $payloadService */
        $payloadService = app()->make(PayloadService::class);
        $rfndSecond = $this->getJsonFixture('order_rfnd_2.json');

        $payload = $payloadService->getPayload(PayloadSource::BUYGOODS_IPN->value, $rfndSecond, 'FOO_RFND');
        $payloadService->attachModelToPayload($payload, $refund);

        $payloadService->run($payload);

        $actual = Order::where(['source_ref' => 'FOO_RFND'])->first();
        $this->assertEquals(OrderStatus::REFUNDED->value, $actual->status);
        $this->assertEquals(Money::USD('146.25'), $actual->refunded_amount);
        $this->assertNotNull($actual->refunded_at);

        $refunds = $actual->refunds;
        $this->assertCount(2, $refunds);
        $refund = $refunds->first();
        $this->assertEquals(Money::USD('117.00'), $refund->amount);
        $refund = $refunds->last();
        $this->assertEquals(Money::USD('29.25'), $refund->amount);
    }

    public function testChargebackOrder()
    {
        /** @var PayloadService $payloadService */
        $payloadService = app()->make(PayloadService::class);
        $account = Account::factory()->create([
            'source'     => AccountSource::BUYGOODS->value,
            'source_ref' => 'acc-foo',
        ]);
        Offer::factory()->create(['account_id' => $account->id, 'source_ref' => '2_FS9_171']);

        $importService = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $payload = $this->getJsonFixture('test_1.json');
        $importService->import($account, $payload);

        $actual = Order::where(['source_ref' => 'FOOBAR'])->first();
        $this->assertNotNull($actual);
        $this->assertEquals(OrderStatus::COMPLETED->value, $actual->status);
        $this->assertEquals(Money::USD(0), $actual->refunded_amount);
        $this->assertEquals(Money::USD(0), $actual->chargeback_fee);

        $payload = $this->getJsonFixture('test_2.json');
        $importService->import($account, $payload);
        $actual->refresh();
        $this->assertEquals(Money::USD('35.00'), $actual->chargeback_fee);
        $this->assertEquals(Money::USD('171.00'), $actual->total_amount);
        $this->assertEquals(Money::USD('0'), $actual->refunded_amount);

        $chargeback = ChargebackFee::where(['order_id' => $actual->id])->first();
        $this->assertNotNull($chargeback);
        $this->assertEquals(Money::USD('35.00'), $actual->chargeback_fee);

        $refund = Refund::where(['order_id' => $actual->id])->first();
        $this->assertNull($refund);
    }

    public function testReplayPayloadSaveCustomerAddress()
    {
        /** @var PayloadService $payloadService */
        $payloadService = app()->make(PayloadService::class);
        $account = Account::factory()->create([
            'source'     => AccountSource::BUYGOODS->value,
            'source_ref' => 'acc-foo',
        ]);

        $customer = Customer::factory()->create(['email' => '<EMAIL>']);
        $customer->accounts()->save($account);
        Address::factory()->create([
            'customer_id' => $customer->id,
            'type'        => 'billing',
            'state'       => 'NY',
            'city'        => 'Faketown',
            'address'     => 'Fake Address'
        ]);
        Address::factory()->create([
            'customer_id' => $customer->id,
            'type'        => 'billing',
            'state'       => 'NY',
            'city'        => 'Faketown',
            'address'     => '123 Maple Avenue'
        ]);

        Address::factory()->create([
            'customer_id' => $customer->id,
            'type'        => 'shipping',
            'state'       => 'NY',
            'city'        => 'Faketown',
            'address'     => 'Fake Address'
        ]);
        Address::factory()->create([
            'customer_id' => $customer->id,
            'type'        => 'shipping',
            'state'       => 'NY',
            'city'        => 'Faketown',
            'address'     => '123 Maple Avenue'
        ]);

        $importService = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $payload = $this->getFixtureByPath('payloads/buygoods/buygoods_ipn.json');
        $importService->import($account, $payload);
        $actual = Order::where(['source_ref' => 'ABCDEFG1'])->first();
        $this->assertNotNull($actual);
        $payload = $actual->payloads->first();
        $customer = $actual->customer;
        $this->assertCount(4, $customer->addresses);

        $payloadService->run($payload);

        $customer->refresh();
        $this->assertCount(4, $customer->addresses);
    }

    public function testImportOrderAbondonCheckOrderTotalAmount()
    {
        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);

        Offer::factory()->create([
            'type'       => OfferType::INITIAL->value,
            'account_id' => $account->id,
            'source_ref' => '2_FS6_257',
            'price'      => Money::USD(20.00),
        ]);

        $importService = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $payload = $this->getJsonFixture('abandon2.json');
        $importService->import($account, $payload);


        $customer = Customer::where(['email' => '<EMAIL>'])->first();
        $this->assertNotNull($customer);

        $actual = Order::where(['customer_id' => $customer->id])->first();
        $this->assertNotNull($actual);
        $this->assertEquals(Money::USD('257.00'), $actual->total_amount);
    }

    public function testImportOrderAbondonUpdateTotalAmountByPayloadReplayed()
    {
        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);

        Offer::factory()->create([
            'type'       => OfferType::INITIAL->value,
            'account_id' => $account->id,
            'source_ref' => '2_FS6_257',
            'price'      => Money::USD(20.00),
        ]);

        $importService = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $payload = $this->getJsonFixture('abandon2.json');

        $strategy = new BuygoodsIpnStrategy();
        /** @var PayloadService $payloadService */
        $payloadService = app()->make(PayloadService::class);
        $payloadModel = $payloadService->getPayload(PayloadSource::BUYGOODS_IPN, $payload);
        $strategy->setPayload($payloadModel);

        $sourceRef = OrderStatus::ABANDONED->value . '_' . md5(json_encode($payload));
        $importService->import($account, $payload);

        $actual = Order::where(['account_id' => $account->id, 'status' => OrderStatus::ABANDONED->value])->first();
        $this->assertNotNull($actual);
        $this->assertEquals(Money::USD('257.00'), $actual->total_amount);
    }

    public function testImportOrderRefundedAtCanceledAt()
    {
        $account = Account::factory()->create([
            'source'     => AccountSource::BUYGOODS->value,
            'source_ref' => 'acc-foo'
        ]);

        Offer::factory()->create([
            'type'       => OfferType::INITIAL->value,
            'account_id' => $account->id,
            'source_ref' => '3_BF6_099',
            'price'      => Money::USD(99.00),
        ]);

        $importService = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $payload = $this->getJsonFixture('test_3.json');
        $importService->import($account, $payload);

        $actual = Order::where(['account_id' => $account->id, 'source_ref' => 'FOOBAR'])->first();
        $this->assertNotNull($actual);
        $this->assertEquals(OrderStatus::COMPLETED->value, $actual->status);
        $this->assertEquals(Money::parse(0), $actual->refunded_amount);
        $this->assertNull($actual->refunded_at);
        $this->assertNull($actual->source_refunded_at);
        $this->assertNull($actual->canceled_at);
        $this->assertNull($actual->source_canceled_at);
    }

    public function testTwoRefunds()
    {
        $account = Account::factory()->create([
            'source'     => AccountSource::BUYGOODS->value,
            'source_ref' => 'acc-foo'
        ]);

        Offer::factory()->create([
            'type'       => OfferType::INITIAL->value,
            'account_id' => $account->id,
            'source_ref' => '1_FS1_059',
            'price'      => Money::USD(59.00),
        ]);

        $importService = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $importService->import($account, $this->getJsonFixture('2rfnd_1.json'));
        $importService->import($account, $this->getJsonFixture('2rfnd_2.json'));
        $importService->import($account, $this->getJsonFixture('2rfnd_3.json'));
        $importService->import($account, $this->getJsonFixture('2rfnd_4.json'));

        $actual = Order::where(['account_id' => $account->id, 'source_ref' => 'FOOBAR'])->first();
        $this->assertNotNull($actual);
        $this->assertEquals(6899, $actual->total_amount->getAmount());
        $this->assertEquals(OrderStatus::REFUNDED->value, $actual->status);
        $this->assertCount(2, $actual->refunds);
        $this->assertEquals(6899, $actual->refunded_amount->getAmount());
        $this->assertEquals(Money::USD('59.00'), $actual->refunds->first()->amount);
        $this->assertEquals(Money::USD('9.99'), $actual->refunds->last()->amount);
    }

    public function testRepairOrderWithTwoRefunds()
    {
        $account = Account::factory()->create([
            'source'     => AccountSource::BUYGOODS->value,
            'source_ref' => 'acc-foo'
        ]);

        $offer = Offer::factory()->create([
            'type'       => OfferType::INITIAL->value,
            'account_id' => $account->id,
            'source_ref' => '1_FS1_059',
            'price'      => Money::USD(59.00),
        ]);
        $order = Order::factory()->create([
            'account_id'      => $account->id,
            'source_ref'      => 'FOOBAR',
            'total_amount'    => Money::USD('68.99'),
            'refunded_amount' => Money::USD('9.99'),
            'status'          => OrderStatus::REFUNDED->value
        ]);

        OrderOffer::factory()->create(['offer_id' => $offer->id, 'order_id' => $order->id]);
        $refund = Refund::factory()->create(['order_id' => $order->id, 'amount' => Money::USD('9.99')]);

        /** @var PayloadService $payloadService */
        $payloadService = app()->make(PayloadService::class);
        $payload = $payloadService->getPayload(PayloadSource::BUYGOODS_IPN, $this->getJsonFixture('2rfnd_1.json'), 'FOOBAR');
        $payloadService->attachModelToPayload($payload, $order, true);
        $payload2 = $payloadService->getPayload(PayloadSource::BUYGOODS_IPN, $this->getJsonFixture('2rfnd_2.json'), 'FOOBAR');
        $payloadService->attachModelToPayload($payload2, $order, true);
        $payload3 = $payloadService->getPayload(PayloadSource::BUYGOODS_IPN, $this->getJsonFixture('2rfnd_3.json'), 'FOOBAR');
        $payloadService->attachModelToPayload($payload3, $order, true);
        $payload4 = $payloadService->getPayload(PayloadSource::BUYGOODS_IPN, $this->getJsonFixture('2rfnd_4.json'), 'FOOBAR');
        $payloadService->attachModelToPayload($payload4, $order, true);

        Artisan::call('repair:orders 1 --source-ref=FOOBAR');

        $actual = Order::where(['account_id' => $account->id, 'source_ref' => 'FOOBAR'])->first();
        $this->assertNotNull($actual);
        $this->assertEquals(6899, $actual->total_amount->getAmount());
        $this->assertEquals(OrderStatus::REFUNDED->value, $actual->status);
        $this->assertEquals(6899, $actual->refunded_amount->getAmount());
        $this->assertCount(2, $actual->refunds);
        $this->assertEquals(Money::USD('59.00'), $actual->refunds->last()->amount);
        $this->assertEquals(Money::USD('9.99'), $actual->refunds->first()->amount);
    }

    public function testImportEmptySourceref()
    {
        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);
        Offer::factory()->create(['account_id' => $account->id, 'source_ref' => '2_FS6_097']);

        $importService = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $payload = $this->getJsonFixture('import.json');
        unset($payload['order_id_global']);
        $actual = $importService->import($account, $payload);
        $this->assertNull($actual);
        $payloadModel = $importService->getPayload();
        $this->assertNotNull($payloadModel);
    }

    public function testSaveAddressStateAndCountry()
    {
        app()->bind(AddressServiceContract::class, AddressService::class);

        Artisan::call('db:seed --class=CountriesSeeder');
        $account = Account::factory()->create([
            'source'     => AccountSource::BUYGOODS->value,
            'source_ref' => 'acc-foo'
        ]);

        Offer::factory()->create(['account_id' => $account->id, 'source_ref' => '1_FS7_234']);

        $importService = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $payload = $this->getFixtureByPath('payloads/buygoods/abandon_event.json');
        $actual = $importService->import($account, $payload);
        $addresses = $actual->customer->addresses;
        $this->assertCount(2, $addresses);

        foreach ($addresses as $address) {
            $this->assertEquals('John', $address->first_name);
            $this->assertEquals('Doe', $address->last_name);
            $this->assertEquals('New Haven', $address->city);
            $this->assertEquals('AL', $address->state);
            $this->assertEquals('US', $address->country);
        }
    }

    public function testImportAbandonEventOfferNotExists()
    {
        $account = Account::factory()->create([
            'source'     => AccountSource::BUYGOODS->value,
            'source_ref' => '1234'
        ]);

        $importService = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $payload = $this->getFixtureByPath('/payloads/buygoods/abandon_event_2.json');
        $actual = $importService->import($account, $payload);
        $this->assertNotNull($actual);
        $this->assertEquals(Money::parse(0), $actual->total_amount);
        $this->assertCount(0, $actual->orderOffers);
        $payloadModel = $importService->getPayload();
        $this->assertNotNull($payloadModel);
        $this->assertEquals(PayloadStatus::DELAYED->value, $payloadModel->status);
        $this->assertNotNull($payloadModel->retry_at);
        Offer::factory()->create([
            'account_id' => $account->id,
            'source_ref' => '1_ABCD_100',
            'price'      => Money::parse(100)
        ]);
        $payloadService = app()->make(PayloadService::class);
        $payloadService->run($payloadModel);
        $payloadModel->refresh();
        $this->assertEquals(PayloadStatus::SUCCESS->value, $payloadModel->status);

        $actual = Order::where(['account_id' => $account->id])->first();
        $this->assertNotNull($actual);
        $this->assertEquals(Money::parse('100.0'), $actual->total_amount);
        $this->assertCount(1, $actual->orderOffers);
    }

    public function testParseProductSourceRefSuccess()
    {
        DB::table('countries')->insert([
            'id' => 1,
            'capital' => 'Berlin',
            'citizenship' => 'German',
            'country_code' => 279,
            'currency' => 'Euro',
            'currency_code' => 'EUR',
            'full_name' => 'Federal Republic of Germany',
            'currency_decimals' => 2,
            'iso_3166_2' => 'DE',
            'iso_3166_3' => 'DEU',
            'name' => 'Germany',
            'region_code' => 150,
            'sub_region_code' => 155,
            'eea' => 1,
            'calling_code' => 1,
        ]);

        /** @var BuygoodsIpnStrategy $strategy */
        $strategy = app()->make(BuygoodsIpnStrategy::class);
        $actual = $strategy->parseProductSourceRef('DE_1_FS1_059');
        $expected = [
            'currency'    => 'USD',
            'type'        => OfferType::INITIAL->value,
            'productCode' => 'FS',
            'quantity'    => '1',
            'price'       => Money::parse('59.0')->absolute()
        ];

        $this->assertEquals($expected, $actual);

        $actual = $strategy->parseProductSourceRef('1_FS1_059');
        $this->assertEquals($expected, $actual);

        $this->expectException(ParserException::class);
        $strategy->parseProductSourceRef('FOO_1_FS1_059');
    }
    public function testImportOrderWithUtm()
    {
        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);
        Offer::factory()->create(['account_id' => $account->id, 'source_ref' => '2_FS6_097']);

        $importService = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $payload = $this->getJsonFixture('import.json');
        $payload['vid1'] = 'affiliate||TestMedium||test.com||FOO_BAR||';
        $order = $importService->import($account, $payload);
        $this->assertNotNull($order);

        $actualTracking = Tracking::where(['order_id' => $order->id, 'key' => 'vid1'])->first();
        $this->assertNotNull($actualTracking);
        $this->assertEquals('affiliate||TestMedium||test.com||FOO_BAR||', $actualTracking->value);
        $actualSource = TrackingUtm::where(['order_id' => $order->id, 'utm_name' => 'utm_source'])->first();
        $this->assertNotNull($actualSource);
        $this->assertEquals('affiliate', $actualSource->utm_value);

        $actualMedium = TrackingUtm::where(['order_id' => $order->id, 'utm_name' => 'utm_medium'])->first();
        $this->assertNotNull($actualMedium);
        $this->assertEquals('TestMedium', $actualMedium->utm_value);

        $actualCampaign = TrackingUtm::where(['order_id' => $order->id, 'utm_name' => 'utm_campaign'])->first();
        $this->assertNotNull($actualCampaign);
        $this->assertEquals('test.com', $actualCampaign->utm_value);

        $actualContent = TrackingUtm::where(['order_id' => $order->id, 'utm_name' => 'utm_content'])->first();
        $this->assertNotNull($actualContent);
        $this->assertEquals('FOO_BAR', $actualContent->utm_value);
    }
}
