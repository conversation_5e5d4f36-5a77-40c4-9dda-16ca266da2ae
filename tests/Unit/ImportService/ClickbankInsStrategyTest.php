<?php

namespace App\Tests\Unit\ImportService;

use App\Dto\ImportOrderDto;
use App\Enums\AccountSource;
use App\Enums\AddressType;
use App\Enums\OrderStatus;
use App\Enums\PayloadSource;
use App\Enums\PayloadStatus;
use App\Enums\ProductCostType;
use App\Models\Account;
use App\Models\Offer;
use App\Models\Order;
use App\Models\Payload;
use App\Models\Product;
use App\Models\ProductCost;
use App\Models\Tracking;
use App\Models\TrackingUtm;
use App\Services\ImportService\Clients\ClickbankApi;
use App\Services\ImportService\Exceptions\ClickbankException;
use App\Services\ImportService\Order\ImportOrderService;
use App\Services\ImportService\Strategies\ClickbankInsStrategy;
use App\Tests\TestCase;
use Cknow\Money\Money;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Config;
use Mockery;
use Mo<PERSON>y\MockInterface;

class ClickbankInsStrategyTest extends TestCase
{
    use ExpectedDto;

    public function setUp(): void
    {
        parent::setUp();
        Config::set('services.konnektive.enabled', false);
    }

    public function testGetImportOrderDtoAbandon()
    {
        $source = 'clickbank-ins-abandon';
        $account = Account::factory()->create([
            'source'     => AccountSource::CLICKBANK->value,
            'source_ref' => 'foo-acc',
            'api_key'    => 'api_key',
        ]);

        $data = $this->getJsonFixture('abandon_order.json');
        $strategy = new ClickbankInsStrategy();
        $strategy->setAccount($account);
        $actual = $strategy->getImportOrderDto($strategy->preparePayloadData($data));
        logger(json_encode($this->expectedTrackingDto(AccountSource::CLICKBANK->value, $source)));

        unset($data['attemptCount']);
        $sourceRef = OrderStatus::ABANDONED->value.'_'.md5(
                json_encode(array_merge($data, ['account_id' => $account->id]))
            );

        $expected = new ImportOrderDto([
            'order'           => $this->expectedOrderDto($source),
            'customer'        => $this->expectedCustomerDto($account, $source),
            'affiliate'       => $this->expectedAffiliateDto($source),
            'meta'            => $this->expectedMetaDto(AccountSource::CLICKBANK->value),
            'tracking'        => $this->expectedTrackingDto(AccountSource::CLICKBANK->value, $source),
            'offers'          => [$this->expectedOfferDto($source)],
            'transactionType' => 'ABANDONED_ORDER',
            'address'         => $this->expectedAddressDto($source),
            'shippingAddress' => $this->expectedShippingAddressDto($source),
        ]);

        $this->assertEquals($expected->affiliate, $actual->affiliate, 'Affiliate dto');
        $this->assertEquals($expected->customer, $actual->customer, 'Customer dto');
        $this->assertEquals($sourceRef, $actual->order->sourceRef, 'Order source ref');
        $this->assertEquals(Arr::except($expected->order->toArray(), ['sourceRef']),
            Arr::except($actual->order->toArray(), ['sourceRef']),
            'Order dto');
        $this->assertEquals($expected->meta, $actual->meta, 'Meta dto');
        $this->assertEquals($expected->tracking, $actual->tracking, 'Tracking dto');
        $this->assertEquals($expected->offers, $actual->offers, 'Offers dto');
        $this->assertEquals($expected->address, $actual->address, 'Address dto');
        $this->assertEquals($expected->shippingAddress, $actual->shippingAddress, 'Shipping address dto');
    }

    public function testGetImportOrderDtoSale()
    {
        $source = 'clickbank-ins-sale';
        $account = Account::factory()->create([
            'source'     => AccountSource::CLICKBANK->value,
            'source_ref' => 'foo-acc',
            'api_key'    => 'api_key',
        ]);

        $data = $this->getJsonFixture('sale_order.json');

        $strategy = new ClickbankInsStrategy();
        $strategy->setAccount($account);
        $actual = $strategy->getImportOrderDto($strategy->preparePayloadData($data));
        logger(json_encode($this->expectedTrackingDto(AccountSource::CLICKBANK->value, $source)));

        $expected = new ImportOrderDto([
            'order'           => $this->expectedOrderDto($source),
            'customer'        => $this->expectedCustomerDto($account, $source),
            'affiliate'       => $this->expectedAffiliateDto($source),
            'meta'            => $this->expectedMetaDto(AccountSource::CLICKBANK->value, $source),
            'tracking'        => $this->expectedTrackingDto(AccountSource::CLICKBANK->value, $source),
            'offers'          => [$this->expectedOfferDto($source)],
            'transactionType' => 'SALE',
            'address'         => $this->expectedAddressDto($source),
            'shippingAddress' => $this->expectedShippingAddressDto($source),
        ]);

        $this->assertEquals($expected->affiliate, $actual->affiliate, 'Affiliate dto');
        $this->assertEquals($expected->customer, $actual->customer, 'Customer dto');
        $this->assertEquals($expected->order, $actual->order, 'Order dto');
        $this->assertEquals($expected->meta, $actual->meta, 'Meta dto');
        $this->assertEquals($expected->tracking, $actual->tracking, 'Tracking dto');
        $this->assertEquals($expected->offers, $actual->offers, 'Offers dto');
        $this->assertEquals($expected->address, $actual->address, 'Address dto');
        $this->assertEquals($expected->shippingAddress, $actual->shippingAddress, 'Shipping address dto');
    }

    public function testPurchasedAtFormOrderApiMethodSuccess()
    {
        $mock = Mockery::mock(ClickbankApi::class, function (MockInterface $mock) {
            $mock->shouldReceive('setApiKey');
            $mock->shouldReceive('getOrdersByReceipt')->andReturn(collect([[
                'receipt' => 'FOOBAR',
                'transactionTime' => "2024-03-13T01:59:43-07:00",
                'transactionType' => 'payment'
            ]]));
        });
        app()->instance(ClickbankApi::class, $mock);
        $account = Account::factory()->create([
            'source'     => AccountSource::CLICKBANK->value,
            'source_ref' => 'foo-acc',
            'api_key'    => 'api_key',
        ]);

        $strategy = new ClickbankInsStrategy();
        $strategy->setAccount($account);
        [$purchasedAt, $sourcePurchasedAt] = $strategy->getPurchasedAtFromOrderApi('foo-bar');
        $this->assertEquals('2024-03-13 01:59:43', $sourcePurchasedAt->format('Y-m-d H:i:s'));
        $this->assertEquals('2024-03-13 04:59:43', $purchasedAt->format('Y-m-d H:i:s'));
    }

    public function testPurchasedAtFormOrderApiMethodApiKeyEmpty()
    {
        $account = Account::factory()->create([
            'source'     => AccountSource::CLICKBANK->value,
            'source_ref' => 'foo-acc',
            'api_key'    => null
        ]);

        $strategy = new ClickbankInsStrategy();
        $strategy->setAccount($account);
        $this->expectException(ClickbankException::class);
        $strategy->getPurchasedAtFromOrderApi('foo-bar');
    }

    public function testPurchasedAtFormOrderApiMethodReturnNull()
    {
        $mock = Mockery::mock(ClickbankApi::class, function (MockInterface $mock) {
            $mock->shouldReceive('setApiKey');
            $mock->shouldReceive('getOrdersByReceipt')->andReturn(collect());
        });

        app()->instance(ClickbankApi::class, $mock);
        $account = Account::factory()->create([
            'source'     => AccountSource::CLICKBANK->value,
            'source_ref' => 'foo-acc',
            'api_key'    => 'api_key',
        ]);

        $strategy = new ClickbankInsStrategy();
        $strategy->setAccount($account);
        $actual = $strategy->getPurchasedAtFromOrderApi('foo-bar');
        $this->assertEquals([null, null], $actual);
    }

    public function testPurchasedAtFormOrderApiMethodNotReturnTransactionTime()
    {
        $mock = Mockery::mock(ClickbankApi::class, function (MockInterface $mock) {
            $mock->shouldReceive('setApiKey');
            $mock->shouldReceive('getOrdersByReceipt')->andReturn(collect());
        });

        app()->instance(ClickbankApi::class, $mock);
        $account = Account::factory()->create([
            'source'     => AccountSource::CLICKBANK->value,
            'source_ref' => 'foo-acc',
            'api_key'    => 'api_key',
        ]);

        $strategy = new ClickbankInsStrategy();
        $strategy->setAccount($account);
        $actual = $strategy->getPurchasedAtFromOrderApi('foo-bar');
        $this->assertEquals([null, null], $actual);
    }

    public function testImportClickbankInsSaleOrder()
    {
        $account = Account::factory()->create(['source_ref' => 'acc-foo']);
        $offer = Offer::factory()->create([
            'account_id' => $account->id,
            'source_ref' => '1-FS3-147',
            'price'      => Money::USD('147.00'),
            'quantity'   => 3,
        ]);
        $product = Product::factory()->create(['default_price' => Money::parse(0)]);
        ProductCost::factory()->create([
            'product_id' => $product->id,
            'type' => ProductCostType::COG->value,
            'amount' => Money::parse('4.48'),
        ]);
        ProductCost::factory()->create([
            'product_id' => $product->id,
            'type' => ProductCostType::AVG_SHIPPING->value,
            'amount' => Money::parse('1.05'),
        ]);
        $offer->productAttach($product->id, 1);

        $data = $this->getFixtureByPath('/ImportOrderServiceTest/cb_ins_sale.json');
        $importService = new ImportOrderService(PayloadSource::CLICKBANK_INS);
        $order = $importService->import($account, $data);
        $this->assertNotNull($order);

        $actualTracking = Tracking::where(['order_id' => $order->id, 'key' => 'utm_source'])->first();
        $this->assertNotNull($actualTracking);
        $actualSource = TrackingUtm::where(['order_id' => $order->id, 'utm_name' => 'utm_source'])->first();
        $this->assertNotNull($actualSource);
        $this->assertEquals('affiliate', $actualSource->utm_value);
        $this->assertEquals($actualTracking->id, $actualSource->tracking_id);

        $actualTracking = Tracking::where(['order_id' => $order->id, 'key' => 'utm_medium'])->first();
        $this->assertNotNull($actualTracking);
        $actualMedium = TrackingUtm::where(['order_id' => $order->id, 'utm_name' => 'utm_medium'])->first();
        $this->assertNotNull($actualMedium);
        $this->assertEquals('TestMedium', $actualMedium->utm_value);
        $this->assertEquals($actualTracking->id, $actualMedium->tracking_id);

        $actualTracking = Tracking::where(['order_id' => $order->id, 'key' => 'utm_campaign'])->first();
        $this->assertNotNull($actualTracking);
        $actualCampaign = TrackingUtm::where(['order_id' => $order->id, 'utm_name' => 'utm_campaign'])->first();
        $this->assertNotNull($actualCampaign);
        $this->assertEquals('test.com', $actualCampaign->utm_value);
        $this->assertEquals($actualTracking->id, $actualCampaign->tracking_id);

        $actualTracking = Tracking::where(['order_id' => $order->id, 'key' => 'utm_content'])->first();
        $this->assertNotNull($actualTracking);
        $actualContent = TrackingUtm::where(['order_id' => $order->id, 'utm_name' => 'utm_content'])->first();
        $this->assertNotNull($actualContent);
        $this->assertEquals('FOO_BAR', $actualContent->utm_value);
        $this->assertEquals($actualTracking->id, $actualContent->tracking_id);
    }
}
