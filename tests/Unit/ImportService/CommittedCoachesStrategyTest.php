<?php

namespace App\Tests\Unit\ImportService;

use App\Enums\AccountSource;
use App\Enums\OrderStatus;
use App\Enums\PayloadSource;
use App\Enums\PayloadStatus;
use App\Enums\ProductCostType;
use App\Models\Account;
use App\Models\Product;
use App\Models\ProductCost;
use App\Models\ProductSku;
use App\Models\Refund;
use App\Services\ImportService\Order\ImportOrderService;
use App\Services\PayloadService;
use App\Tests\TestCase;
use Cknow\Money\Money;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class CommittedCoachesStrategyTest extends TestCase
{
    public function testImportOrderOnboardSuccess()
    {
        try {
            $product = ProductSku::where(['sku' => 'CommittedCoaches'])->firstOrFail()?->product;
        } catch (ModelNotFoundException $ex) {
            $product = null;
        }

        if (!$product) {
            $product = Product::factory()->create();
            ProductSku::factory(['sku' => 'CommittedCoaches', 'product_id' => $product->id])->create();

            ProductCost::factory()->create([
                'product_id' => $product->id,
                'type' => ProductCostType::COG->value,
                'amount' => Money::parse(1),
            ]);
        }

        try {
            Account::where(['source' => AccountSource::COMMITTED_COACHES->value])->firstOrFail();
        } catch (ModelNotFoundException $ex) {
            Account::factory()->create(['source' => AccountSource::COMMITTED_COACHES->value]);
        }

        $data = $this->getFixtureByPath('payloads/committed_coaches/onboard.json');
        $service = new ImportOrderService(PayloadSource::COMMITTED_COACHES_WEBHOOK);
        $account = $service->getAccount($data);
        $actual = $service->import($account, $data);
        $this->assertNotNull($actual);
        $this->assertEquals($data['transactionID'], $actual->source_ref);
        $this->assertEquals(Money::parse('1000.0'), $actual->total_amount);
        $this->assertEquals($account->id, $actual->account_id);
        $this->assertEquals(OrderStatus::COMPLETED->value, $actual->status);
        $this->assertCount(1, $actual->orderOffers);
        $offer = $actual->orderOffers->first()->offer;
        $this->assertEquals('Committed Coaches Plan $1000', $offer->name);
        $this->assertEquals(Money::parse('1000.0'), $offer->price);
        $this->assertEquals($product->id, $offer->products->first()->id);
        $this->assertEquals(1, $offer->products->first()->pivot->quantity);
        $this->assertEquals('<EMAIL>', $actual->customer->email);
        $this->assertEquals('+***********', $actual->customer->phone);
        $this->assertEquals('John', $actual->customer->first_name);
        $this->assertEquals('Doe', $actual->customer->last_name);
    }
    public function testImportOrderRefundSuccess()
    {
        try {
            $product = Product::whereHas('skus', function (Builder $builder) {
                $builder->where('sku', '=', 'CommittedCoaches');
            })->firstOrFail();
        } catch (ModelNotFoundException $ex) {
            $product = Product::factory()->create();
            ProductSku::factory()->create(['sku' => 'CommittedCoaches', 'product_id' => $product->id]);

            ProductCost::factory()->create([
                'product_id' => $product->id,
                'type' => ProductCostType::COG->value,
                'amount' => Money::parse(1),
            ]);
        }

        try {
            Account::where(['source' => AccountSource::COMMITTED_COACHES->value])->firstOrFail();
        } catch (ModelNotFoundException $ex) {
            Account::factory()->create(['source' => AccountSource::COMMITTED_COACHES->value]);
        }

        $onboardData = $this->getFixtureByPath('payloads/committed_coaches/onboard.json');
        $refundData = $this->getFixtureByPath('payloads/committed_coaches/refunded.json');
        $service = new ImportOrderService(PayloadSource::COMMITTED_COACHES_WEBHOOK);
        $account = $service->getAccount($onboardData);
        $actual = $service->import($account, $onboardData);
        $this->assertNotNull($actual);
        $actual = $service->import($account, $refundData);
        $this->assertEquals(OrderStatus::REFUNDED->value, $actual->status);
        $this->assertEquals($actual->total_amount, $actual->refunded_amount);
        $this->assertNotNull($actual->refunds->first());
    }

    public function testImportOrderCompletedSuccess()
    {
        try {
            $product = Product::whereHas('skus', function (Builder $builder) {
                $builder->where('sku', '=', 'CommittedCoaches');
            })->firstOrFail();
        } catch (ModelNotFoundException $ex) {
            $product = Product::factory()->create();
            ProductSku::factory()->create(['sku' => 'CommittedCoaches', 'product_id' => $product->id]);

            ProductCost::factory()->create([
                'product_id' => $product->id,
                'type' => ProductCostType::COG->value,
                'amount' => Money::parse(1),
            ]);
        }

        try {
            Account::where(['source' => AccountSource::COMMITTED_COACHES->value])->firstOrFail();
        } catch (ModelNotFoundException $ex) {
            Account::factory()->create(['source' => AccountSource::COMMITTED_COACHES->value]);
        }

        $data = $this->getFixtureByPath('payloads/committed_coaches/validation-errors.json');
        $service = new ImportOrderService(PayloadSource::COMMITTED_COACHES_WEBHOOK);
        $account = $service->getAccount($data);
        $actual = $service->import($account, $data);
        $this->assertNotNull($actual);
    }

    public function testRelayRefundOrderSuccess()
    {
        try {
            $product = Product::whereHas('skus', function (Builder $builder) {
                $builder->where('sku', '=', 'CommittedCoaches');
            })->firstOrFail();
        } catch (ModelNotFoundException $ex) {
            $product = Product::factory()->create();
            ProductSku::factory()->create(['sku' => 'CommittedCoaches', 'product_id' => $product->id]);

            ProductCost::factory()->create([
                                               'product_id' => $product->id,
                                               'type' => ProductCostType::COG->value,
                                               'amount' => Money::parse(1),
                                           ]);
        }

        try {
            Account::where(['source' => AccountSource::COMMITTED_COACHES->value])->firstOrFail();
        } catch (ModelNotFoundException $ex) {
            Account::factory()->create(['source' => AccountSource::COMMITTED_COACHES->value]);
        }

        $onboardData = $this->getFixtureByPath('payloads/committed_coaches/onboard.json');
        $refundData = $this->getFixtureByPath('payloads/committed_coaches/refunded.json');
        $service = new ImportOrderService(PayloadSource::COMMITTED_COACHES_WEBHOOK);
        $account = $service->getAccount($onboardData);
        $actual = $service->import($account, $onboardData);
        $this->assertNotNull($actual);
        $this->assertCount(1, $actual->payloads);
        $payload = $actual->payloads->first();
        $this->assertEquals(PayloadStatus::SUCCESS->value, $payload->status);
        $actual = $service->import($account, $refundData);
        $this->assertEquals(OrderStatus::REFUNDED->value, $actual->status);
        $this->assertEquals($actual->total_amount, $actual->refunded_amount);
        $this->assertNotNull($actual->refunds->first());
        $this->assertCount(2, $actual->payloads);
        $payload = $actual->payloads->last();
        $this->assertEquals(PayloadStatus::SUCCESS->value, $payload->status);

        /** @var PayloadService $payloadService */
        $payloadService = app()->make(PayloadService::class);
        $payloadService->run($payload);
        $payload->refresh();
        $refunds = Refund::where(['order_id' => $actual->id])->get();
        $this->assertCount(1, $refunds);
        $this->assertEquals(PayloadStatus::SUCCESS->value, $payload->status);
        $this->assertCount(1, $payload->orders);

        $payloadService->run($payload);
        $payload->refresh();
        $this->assertEquals(PayloadStatus::SUCCESS->value, $payload->status);
    }
}
