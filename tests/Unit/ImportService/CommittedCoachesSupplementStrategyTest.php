<?php

namespace App\Tests\Unit\ImportService;

use App\Enums\AccountSource;
use App\Enums\OrderStatus;
use App\Enums\PayloadSource;
use App\Enums\PayloadStatus;
use App\Enums\ProductCostType;
use App\Models\Account;
use App\Models\Offer;
use App\Models\Order;
use App\Models\Payload;
use App\Models\Product;
use App\Models\ProductCost;
use App\Models\ProductSku;
use App\Services\ImportService\Order\ImportOrderService;
use App\Services\ImportService\Strategies\CommittedCoachesSupplementStrategy;
use App\Services\PayloadService;
use App\Tests\TestCase;
use Cknow\Money\Money;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Notification;
use App\Services\ShipOffers\ShipOffersService;
use Illuminate\Support\Facades\Queue;
use Mockery;

class CommittedCoachesSupplementStrategyTest extends TestCase
{
    public function testImportOrderCompletedSuccess()
    {
        Queue::fake();
        try {
            $product = ProductSku::where(['sku' => 'sku_1'])->firstOrFail()?->product;
        } catch (ModelNotFoundException $ex) {
            $product = null;
        }

        if (!$product) {
            $product = Product::factory()->create();
            ProductSku::factory(['sku' => 'sku_1', 'product_id' => $product->id])->create();

            ProductCost::factory()->create([
                'product_id' => $product->id,
                'type'       => ProductCostType::COG->value,
                'amount'     => Money::parse(1),
            ]);
        }

        try {
            $product2 = ProductSku::where(['sku' => 'sku_2'])->firstOrFail()?->product;
        } catch (ModelNotFoundException $ex) {
            $product2 = null;
        }

        if (!$product2) {
            $product2 = Product::factory()->create();
            ProductSku::factory(['sku' => 'sku_2', 'product_id' => $product2->id])->create();

            ProductCost::factory()->create([
                'product_id' => $product2->id,
                'type'       => ProductCostType::COG->value,
                'amount'     => Money::parse(1),
            ]);
        }

        try {
            Account::where(['source' => AccountSource::COMMITTED_COACHES->value])->firstOrFail();
        } catch (ModelNotFoundException $ex) {
            Account::factory()->create(['source' => AccountSource::COMMITTED_COACHES->value]);
        }

        $mock = \Mockery::mock(ShipOffersService::class, function ($mock) {
            $mock->shouldReceive('sendOrder')->once();
        });
        app()->instance(ShipOffersService::class, $mock);

        $data = $this->getFixtureByPath('payloads/committed_coaches_supplement/new_order.json');
        unset($data['line_items'][0]);
        $service = new ImportOrderService(PayloadSource::COMMITTED_COACHES_SUPPLEMENT);
        $account = $service->getAccount($data);
        $actual = $service->import($account, $data);
        $this->assertNotNull($actual);
        $this->assertEquals($data['refnumber'], $actual->source_ref);
        $this->assertEquals(Money::parse('297.0'), $actual->total_amount);
        $this->assertEquals($account->id, $actual->account_id);
        $this->assertEquals(OrderStatus::PENDING_FULFILLMENT->value, $actual->status);
        $this->assertCount(1, $actual->orderOffers);

        $offer = $actual->orderOffers->last()->offer;
        $this->assertEquals('Pressure Ease', $offer->name);
        $this->assertEquals(Money::parse('69.0'), $offer->price);
        $this->assertEquals(1, $offer->quantity);

        $this->assertEquals('<EMAIL>', $actual->customer->email);
        $this->assertEquals('+***********', $actual->customer->phone);
        $this->assertEquals('John', $actual->customer->first_name);
        $this->assertEquals('Doe', $actual->customer->last_name);

        $this->assertNotNull($actual->shippingAddress);
        $this->assertEquals('John', $actual->shippingAddress->first_name);
        $this->assertEquals('Doe', $actual->shippingAddress->last_name);
        $this->assertEquals('123 address', $actual->shippingAddress->address);
        $this->assertEquals('AZ', $actual->shippingAddress->state);
        $this->assertEquals('Fake city', $actual->shippingAddress->city);

        $payload = Payload::where([
            'source'     => PayloadSource::COMMITTED_COACHES_SUPPLEMENT->value,
            'source_ref' => 'NewOrder_FOO-BAR',
        ])->first();

        $this->assertNotNull($payload);
        $this->assertEquals(PayloadStatus::SUCCESS->value, $payload->status);
    }

    public function testImportOrderCanceledSuccess()
    {
        Queue::fake();
        try {
            $product = ProductSku::where(['sku' => 'sku_1'])->firstOrFail()?->product;
        } catch (ModelNotFoundException $ex) {
            $product = null;
        }

        if (!$product) {
            $product = Product::factory()->create();
            ProductSku::factory(['sku' => 'sku_1', 'product_id' => $product->id])->create();

            ProductCost::factory()->create([
                'product_id' => $product->id,
                'type'       => ProductCostType::COG->value,
                'amount'     => Money::parse(1),
            ]);
        }

        try {
            $product2 = ProductSku::where(['sku' => 'sku_2'])->firstOrFail()?->product;
        } catch (ModelNotFoundException $ex) {
            $product2 = null;
        }

        if (!$product2) {
            $product2 = Product::factory()->create();
            ProductSku::factory(['sku' => 'sku_2', 'product_id' => $product2->id])->create();

            ProductCost::factory()->create([
                'product_id' => $product2->id,
                'type'       => ProductCostType::COG->value,
                'amount'     => Money::parse(1),
            ]);
        }

        try {
            Account::where(['source' => AccountSource::COMMITTED_COACHES->value])->firstOrFail();
        } catch (ModelNotFoundException $ex) {
            Account::factory()->create(['source' => AccountSource::COMMITTED_COACHES->value]);
        }

        $mock = Mockery::mock(ShipOffersService::class, function ($mock) {
            $mock->shouldReceive('sendOrder')->once();
            $mock->shouldReceive('cancelOrder')->once()->andReturn(true);
        });
        app()->instance(ShipOffersService::class, $mock);

        $data = $this->getFixtureByPath('payloads/committed_coaches_supplement/new_order.json');
        unset($data['line_items'][0]);
        $service = new ImportOrderService(PayloadSource::COMMITTED_COACHES_SUPPLEMENT);
        $account = $service->getAccount($data);
        $actual = $service->import($account, $data);
        $this->assertNotNull($actual);
        $this->assertEquals(OrderStatus::PENDING_FULFILLMENT->value, $actual->status);

        $data = $this->getFixtureByPath('payloads/committed_coaches_supplement/cancel.json');
        $actual = $service->import($account, $data);
        $this->assertEquals(OrderStatus::CANCELED->value, $actual->status);

        $payload = Payload::where([
            'source'     => PayloadSource::COMMITTED_COACHES_SUPPLEMENT->value,
            'source_ref' => 'CancelOrder_FOO-BAR',
        ])->first();

        $this->assertNotNull($payload);
        $this->assertEquals(PayloadStatus::SUCCESS->value, $payload->status);
    }

    public function testImportOrderCanceledError()
    {
        Queue::fake();
        try {
            $product = ProductSku::where(['sku' => 'sku_1'])->firstOrFail()?->product;
        } catch (ModelNotFoundException $ex) {
            $product = null;
        }

        if (!$product) {
            $product = Product::factory()->create();
            ProductSku::factory(['sku' => 'sku_1', 'product_id' => $product->id])->create();

            ProductCost::factory()->create([
                'product_id' => $product->id,
                'type'       => ProductCostType::COG->value,
                'amount'     => Money::parse(1),
            ]);
        }

        try {
            $product2 = ProductSku::where(['sku' => 'sku_2'])->firstOrFail()?->product;
        } catch (ModelNotFoundException $ex) {
            $product2 = null;
        }

        if (!$product2) {
            $product2 = Product::factory()->create();
            ProductSku::factory(['sku' => 'sku_2', 'product_id' => $product2->id])->create();

            ProductCost::factory()->create([
                'product_id' => $product2->id,
                'type'       => ProductCostType::COG->value,
                'amount'     => Money::parse(1),
            ]);
        }

        try {
            Account::where(['source' => AccountSource::COMMITTED_COACHES->value])->firstOrFail();
        } catch (ModelNotFoundException $ex) {
            Account::factory()->create(['source' => AccountSource::COMMITTED_COACHES->value]);
        }

        $mock = Mockery::mock(ShipOffersService::class, function ($mock) {
            $mock->shouldReceive('sendOrder')->twice();
            $mock->shouldReceive('cancelOrder')->once()->andReturn(false);
        });
        app()->instance(ShipOffersService::class, $mock);

        $data = $this->getFixtureByPath('payloads/committed_coaches_supplement/new_order.json');
        unset($data['line_items'][0]);
        $service = new ImportOrderService(PayloadSource::COMMITTED_COACHES_SUPPLEMENT);
        $account = $service->getAccount($data);
        $actual = $service->import($account, $data);
        $this->assertNotNull($actual);
        $this->assertEquals(OrderStatus::PENDING_FULFILLMENT->value, $actual->status);

        $data = $this->getFixtureByPath('payloads/committed_coaches_supplement/cancel.json');
        $actual = $service->import($account, $data);
        $this->assertEquals(OrderStatus::PENDING_FULFILLMENT->value, $actual->status);
    }

    public function testImportOrderCompletedProductMissing()
    {
        Notification::fake();
        Queue::fake();
        try {
            $product = ProductSku::where(['sku' => 'sku_1'])->firstOrFail()?->product;
        } catch (ModelNotFoundException $ex) {
            $product = null;
        }

        if (!$product) {
            $product = Product::factory()->create();
            ProductSku::factory(['sku' => 'sku_1', 'product_id' => $product->id])->create();

            ProductCost::factory()->create([
                'product_id' => $product->id,
                'type'       => ProductCostType::COG->value,
                'amount'     => Money::parse(1),
            ]);
        }

        try {
            Account::where(['source' => AccountSource::COMMITTED_COACHES->value])->firstOrFail();
        } catch (ModelNotFoundException $ex) {
            Account::factory()->create(['source' => AccountSource::COMMITTED_COACHES->value]);
        }

        $data = $this->getFixtureByPath('payloads/committed_coaches_supplement/new_order.json');
        unset($data['line_items'][0]);
        $service = new ImportOrderService(PayloadSource::COMMITTED_COACHES_SUPPLEMENT);
        $account = $service->getAccount($data);
        $actual = $service->import($account, $data);
        $this->assertNotNull($actual);

        $payload = Payload::where([
            'source'     => PayloadSource::COMMITTED_COACHES_SUPPLEMENT->value,
            'source_ref' => 'NewOrder_FOO-BAR',
        ])->first();

        $this->assertNotNull($payload);
        $this->assertEquals(PayloadStatus::DELAYED->value, $payload->status);
        Notification::assertCount(1);

        try {
            $product2 = ProductSku::where(['sku' => 'sku_2'])->firstOrFail()?->product;
        } catch (ModelNotFoundException $ex) {
            $product2 = null;
        }

        if (!$product2) {
            $product2 = Product::factory()->create();
            ProductSku::factory(['sku' => 'sku_2', 'product_id' => $product2->id])->create();

            ProductCost::factory()->create([
                'product_id' => $product2->id,
                'type'       => ProductCostType::COG->value,
                'amount'     => Money::parse(1),
            ]);
        }

        /** @var PayloadService $payloadService */
        $payloadService = app()->make(PayloadService::class);
        $payloadService->run($payload);

        $payload->refresh();
        $this->assertEquals(PayloadStatus::SUCCESS->value, $payload->status);

        $actual->refresh();

        $offer = $actual->orderOffers->last()->offer;
        $this->assertEquals('Pressure Ease', $offer->name);
        $this->assertEquals(Money::parse('69.0'), $offer->price);
        $this->assertEquals(1, $offer->quantity);
        $this->assertEquals($product2->id, $offer->products->first()->id);
        $this->assertEquals(1, $offer->products->first()->pivot->quantity);
    }

    public function testImportOrderCompletedOfferMissing()
    {
        Notification::fake();
        Queue::fake();
        try {
            $product = ProductSku::where(['sku' => 'sku_1'])->firstOrFail()?->product;
        } catch (ModelNotFoundException $ex) {
            $product = null;
        }

        if (!$product) {
            $product = Product::factory()->create();
            ProductSku::factory(['sku' => 'sku_1', 'product_id' => $product->id])->create();

            ProductCost::factory()->create([
                'product_id' => $product->id,
                'type'       => ProductCostType::COG->value,
                'amount'     => Money::parse(1),
            ]);
        }

        try {
            Account::where(['source' => AccountSource::COMMITTED_COACHES->value])->firstOrFail();
        } catch (ModelNotFoundException $ex) {
            Account::factory()->create(['source' => AccountSource::COMMITTED_COACHES->value]);
        }

        $data = $this->getFixtureByPath('payloads/committed_coaches_supplement/new_order_offer_missing.json');
        $service = new ImportOrderService(PayloadSource::COMMITTED_COACHES_SUPPLEMENT);
        $account = $service->getAccount($data);
        $actual = $service->import($account, $data);
        $this->assertNull($actual);

        $payload = Payload::where([
            'source'     => PayloadSource::COMMITTED_COACHES_SUPPLEMENT->value,
            'source_ref' => 'NewOrder_FOO-BAR',
        ])->first();

        $this->assertNotNull($payload);
        $this->assertEquals(PayloadStatus::DELAYED->value, $payload->status);

        try {
            $product2 = ProductSku::where(['sku' => 'sku_2'])->firstOrFail()?->product;
        } catch (ModelNotFoundException $ex) {
            $product2 = null;
        }

        if (!$product2) {
            $product2 = Product::factory()->create();
            ProductSku::factory(['sku' => 'sku_2', 'product_id' => $product2->id])->create();

            ProductCost::factory()->create([
                'product_id' => $product2->id,
                'type'       => ProductCostType::COG->value,
                'amount'     => Money::parse(1),
            ]);
        }

        /** @var PayloadService $payloadService */
        $payloadService = app()->make(PayloadService::class);
        $payloadService->run($payload);

        $payload->refresh();
        $this->assertEquals(PayloadStatus::DELAYED->value, $payload->status);
    }

    public function testImportOrderCompletedSuccessWithoutLastName()
    {
        Queue::fake();
        try {
            $product = ProductSku::where(['sku' => 'sku_1'])->firstOrFail()?->product;
        } catch (ModelNotFoundException $ex) {
            $product = null;
        }

        if (!$product) {
            $product = Product::factory()->create();
            ProductSku::factory(['sku' => 'sku_1', 'product_id' => $product->id])->create();

            ProductCost::factory()->create([
                'product_id' => $product->id,
                'type'       => ProductCostType::COG->value,
                'amount'     => Money::parse(1),
            ]);
        }

        try {
            $product2 = ProductSku::where(['sku' => 'sku_2'])->firstOrFail()?->product;
        } catch (ModelNotFoundException $ex) {
            $product2 = null;
        }

        if (!$product2) {
            $product2 = Product::factory()->create();
            ProductSku::factory(['sku' => 'sku_2', 'product_id' => $product2->id])->create();

            ProductCost::factory()->create([
                'product_id' => $product2->id,
                'type'       => ProductCostType::COG->value,
                'amount'     => Money::parse(1),
            ]);
        }

        try {
            Account::where(['source' => AccountSource::COMMITTED_COACHES->value])->firstOrFail();
        } catch (ModelNotFoundException $ex) {
            Account::factory()->create(['source' => AccountSource::COMMITTED_COACHES->value]);
        }

        $data = $this->getFixtureByPath('payloads/committed_coaches_supplement/new_order.json');
        unset($data['line_items'][0]);
        $data['name'] = 'John';
        $service = new ImportOrderService(PayloadSource::COMMITTED_COACHES_SUPPLEMENT);
        $account = $service->getAccount($data);
        $actual = $service->import($account, $data);
        $this->assertNotNull($actual);
        $this->assertEquals($data['refnumber'], $actual->source_ref);
        $this->assertEquals(Money::parse('297.0'), $actual->total_amount);
        $this->assertEquals($account->id, $actual->account_id);
        $this->assertEquals(OrderStatus::PENDING_FULFILLMENT->value, $actual->status);
        $this->assertCount(1, $actual->orderOffers);

        $offer = $actual->orderOffers->last()->offer;
        $this->assertEquals('Pressure Ease', $offer->name);
        $this->assertEquals(Money::parse('69.0'), $offer->price);
        $this->assertEquals(1, $offer->quantity);

        $this->assertEquals('<EMAIL>', $actual->customer->email);
        $this->assertEquals('+***********', $actual->customer->phone);
        $this->assertEquals('John', $actual->customer->first_name);
        $this->assertNull($actual->customer->last_name);

        $this->assertNotNull($actual->shippingAddress);
        $this->assertEquals('John', $actual->shippingAddress->first_name);
        $this->assertNull($actual->shippingAddress->last_name);
        $this->assertEquals('123 address', $actual->shippingAddress->address);
        $this->assertEquals('AZ', $actual->shippingAddress->state);
        $this->assertEquals('Fake city', $actual->shippingAddress->city);

        $payload = Payload::where([
            'source'     => PayloadSource::COMMITTED_COACHES_SUPPLEMENT->value,
            'source_ref' => 'NewOrder_FOO-BAR',
        ])->first();

        $this->assertNotNull($payload);
        $this->assertEquals(PayloadStatus::SUCCESS->value, $payload->status);
    }

    public function testImportOrderAttachProductToExistingOfferSuccess()
    {
        Queue::fake();

        $payloads = Payload::where(['source' => PayloadSource::COMMITTED_COACHES_SUPPLEMENT->value])->get();

        foreach ($payloads as $payload) {
            $payload->delete();
        }

        foreach (Offer::get() as $offer) {
            $offer->delete();
        }

        try {
            $account = Account::where(['source' => AccountSource::COMMITTED_COACHES->value])->firstOrFail();
        } catch (ModelNotFoundException $ex) {
            $account = Account::factory()->create(['source' => AccountSource::COMMITTED_COACHES->value]);
        }

        $product1 = Product::factory()->create();
        ProductSku::factory(['sku' => '4251-PREBIOTC', 'product_id' => $product1->id])->create();
        $offer1 = Offer::factory()->create([
            'account_id' => $account->id,
            'name'       => 'Digest Pro',
            'source_ref' => 'DP',
        ]);
        $offer1->products()->sync([]);
        $offer1->productAttach($product1->id, 1);

        $product2 = Product::factory()->create();
        ProductSku::factory(['sku' => '1784-SUGBAL', 'product_id' => $product2->id])->create();
        $offer2 = Offer::factory()->create([
            'account_id' => $account->id,
            'source_ref' => 'SB',
            'name'       => 'SugarBalance',
        ]);
        $offer2->products()->sync([]);
        $offer2->productAttach($product2->id, 1);

        $product3 = Product::factory()->create();
        ProductSku::factory(['sku' => '2632-FMLSUP-108', 'product_id' => $product3->id])->create();
        $offer3 = Offer::factory()->create([
            'account_id' => $account->id,
            'source_ref' => 'TB',
            'name'       => 'TonicBurn',
        ]);
        $offer3->products()->sync([]);
        $data = $this->getFixtureByPath('payloads/committed_coaches_supplement/new_order_3_offers.json');
        unset($data['line_items'][0]);
        $service = new ImportOrderService(PayloadSource::COMMITTED_COACHES_SUPPLEMENT);
        $account = $service->getAccount($data);
        $actual = $service->import($account, $data);
        $this->assertNotNull($actual);
        $this->assertEquals($data['refnumber'], $actual->source_ref);
        $this->assertEquals($account->id, $actual->account_id);
        $this->assertEquals(OrderStatus::PENDING_FULFILLMENT->value, $actual->status);
        $this->assertCount(2, $actual->orderOffers);

        $payload = Payload::where([
            'source' => PayloadSource::COMMITTED_COACHES_SUPPLEMENT->value,
        ])->first();

        $this->assertNotNull($payload);
        $this->assertEquals(PayloadStatus::SUCCESS->value, $payload->status);

        $offer3->refresh();
        $actual = Offer::find($offer3->id);
        $this->assertCount(1, $actual->products);
        $this->assertEquals($product3->id, $actual->products->first()->id);
    }

    public function testCompletedMethod()
    {
        $mock = Mockery::mock(ShipOffersService::class, function ($mock) {
            $mock->shouldReceive('sendOrder')->twice()->andReturn([]);
        });
        app()->instance(ShipOffersService::class, $mock);

        /** CommittedCoachesSupplementStrategy $strategy */
        $strategy = app()->make(CommittedCoachesSupplementStrategy::class);
        $account = Account::factory()->create(['source' => AccountSource::COMMITTED_COACHES->value]);
        $orderCompleted = Order::factory()->create([
            'account_id' => $account->id,
            'status'     => OrderStatus::COMPLETED->value,
        ]);
        $orderCanceled = Order::factory()->create([
            'account_id' => $account->id,
            'status'     => OrderStatus::CANCELED->value,
        ]);
        $orderPending = Order::factory()->create([
            'account_id' => $account->id,
            'status'     => OrderStatus::PENDING_FULFILLMENT->value,
        ]);
        $orderCompletedAndFulfilled = Order::factory()->create([
            'account_id'   => $account->id,
            'status'       => OrderStatus::COMPLETED->value,
            'fulfilled_at' => now(),
        ]);

        $strategy->completed($orderCompleted, []);
        $strategy->completed($orderCanceled, []);
        $strategy->completed($orderPending, []);
        $strategy->completed($orderCompletedAndFulfilled, []);
    }

    public function testImportOrderErrorProductWithUnknownShortcode()
    {
        Queue::fake();
        try {
            $product = ProductSku::where(['sku' => 'sku_1'])->firstOrFail()?->product;
        } catch (ModelNotFoundException $ex) {
            $product = null;
        }

        if (!$product) {
            $product = Product::factory()->create();
            ProductSku::factory(['sku' => 'sku_1', 'product_id' => $product->id])->create();

            ProductCost::factory()->create([
                'product_id' => $product->id,
                'type'       => ProductCostType::COG->value,
                'amount'     => Money::parse(1),
            ]);
        }

        try {
            $product2 = ProductSku::where(['sku' => 'sku_2'])->firstOrFail()?->product;
        } catch (ModelNotFoundException $ex) {
            $product2 = null;
        }

        if (!$product2) {
            $product2 = Product::factory()->create();
            ProductSku::factory(['sku' => 'sku_2', 'product_id' => $product2->id])->create();

            ProductCost::factory()->create([
                'product_id' => $product2->id,
                'type'       => ProductCostType::COG->value,
                'amount'     => Money::parse(1),
            ]);
        }

        try {
            Account::where(['source' => AccountSource::COMMITTED_COACHES->value])->firstOrFail();
        } catch (ModelNotFoundException $ex) {
            Account::factory()->create(['source' => AccountSource::COMMITTED_COACHES->value]);
        }

        $data = $this->getFixtureByPath('payloads/committed_coaches_supplement/new_order.json');
        $data['line_items'][1]['shortcode'] = 'Unknown';
        $service = new ImportOrderService(PayloadSource::COMMITTED_COACHES_SUPPLEMENT);
        $account = $service->getAccount($data);
        $actual = $service->import($account, $data);
        $this->assertNull($actual);

        $payload = Payload::where([
            'source'     => PayloadSource::COMMITTED_COACHES_SUPPLEMENT->value,
            'source_ref' => 'NewOrder_FOO-BAR',
        ])->first();

        $this->assertNotNull($payload);
        $this->assertEquals(PayloadStatus::DELAYED->value, $payload->status);
    }
}
