<?php

namespace App\Tests\Unit\ImportService;

use App\Dto\AffiliateDto;
use App\Dto\CustomerAddressDto;
use App\Dto\ImportOrderDto;
use App\Dto\OrderDto;
use App\Dto\ShippingAddressDto;
use App\Dto\TrackingDto;
use App\Enums\AccountSource;
use App\Enums\AddressType;
use App\Enums\OfferType;
use App\Enums\OrderStatus;
use App\Enums\PayloadSource;
use App\Enums\PayloadStatus;
use App\Enums\ProductCostType;
use App\Events\OrderTotalAmountCalculate;
use App\Exceptions\ImportOrderException;
use App\Models\Account;
use App\Models\AccountAffiliate;
use App\Models\Address;
use App\Models\Affiliate;
use App\Models\Customer;
use App\Models\Domain;
use App\Models\DomainProduct;
use App\Models\Offer;
use App\Models\Order;
use App\Models\OrderOffer;
use App\Models\Payload;
use App\Models\Product;
use App\Models\ProductCost;
use App\Models\Refund;
use App\Models\Tracking;
use App\Models\TrackingCategory;
use App\Services\AmazonSellerCentral\ASCService;
use App\Services\ImportService\Clients\ClickbankApi;
use App\Services\ImportService\Clients\DigistoreApi;
use App\Services\ImportService\Exceptions\OfferMissingException;
use App\Services\ImportService\Order\ImportOrderService;
use App\Services\ImportService\Strategies\DigistoreIpnStrategy;
use App\Services\PayloadService;
use App\Tests\TestCase;
use Carbon\Carbon;
use Cknow\Money\Money;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Notification;
use Mockery;
use Mockery\MockInterface;

class ImportOrderServiceTest extends TestCase
{
    use ExpectedDto;

    public function setUp(): void
    {
        parent::setUp();
        Config::set('services.konnektive.enabled', false);
    }

    public function testGetImportOrderDtoFromTransactionData()
    {
        Event::fake([OrderTotalAmountCalculate::class]);
        Config::set('services.hubspot.sync_enabled', false);
        $account = Account::factory()->create([
            'source'  => AccountSource::DIGISTORE24->value,
            'api_key' => 'api_key',
        ]);

        Offer::factory()->create(['account_id' => $account->id, 'source_ref' => 51629]);
        $service = new ImportOrderService(PayloadSource::DIGISTORE_API);
        $transaction = $this->getJsonFixture('digistore_transaction.json');
        $service->import($account, $transaction);
        $payload = $service->importStrategy->getPayload();
        $this->assertNotNull($payload);

        $customerDto = $this->expectedCustomerDto($account);
        $cDto = $customerDto->toArray(true);
        unset($cDto['account_id']);
        $actual = Customer::where($cDto)
            ->whereHas('accounts', function ($builder) use ($account) {
                $builder->where('accounts.id', '=', $account->id);
            })
            ->first();
        $this->assertNotNull($actual);
        $payload->load('customers');
        $this->assertCount(1, $payload->customers);

        $affiliateDto = $this->expectedAffiliateDto();
        $actual = Affiliate::where(['name' => $affiliateDto->name])->first();
        $this->assertNotNull($actual);
        $payload->load('affiliates');
        $this->assertCount(1, $payload->affiliates);


        $offerDto = $this->expectedOfferDto();
        $actual = Offer::where(['source_ref' => $offerDto->sourceRef])->first();
        $this->assertNotNull($actual);

        $orderDto = $this->expectedOrderDto();
        $actual = Order::where(['source_ref' => $orderDto->sourceRef])->first();
        $this->assertNotNull($actual);
        $payload->load('orders');
        $this->assertNotCount(0, $actual->meta);
        $this->assertCount(1, $payload->orders);

        Event::assertDispatched(OrderTotalAmountCalculate::class);
    }
    public function testSaveOrderWithFreeOfferData()
    {
        Event::fake([OrderTotalAmountCalculate::class]);
        Config::set('services.hubspot.sync_enabled', false);
        $account = Account::factory()->create([
            'source'  => AccountSource::DIGISTORE24->value,
            'api_key' => 'api_key',
        ]);

        Offer::factory()->create(['account_id' => $account->id, 'source_ref' => 51629]);
        $fOffer = Offer::factory()->create(['account_id' => $account->id, 'source_ref' => 11111]);
        $service = new ImportOrderService(PayloadSource::DIGISTORE_API);
        $transaction = $this->getJsonFixture('digistore_transaction.json');
        $item = $transaction['items'][0];
        $item['total_netto_amount'] = 0;
        $item['product_id'] = 11111;
        $transaction['items'][] = $item;
        $service->import($account, $transaction);
        $payload = $service->importStrategy->getPayload();
        $this->assertNotNull($payload);

        $customerDto = $this->expectedCustomerDto($account);
        $cDto = $customerDto->toArray(true);
        unset($cDto['account_id']);
        $actual = Customer::where($cDto)
            ->whereHas('accounts', function ($builder) use ($account) {
                $builder->where('accounts.id', '=', $account->id);
            })
            ->first();
        $this->assertNotNull($actual);
        $payload->load('customers');
        $this->assertCount(1, $payload->customers);

        $affiliateDto = $this->expectedAffiliateDto();
        $actual = Affiliate::where(['name' => $affiliateDto->name])->first();
        $this->assertNotNull($actual);
        $payload->load('affiliates');
        $this->assertCount(1, $payload->affiliates);

        $offerDto = $this->expectedOfferDto();
        $actual = Offer::where(['source_ref' => $offerDto->sourceRef])->first();
        $this->assertNotNull($actual);

        $orderDto = $this->expectedOrderDto();
        $actual = Order::where(['source_ref' => $orderDto->sourceRef])->first();
        $this->assertNotNull($actual);
        $payload->load('orders');
        $this->assertNotCount(0, $actual->meta);
        $this->assertCount(1, $payload->orders);
        $this->assertCount(2, $actual->orderOffers);


        $freeOffer = $actual->orderOffers()->where(['offer_id' => $fOffer->id])->first();
        $this->assertNotNull($actual);
        $this->assertEquals(Money::parse(0), $freeOffer->amount);

        Event::assertDispatched(OrderTotalAmountCalculate::class);
    }

    public function testDigistoreImportDefineProduct()
    {
        Event::fake([OrderTotalAmountCalculate::class]);

        $mock = $this->mock(
            DigistoreApi::class,
            function (MockInterface $mock) {
                $mock->shouldReceive('getOrders')->andReturn([]);
                $mock->shouldReceive('setApiKey');
            }
        );
        app()->instance(DigistoreApi::class, $mock);

        Config::set('services.hubspot.sync_enabled', false);
        $account = Account::factory()->create([
            'source'  => AccountSource::DIGISTORE24->value,
            'api_key' => 'api_key',
        ]);

        Offer::factory()->create(['account_id' => $account->id, 'source_ref' => 468134]);
        $service = new ImportOrderService(PayloadSource::DIGISTORE_IPN);
        $transaction = $this->getJsonFixture('digistore_payload.json');
        $service->import($account, $transaction);
        $payload = $service->importStrategy->getPayload();
        $this->assertNotNull($payload);
        $actual = Offer::where(['source_ref' => 468134])->first();
        $this->assertNotNull($actual);
        $this->assertEquals(1, $actual->quantity);
        Event::assertDispatched(OrderTotalAmountCalculate::class);
    }

    public function testDigistoreImportOfferNotFound()
    {
        Notification::fake();

        Config::set('services.hubspot.sync_enabled', false);
        $account = Account::factory()->create([
            'source'  => AccountSource::DIGISTORE24->value,
            'api_key' => 'api_key',
        ]);

        $mock = $this->mock(
            DigistoreApi::class,
            function (MockInterface $mock) {
                $mock->shouldReceive('getOrders')->andReturn([]);
                $mock->shouldReceive('setApiKey');
            }
        );
        app()->instance(DigistoreApi::class, $mock);

        /** @var DigistoreIpnStrategy $strategy */
        $strategy = app()->make(DigistoreIpnStrategy::class);
        $strategy->setAccount($account);
        $transaction = $this->getJsonFixture('digistore_payload_boostaro_6.json');
        $this->expectException(OfferMissingException::class);
        $importDto = $strategy->getImportOrderDto($transaction);
        $strategy->getOffer($account, $importDto->offers[0]);
    }

    public function testImportBuyGoodsStrategyOneOffer()
    {
        Event::fake([OrderTotalAmountCalculate::class]);

        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);

        $service = new ImportOrderService(PayloadSource::BUYGOODS_CSV);
        $service->importStrategy->setPayloadTitle('foo_bar_title');
        $service->import($account, $this->getPayload($account, 'buygoods_import_csv'));
        $payload = $service->importStrategy->getPayload();
        $this->assertNotNull($payload);
        $this->assertEquals('foo_bar_title', $payload->source);
        $this->assertEquals($this->getPayload($account), $payload->payload);
        $this->assertEquals(md5(json_encode($this->getPayload($account))), $payload->hash);

        $customerDto = $this->expectedCustomerDto($account, 'buygoods_import_csv');
        /** @var Customer $actual */
        $actual = Customer::where(['email' => $customerDto->email])->first();
        $this->assertNotNull($actual);
        $payload->load('customers');
        $this->assertCount(1, $payload->customers);
        $this->assertEquals($actual->id, $payload->customers()->first()->id);
        $this->assertEquals($actual->email, $customerDto->email);
        $this->assertEquals($actual->first_name, $customerDto->firstName);
        $this->assertEquals($actual->last_name, $customerDto->lastName);
        $this->assertEquals($actual->accounts[0]->id, $account->id);
        $this->assertEquals($actual->phone, '+***********');

        $actual = Address::where(['customer_id' => $actual->id])->get();
        $this->assertCount(1, $actual);
        $addressDto = $this->expectedShippingAddressDto();
        $actualAddress = $actual->first();
        $this->assertEquals($actualAddress->address, $addressDto->address);
        $this->assertEquals($actualAddress->city, $addressDto->city);
        $this->assertEquals($actualAddress->state, $addressDto->state);
        $this->assertEquals($actualAddress->postal_code, $addressDto->postalCode);
        $this->assertEquals($actualAddress->type, AddressType::SHIPPING->value);

        $affiliateDto = $this->expectedAffiliateDto('buygoods_import_csv');
        $actual = AccountAffiliate::where(['affiliate_source_ref' => $affiliateDto->sourceRef])->first();
        $this->assertNotNull($actual);
        $this->assertEquals($affiliateDto->name, $actual->affiliate->name);
        $payload->load('affiliates');
        $this->assertCount(1, $payload->affiliates);

        $orderDto = $this->expectedOrderDto('buygoods_import_csv');
        $actual = Order::where(['source_ref' => $orderDto->sourceRef])->first();
        $this->assertNotNull($actual);
        $payload->load('orders');
        $this->assertCount(1, $payload->orders);
        $this->assertEquals($orderDto->totalAmount, $actual->total_amount);
        $this->assertEquals($orderDto->status, $actual->status);

        $offerDto = $this->expectedOfferDto('buygoods_import_csv');
        $product = Product::where(['code' => $offerDto->productCode])->first();

        $actual = Offer::where(['account_id' => $account->id, 'source_ref' => $offerDto->sourceRef])->first();
        $this->assertNotNull($actual);
        $this->assertEquals($product->id, $actual->products()->first()->id);
        $this->assertEquals($offerDto->name, $actual->name);
        $payload->load('offers');
        $this->assertCount(1, $payload->offers);

        $actual = OrderOffer::where('offer_id', $actual->id)->first();
        $this->assertEquals($offerDto->quantity, $actual->quantity);
        Event::assertDispatched(OrderTotalAmountCalculate::class);
    }

    public function testImportBuyGoodsStrategyTwoOffers()
    {
        Event::fake([OrderTotalAmountCalculate::class]);

        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);

        $service = new ImportOrderService(PayloadSource::BUYGOODS_CSV);
        $service->importStrategy->setPayloadTitle('foo_bar_title');
        $payload = $this->getPayload($account, 'buygoods_import_csv');
        $payload['Product Codenames'] = '1_BS3_177 U_BS6_097 ';
        $payload['Product Names'] = "Boostaro - 3 Bottle Exclusive Limited Time Discount \n Boostaro - BONUS - 6 Bottle Exclusive Limited Time Discount";
        $payload['SKU'] = '4801-BLSGCSCP-104:3 4801-BLSGCSCP-104:6';
        $service->import($account, $payload);

        $od1 = $this->expectedOfferDto('buygoods_import_csv_1');
        $od2 = $this->expectedOfferDto('buygoods_import_csv_2');
        $product = Product::where(['code' => $od1->productCode])->first();
        $payload = $service->importStrategy->getPayload();
        $payload->load('offers');
        $this->assertCount(2, $payload->offers);

        $actualOD1 = Offer::where(['account_id' => $account->id, 'source_ref' => $od1->sourceRef])->first();
        $actualOD2 = Offer::where(['account_id' => $account->id, 'source_ref' => $od2->sourceRef])->first();
        $this->assertNotNull($actualOD1);
        $this->assertNotNull($actualOD2);

        $this->assertEquals($account->id, $actualOD1->account_id);
        $this->assertEquals($product->id, $actualOD1->products()->first()->id);
        $this->assertEquals($od1->name, $actualOD1->name);

        $this->assertEquals($account->id, $actualOD2->account_id);
        $this->assertEquals($product->id, $actualOD2->products()->first()->id);
        $this->assertEquals($od2->name, $actualOD2->name);

        $oo1 = OrderOffer::where('offer_id', $actualOD1->id)->first();
        $this->assertNotNull($oo1);
        $this->assertEquals($oo1->quantity, $actualOD1->quantity);
        $this->assertEquals($oo1->price, $actualOD1->amount);

        $oo2 = OrderOffer::where('offer_id', $actualOD2->id)->first();
        $this->assertNotNull($oo2);
        $this->assertEquals($oo2->quantity, $actualOD2->quantity);
        $this->assertEquals($oo2->price, $actualOD2->amount);
        Event::assertDispatched(OrderTotalAmountCalculate::class);
    }

    public function testImportBuyGoodsSecondTime()
    {
        Event::fake([OrderTotalAmountCalculate::class]);

        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);

        $service = new ImportOrderService(PayloadSource::BUYGOODS_CSV);
        $service->importStrategy->setPayloadTitle('foo_bar_title');
        $service->import($account, $this->getPayload($account, 'buygoods_import_csv'));
        $actual = Payload::where(['hash' => md5(json_encode($this->getPayload($account)))])->count();
        $this->assertEquals(1, $actual);

        $customerDto = $this->expectedCustomerDto($account, 'buygoods_import_csv');
        $actual = Customer::where(['email' => $customerDto->email])->count();
        $this->assertEquals(1, $actual);

        $actual = Address::where(
            [
                'customer_id' => Customer::where(['email' => $customerDto->email])->first()->id,
                'type'        => AddressType::SHIPPING->value,
            ]
        )->count();
        $this->assertEquals(1, $actual);

        $affiliateDto = $this->expectedAffiliateDto('buygoods_import_csv');
        $actual = AccountAffiliate::where(['affiliate_source_ref' => $affiliateDto->sourceRef])->count();
        $this->assertEquals(1, $actual);

        $orderDto = $this->expectedOrderDto('buygoods_import_csv');
        $actual = Order::where(['source_ref' => $orderDto->sourceRef])->count();
        $this->assertEquals(1, $actual);

        $offerDto = $this->expectedOfferDto('buygoods_import_csv');

        $actual = Offer::where(['account_id' => $account->id, 'source_ref' => $offerDto->sourceRef])->count();
        $this->assertEquals(1, $actual);
        Event::assertDispatched(OrderTotalAmountCalculate::class);
    }

    public function testImportBuyGoodsUpdateOrderStatus()
    {
        Event::fake([OrderTotalAmountCalculate::class]);

        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);

        $service = new ImportOrderService(PayloadSource::BUYGOODS_CSV);
        $service->importStrategy->setPayloadTitle('foo_bar_title');
        $data = $this->getPayload($account, 'buygoods_import_csv');
        $service->import($account, $data);
        $actual = Payload::where(['hash' => md5(json_encode($data))])->count();
        $this->assertEquals(1, $actual);

        $data['Status'] = 'Canceled';
        $service->import($account, $data);

        $orderDto = $this->expectedOrderDto('buygoods_import_csv');
        $actual = Order::where(['source_ref' => $orderDto->sourceRef])->first();
        $this->assertNotNull($actual);
        $this->assertEquals(OrderStatus::CANCELED->value, $actual->status);

        $actual = Payload::where(['hash' => md5(json_encode($data))])->count();
        $this->assertEquals(1, $actual);

        $payload = Payload::where(['hash' => md5(json_encode($data))])->first();
        $payload->load('orders');
        $this->assertCount(1, $payload->orders);
        Event::assertDispatched(OrderTotalAmountCalculate::class);
    }

    public function testImportBuyGoodsImportFile()
    {
        Event::fake([OrderTotalAmountCalculate::class]);

        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);

        $service = new ImportOrderService(PayloadSource::BUYGOODS_CSV);
        $service->importStrategy->setPayloadTitle('foo_bar_title');
        $filePath = $this->getFixturePath('buygoods_import_one_order.csv');
        $service->importFromFile($filePath, $account);
        $payload = $service->importStrategy->getPayload();
        $this->assertNotNull($payload);
        $this->assertEquals('foo_bar_title', $payload->source);
        $this->assertEquals($this->getPayload($account), $payload->payload);
        $this->assertEquals(md5(json_encode($this->getPayload($account))), $payload->hash);

        $customerDto = $this->expectedCustomerDto($account, 'buygoods_import_csv');
        /** @var Customer $actual */
        $actual = Customer::where(['email' => $customerDto->email])->first();
        $this->assertNotNull($actual);
        $payload->load('customers');
        $this->assertCount(1, $payload->customers);
        $this->assertEquals($actual->id, $payload->customers()->first()->id);
        $this->assertEquals($actual->email, $customerDto->email);
        $this->assertEquals($actual->first_name, $customerDto->firstName);
        $this->assertEquals($actual->last_name, $customerDto->lastName);
        $this->assertEquals($actual->accounts[0]->id, $account->id);
        $this->assertEquals($actual->phone, '+***********');

        $actual = Address::where(['customer_id' => $actual->id])->get();
        $this->assertCount(1, $actual);
        $addressDto = $this->expectedShippingAddressDto();
        $actualAddress = $actual->first();
        $this->assertEquals($actualAddress->address, $addressDto->address);
        $this->assertEquals($actualAddress->city, $addressDto->city);
        $this->assertEquals($actualAddress->state, $addressDto->state);
        $this->assertEquals($actualAddress->postal_code, $addressDto->postalCode);
        $this->assertEquals($actualAddress->type, AddressType::SHIPPING->value);

        $affiliateDto = $this->expectedAffiliateDto('buygoods_import_csv');
        $actual = AccountAffiliate::where(['affiliate_source_ref' => $affiliateDto->sourceRef])->first();
        $this->assertNotNull($actual);
        $this->assertEquals($affiliateDto->name, $actual->affiliate->name);
        $payload->load('affiliates');
        $this->assertCount(1, $payload->affiliates);

        $orderDto = $this->expectedOrderDto('buygoods_import_csv');
        $actual = Order::where(['source_ref' => $orderDto->sourceRef])->first();
        $this->assertNotNull($actual);
        $payload->load('orders');
        $this->assertCount(1, $payload->orders);
        $this->assertEquals($orderDto->totalAmount, $actual->total_amount);
        $this->assertEquals($orderDto->status, $actual->status);

        $offerDto = $this->expectedOfferDto('buygoods_import_csv');
        $product = Product::where(['code' => $offerDto->productCode])->first();

        $actual = Offer::where(['account_id' => $account->id, 'source_ref' => $offerDto->sourceRef])->first();
        $this->assertNotNull($actual);
        $this->assertEquals($product->id, $actual->products()->first()->id);
        $this->assertEquals($offerDto->name, $actual->name);
        $payload->load('offers');
        $this->assertCount(1, $payload->offers);

        $actual = OrderOffer::where('offer_id', $actual->id)->first();
        $this->assertEquals($offerDto->quantity, $actual->quantity);
        Event::assertDispatched(OrderTotalAmountCalculate::class);
    }

    public function testImportBuyGoodsSaveDomainProduct()
    {
        Event::fake([OrderTotalAmountCalculate::class]);

        $account = Account::factory()->create([
            'source' => AccountSource::BUYGOODS->value,
        ]);

        $offerDto = $this->expectedOfferDto('buygoods_import_csv');

        try {
            $product = Product::where(['code' => $offerDto->productCode])->firstOrFail();
        } catch (ModelNotFoundException $e) {
            $product = Product::factory()->create(['code' => $offerDto->productCode]);
        }

        $domain = Domain::factory()->create(['affiliate_id' => null, 'domain' => 'foo.com']);
        $actual = DomainProduct::where([
            'product_id' => $product->id,
            'domain_id'  => $domain->id,
        ])->first();
        $this->assertNull($actual);

        $service = new ImportOrderService(PayloadSource::BUYGOODS_CSV);
        $service->importStrategy->setPayloadTitle('foo_bar_title');
        $data = $this->getPayload($account, 'buygoods_import_csv');
        $data['Referrer URL'] = 'foo.com';
        $service->import($account, $data);

        $actual = DomainProduct::where([
            'product_id' => $product->id,
            'domain_id'  => $domain->id,
        ])->first();
        $this->assertNotNull($actual);
        Event::assertDispatched(OrderTotalAmountCalculate::class);
    }

    public function testGetImportOrderDtoFromEventData()
    {
        Config::set('services.hubspot.sync_enabled', false);
        $mock = $this->mock(
            DigistoreApi::class,
            function (MockInterface $mock) {
                $mock->shouldReceive('getOrders')->andReturn([]);
                $mock->shouldReceive('setApiKey');
            }
        );
        app()->instance(DigistoreApi::class, $mock);
        $account = Account::factory()->create([
            'source'  => AccountSource::DIGISTORE24->value,
            'api_key' => 'api_key',
        ]);
        $offer = Offer::factory()->create(['account_id' => $account->id, 'source_ref' => 468134]);

        $service = new ImportOrderService(PayloadSource::DIGISTORE_IPN);
        $service->import($account, $this->getJsonFixture('digistore_event_data.json'));
        $payload = $service->importStrategy->getPayload();
        $this->assertNotNull($payload);

        $customerDto = $this->expectedCustomerDto($account, 'event');
        $actual = Customer::where(['email' => $customerDto->email])->first();
        $this->assertNotNull($actual);
        $payload->load('customers');
        $this->assertCount(1, $payload->customers);

        $actual = Address::where(['customer_id' => $actual->id])->get();
        $this->assertCount(1, $actual);
        $addressDto = $this->expectedAddressDto('event');
        $actualAddress = $actual->first();
        $this->assertEquals($actualAddress->address, $addressDto->address);
        $this->assertEquals($actualAddress->city, $addressDto->city);
        $this->assertEquals($actualAddress->state, $addressDto->state);
        $this->assertEquals($actualAddress->postal_code, $addressDto->postalCode);

        $payload->load('affiliates');
        $this->assertCount(0, $payload->affiliates);

        $orderDto = $this->expectedOrderDto('event');
        $actual = Order::where(['source_ref' => $orderDto->sourceRef])->first();
        $this->assertNotNull($actual);
        $this->assertEquals(OrderStatus::ABANDONED->value, $actual->status);
        $this->assertNotEquals(0, $actual->total_amount->getAmount());
        $payload->load('orders');
        $this->assertCount(1, $payload->orders);
    }

    public function testBuygoodsIpnOrderAddAddress()
    {
        $shippingData = $this->getJsonFixture('bg_ipn_shipping_address.json');
        $billingData = $this->getJsonFixture('bg_ipn_billing_address.json');
        $shippingDto = new ShippingAddressDto($shippingData, PayloadSource::BUYGOODS_IPN->value);
        $billingDto = new CustomerAddressDto($billingData, PayloadSource::BUYGOODS_IPN->value);
        $orderData = $this->getJsonFixture('bg_ipn_without_address.json');
        $data = array_merge($orderData, $shippingData, $billingData);
        $account = Account::factory()->create(['source_ref' => $orderData['account_id']]);
        $product = Product::factory()->create(['code' => 'FOO']);
        $offer = Offer::factory()->create([
            'account_id' => $account->id,
            'source_ref' => $orderData['product_codename']
        ]);

        $offer->productAttach($product->id, 1);
        $service = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $service->import($account, $data);

        $actual = Order::where(['source_ref' => $data['order_id_global']])->first();
        $this->assertNotNull($actual);
        $customer = $actual->customer;
        /** @var Address $actualShipping */
        $actualShipping = $customer->shippingAddress;
        /** @var Address $actualBilling */
        $actualBilling = $customer->billingAddress;

        $this->assertNotNull($actualBilling);
        $this->assertNotNull($actualShipping);

        $exceptAddressFields = [
            'id',
            'address3',
            'full_address',
            'customer_id',
            'is_primary',
            'created_at',
            'updated_at',
            'deleted_at',
        ];

        $this->assertEquals(
            $billingDto->toArray(true),
            Arr::except($actualBilling->toArray(), $exceptAddressFields)
        );
        $this->assertEquals(
            $shippingDto->toArray(true),
            Arr::except($actualShipping->toArray(), $exceptAddressFields)
        );

        $this->assertEquals($actualBilling->id, $actual->billing_address_id);
        $this->assertEquals($actualShipping->id, $actual->shipping_address_id);
    }

    public function testBuygoodsIpnOrderCustomerAndAddressExists()
    {
        $shippingData = $this->getJsonFixture('bg_ipn_shipping_address.json');
        $billingData = $this->getJsonFixture('bg_ipn_billing_address.json');
        $shippingDto = new ShippingAddressDto($shippingData, PayloadSource::BUYGOODS_IPN->value);
        $billingDto = new CustomerAddressDto($billingData, PayloadSource::BUYGOODS_IPN->value);
        $orderData = $this->getJsonFixture('bg_ipn_without_address.json');
        $data = array_merge($orderData, $shippingData, $billingData);
        $account = Account::factory()->create(['source_ref' => $orderData['account_id']]);

        $customer = Customer::factory()->hasAccounts($account)->create([
            'email'      => $data['customer_emailaddress'],
            'first_name' => $data['customer_firstname'],
            'last_name'  => $data['customer_lastname'],
            'phone'      => $data['customer_phone'],
        ]);
        $billingAddress = Address::factory()->create(
            array_merge([
                'customer_id' => $customer->id,
            ], $billingDto->toArray(true))
        );

        $shippingAddress = Address::factory()->create(
            array_merge([
                'customer_id' => $customer->id,
            ], $shippingDto->toArray(true))
        );
        $product = Product::factory()->create(['code' => 'FOO']);
        $offer = Offer::factory()->create([
            'account_id' => $account->id,
            'source_ref' => $orderData['product_codename']
        ]);

        $offer->productAttach($product->id, 1);
        $service = new ImportOrderService(PayloadSource::BUYGOODS_IPN);
        $service->import($account, $data);

        $actual = Order::where(['source_ref' => $data['order_id_global']])->first();
        $this->assertNotNull($actual);
        $actualCustomer = $actual->customer;

        $this->assertEquals($customer->id, $actualCustomer->id);

        $this->assertEquals($billingAddress->id, $actual->billing_address_id);
        $this->assertEquals($shippingAddress->id, $actual->shipping_address_id);

        $this->assertCount(2, $customer->addresses);
    }

    public function testImportClickbankInsSaleOrder()
    {
        $account = Account::factory()->create(['source_ref' => 'acc-foo']);
        $offer = Offer::factory()->create([
            'account_id' => $account->id,
            'source_ref' => '1-FS3-147',
            'price'      => Money::USD('147.00'),
            'quantity'   => 3,
        ]);
        $product = Product::factory()->create(['default_price' => Money::parse(0)]);
        ProductCost::factory()->create([
            'product_id' => $product->id,
            'type' => ProductCostType::COG->value,
            'amount' => Money::parse('4.48'),
        ]);
        ProductCost::factory()->create([
            'product_id' => $product->id,
            'type' => ProductCostType::AVG_SHIPPING->value,
            'amount' => Money::parse('1.05'),
        ]);
        $offer->productAttach($product->id, 1);

        $parentOrder = Order::factory()->create([
            'account_id' => $account->id,
            'source_ref' => 'FOO_PARENT',
        ]);
        $data = $this->getJsonFixture('cb_ins_sale.json');
        $importService = new ImportOrderService(PayloadSource::CLICKBANK_INS);
        $importService->import($account, $data);
        $data['attemptCount'] = 2;
        $importService->import($account, $data);
        $payloads = Payload::where([
            'source' => PayloadSource::CLICKBANK_INS->value,
            'source_ref' => 'FOOBAR'
        ])->get();
        $this->assertCount(1, $payloads);
        $actual = $payloads->first();
        $this->assertNotNull($actual);
        $this->assertCount(1, $actual->customers);
        $this->assertCount(1, $actual->orders);
        $this->assertCount(1, $actual->addresses);
        unset($data['attemptCount']);
        $this->assertEquals($data, $actual->payload);

        $this->assertEquals(PayloadStatus::SUCCESS->value, $actual->status);
        $order = Order::where(['source_ref' => 'FOOBAR', 'account_id' => $account->id])->first();
        $this->assertNotNull($order);
        $this->assertEquals(Money::USD('157.29'), $order->total_amount);
        $this->assertEquals(Money::USD('10.29'), $order->tax_amount);
        $this->assertEquals(Money::USD('12.80'), $order->merchant_fee);
        $this->assertEquals($parentOrder->id, $order->parent_order_id);

        $customer = $order->customer;
        $this->assertEquals('John', $customer->first_name);
        $this->assertEquals('Doe', $customer->last_name);
        $this->assertEquals('+***********', $customer->phone);
        $address = $customer->addresses;
        $this->assertCount(1, $address);
        $shippingAddress = $address->first();
        $this->assertEquals(AddressType::SHIPPING->value, $shippingAddress->type);
        $this->assertEquals("48 Fake Address", $shippingAddress->address);
        $this->assertEquals("Fake City", $shippingAddress->city);
        $this->assertEquals("FL", $shippingAddress->state);
        $this->assertEquals("11111", $shippingAddress->postal_code);
    }

    public function testImportClickbankApiSaleOrder()
    {
        $account = Account::factory()->create(['source_ref' => 'acc-foo']);
        $parentOrder = Order::factory()->create([
            'account_id' => $account->id,
            'source_ref' => 'FOO_PARENT',
        ]);

        $offer = Offer::factory()->create([
            'account_id' => $account->id,
            'source_ref' => '2-FS9-171',
            'price'      => Money::USD('171.00'),
            'quantity'   => 9,
        ]);
        $product = Product::factory()->create(['default_price' => Money::parse(0)]);
        ProductCost::factory()->create([
            'product_id' => $product->id,
            'type' => ProductCostType::COG->value,
            'amount' => Money::parse('4.48'),
        ]);
        ProductCost::factory()->create([
            'product_id' => $product->id,
            'type' => ProductCostType::AVG_SHIPPING->value,
            'amount' => Money::parse('1.05'),
        ]);
        $offer->productAttach($product->id, 1);
        $data = $this->getJsonFixture('cb_api_sale.json');
        $importService = new ImportOrderService(PayloadSource::CLICKBANK_ORDERS_API);
        $importService->import($account, $data);
        $actual = Payload::where([
            'source'     => PayloadSource::CLICKBANK_ORDERS_API->value,
            'source_ref' => 'FOOBAR',
        ])->first();
        $this->assertNotNull($actual);
        $this->assertEquals($data, $actual->payload);
        $this->assertEquals(PayloadStatus::SUCCESS->value, $actual->status);
        $order = Order::where(['source_ref' => 'FOOBAR', 'account_id' => $account->id])->first();
        $this->assertNotNull($order);
        $this->assertEquals(Money::USD('182.54'), $order->total_amount);
        $this->assertEquals(Money::USD('11.54'), $order->tax_amount);
        $this->assertEquals(Money::USD('14.69'), $order->merchant_fee);
        $this->assertEquals($parentOrder->id, $order->parent_order_id);

        $customer = $order->customer;
        $this->assertEquals('John', $customer->first_name);
        $this->assertEquals('Doe', $customer->last_name);
        $this->assertEquals(null, $customer->phone);
        $address = $customer->addresses;
        $this->assertCount(2, $address);

        $shippingAddress = $customer->addresses()->where(['type' => AddressType::SHIPPING->value])->first();
        $this->assertEquals(AddressType::SHIPPING->value, $shippingAddress->type);
        $this->assertEquals("48 Fake Address", $shippingAddress->address);
        $this->assertEquals("Fake City", $shippingAddress->city);
        $this->assertEquals("NC", $shippingAddress->state);
        $this->assertEquals("11111", $shippingAddress->postal_code);

        $address = $customer->addresses()->where(['type' => AddressType::BILLING->value])->first();
        $this->assertEquals(AddressType::BILLING->value, $address->type);
        $this->assertEquals("48 Fake Address", $address->address);
        $this->assertEquals("Fake City", $address->city);
        $this->assertEquals("NC", $address->state);
        $this->assertEquals("11111", $address->postal_code);
    }

    public function testImportClickbankApiRefundOrder()
    {
        $mock = Mockery::mock(ClickbankApi::class, function (MockInterface $mock) {
            $mock->shouldReceive('setApiKey');
            $mock->shouldReceive('getOrdersByReceipt')->andReturn([
                'transactionTime' => "2024-03-13T01:47:17-07:00",
            ]);
        });
        app()->instance(ClickbankApi::class, $mock);

        $account = Account::factory()->create(['source_ref' => 'acc-foo', 'api_key' => 'key']);
        $offer = Offer::factory()->create([
            'account_id' => $account->id,
            'source_ref' => '4-BF6-049',
            'price'      => Money::USD('49.00'),
            'quantity'   => 9,
        ]);
        $product = Product::factory()->create(['code' => 'FOO', 'default_price'     => Money::parse(0)]);
        ProductCost::factory()->create([
            'product_id' => $product->id,
            'type' => ProductCostType::COG->value,
            'amount' => Money::parse('4.48'),
        ]);
        ProductCost::factory()->create([
            'product_id' => $product->id,
            'type' => ProductCostType::AVG_SHIPPING->value,
            'amount' => Money::parse('1.05'),
        ]);
        $offer->productAttach($product->id, 1);
        $data = $this->getJsonFixture('cb_api_sale_2.json');
        $importService = new ImportOrderService(PayloadSource::CLICKBANK_ORDERS_API);
        $importService->import($account, $data);
        $dataRfnd = $this->getJsonFixture('cb_api_rfnd.json');
        $importService->import($account, $dataRfnd);

        $order = Order::where(['source_ref' => 'FOOBAR', 'account_id' => $account->id])->first();
        $this->assertNotNull($order);
        $this->assertEquals(OrderStatus::REFUNDED->value, $order->status);
        $this->assertEquals(Money::parse('51.94'), $order->total_amount);
        $this->assertEquals(Money::parse('51.94'), $order->refunded_amount);
        $this->assertNotNull($order->refunded_at);

        $refund = Refund::where(['order_id' => $order->id])->first();
        $this->assertNotNull($refund);
        $this->assertEquals(Money::parse('51.94'), $refund->amount);
    }

    public function testUpdateOrderMethodUpdatePurchasedAt()
    {
        $account = Account::factory()->create(['source_ref' => 'acc-foo']);
        $order = Order::factory()->create([
            'account_id'          => $account,
            'purchased_at'        => Carbon::now()->subDays(2),
            'source_purchased_at' => Carbon::now()->subDays(2)->tz(DigistoreIpnStrategy::SOURCE_TIMEZONE),
        ]);
        $order->refresh();

        $dto = new OrderDto([
            'merchantFee'         => Money::parse('1'),
            'totalAmount'         => Money::parse('200'),
            'affiliateCommission' => Money::parse('0'),
            'refundedAt'          => '2024-01-01 08:00:00',
            'sourceRefundedAt'    => '2024-01-01 01:00:00',
            'refundedAmount'      => Money::USD('200.00'),
            'purchasedAt'         => Carbon::now()->subDays(),
            'sourcePurchasedAt'   => Carbon::now()->subDays()->tz(DigistoreIpnStrategy::SOURCE_TIMEZONE),
        ]);

        $importOrderDto = new ImportOrderDto(['order' => $dto]);

        $importService = new ImportOrderService(PayloadSource::DIGISTORE_IPN);
        $importService->importStrategy->updateOrder($order, $importOrderDto);
        $order->refresh();
        $this->assertEquals(Carbon::now()->subDays(), $order->purchased_at);
        $expected = Carbon::now()->subDays()->tz(DigistoreIpnStrategy::SOURCE_TIMEZONE);
        $this->assertEquals($expected, $order->source_purchased_at);
    }

    public function testUpdateOrderMethodUpdatePurchasedAtNull()
    {
        $account = Account::factory()->create(['source_ref' => 'acc-foo']);
        $order = Order::factory()->create([
            'account_id'          => $account,
            'purchased_at'        => Carbon::now()->subDays(2),
            'source_purchased_at' => Carbon::now()->subDays(2)->tz(DigistoreIpnStrategy::SOURCE_TIMEZONE),
        ]);
        $order->refresh();

        $dto = new OrderDto([
            'merchantFee'         => Money::parse('1'),
            'totalAmount'         => Money::parse('200'),
            'affiliateCommission' => Money::parse('0'),
            'refundedAt'          => '2024-01-01 08:00:00',
            'sourceRefundedAt'    => '2024-01-01 01:00:00',
            'refundedAmount'      => Money::USD('200.00'),
        ]);

        $importOrderDto = new ImportOrderDto(['order' => $dto]);

        $importService = new ImportOrderService(PayloadSource::DIGISTORE_IPN);
        $importService->importStrategy->updateOrder($order, $importOrderDto);
        $order->refresh();
        $this->assertEquals(Carbon::now()->subDays(2), $order->purchased_at);
        $expected = Carbon::now()->subDays(2)->tz(DigistoreIpnStrategy::SOURCE_TIMEZONE);
        $this->assertEquals($expected, $order->source_purchased_at);
    }

    public function testUpdateOrderStatusMethod()
    {
        $order = Order::factory()->create(['status' => OrderStatus::PENDING_FULFILLMENT->value]);
        $importService = new ImportOrderService(PayloadSource::DIGISTORE_IPN);
        $importService->updateOrderStatus($order, new OrderDto());
        $this->assertEquals(OrderStatus::PENDING_FULFILLMENT->value, $order->status);

        $importService->updateOrderStatus($order, new OrderDto(['status' => OrderStatus::CHARGEBACK->value]));
        $this->assertEquals(OrderStatus::CHARGEBACK->value, $order->status);

        $importService->updateOrderStatus($order, new OrderDto(['status' => OrderStatus::CANCELED->value]));
        $this->assertEquals(OrderStatus::CHARGEBACK->value, $order->status);
    }

    public function testGetAffiliateMethod()
    {
        $data = new AffiliateDto(['sourceRef' => '468134', 'name' => 'test name']);

        $account = Account::factory()->create(
            [
                'source' => AccountSource::DIGISTORE24->value,
                'api_key' => 'api_key',
            ]
        );
        Offer::factory()->create(['account_id' => $account->id, 'source_ref' => 468134]);
        $service = new ImportOrderService(PayloadSource::DIGISTORE_IPN->value);
        $service->import($account, $this->getJsonFixture('digistore_event_data.json'));
        $affiliate = $service->getAffiliate($data);
        $this->assertEquals($affiliate->accounts()->count(), 1);

        $affiliate = $service->getAffiliate($data);
        $this->assertEquals($affiliate->accounts()->count(), 1);
    }

    public function testGetAffiliateMethodDublicateSourceRef()
    {
        $data = new AffiliateDto(['sourceRef' => '111111', 'name' => 'test name']);

        $acc1 = Account::factory()->create(['source' => AccountSource::DIGISTORE24->value, 'api_key' => 'api_key']);
        $acc2 = Account::factory()->create(['source' => AccountSource::DIGISTORE24->value, 'api_key' => 'api_key']);

        $service = new ImportOrderService(PayloadSource::DIGISTORE_IPN->value);
        $service->account = $acc1;
        $service->setPayload(Payload::factory()->create());
        $affiliate = $service->getAffiliate($data);
        $this->assertEquals($affiliate->accounts()->count(), 1);

        $affiliate = $service->getAffiliate($data);
        $this->assertEquals($affiliate->accounts()->count(), 1);

        $service->account = $acc2;
        $affiliate = $service->getAffiliate($data);
        $this->assertEquals($affiliate->accounts()->count(), 2);
    }
    public function testGetAffiliateExistingSourceRef()
    {
        $data = new AffiliateDto(['sourceRef' => '111111', 'name' => 'bar']);
        $affiliate = Affiliate::factory()->create(['name' => 'foo']);
        $affiliate->accounts()->sync([]);
        $acc1 = Account::factory()->create(['source' => AccountSource::DIGISTORE24->value, 'api_key' => 'api_key']);
        AccountAffiliate::factory()->create([
            'account_id' => $acc1->id,
            'affiliate_id' => $affiliate->id,
            'affiliate_source_ref' => '111111'
        ]);
        $service = new ImportOrderService(PayloadSource::DIGISTORE_IPN->value);
        $service->account = $acc1;
        $service->setPayload(Payload::factory()->create());
        $actual = $service->getAffiliate($data);
        $this->assertEquals($actual->id, $affiliate->id);
        $this->assertEquals('bar', $actual->name);
        $this->assertEquals(1, $actual->accounts()->count());
    }

    public function testMissingOffer()
    {
        Notification::fake();

        Config::set('services.hubspot.sync_enabled', false);
        $account = Account::factory()->create([
            'source'  => AccountSource::DIGISTORE24->value,
            'api_key' => 'api_key',
        ]);

        $mock = $this->mock(
            DigistoreApi::class,
            function (MockInterface $mock) {
                $mock->shouldReceive('getOrders')->andReturn([]);
                $mock->shouldReceive('setApiKey');
            }
        );
        app()->instance(DigistoreApi::class, $mock);

        $service = new ImportOrderService(PayloadSource::DIGISTORE_IPN);
        $transaction = $this->getJsonFixture('digistore_payload_boostaro_6.json');
        $actual = $service->import($account, $transaction);
        $this->assertNotNull($actual);
        $payload = $actual->payloads->first();
        $this->assertEquals($payload->source_ref, $actual->source_ref);
        $this->assertEquals(PayloadStatus::DELAYED->value, $payload->status);
        $this->assertEquals(now()->addHours(8), $payload->retry_at);
    }

    public function testGetDeletedAffiliate()
    {
        $account = Account::factory()->create();
        $affiliateRemoved = Affiliate::factory()->create();
        AccountAffiliate::factory()->create([
            'account_id' => $account->id,
            'affiliate_id' => $affiliateRemoved->id,
            'affiliate_source_ref' => 'foo',
        ]);
        $affiliate = Affiliate::factory()->create();
        AccountAffiliate::factory()->create([
            'account_id' => $account->id,
            'affiliate_id' => $affiliate->id,
            'affiliate_source_ref' => 'foo',
        ]);
        $affiliateRemoved->delete();
        $service = new ImportOrderService(PayloadSource::DIGISTORE_IPN);
        $service->account = $account;
        $dto = new AffiliateDto(['name' => 'Foo', 'source_ref' => 'foo']);
        $actual = $service->getAffiliate($dto);
        $this->assertNotNull($affiliate->id, $actual->id);
    }
    public function testSaveTrackingDataWithoutTrackingCategory()
    {
        $account = Account::factory()->create(['source' => AccountSource::DIGISTORE24->value]);
        $order = Order::factory()->create(['account_id' => $account->id]);
        $dto = new TrackingDto(['cid' => 'tracking-id-1'], $order->account->source);
        $service = new ImportOrderService(PayloadSource::CLICKBANK_INS);
        $service->saveTrackingData($order, $dto);
        $actual = Tracking::where(['order_id' => $order->id])->first();
        $this->assertNotNull($actual);
        $this->assertNull($actual->tracking_category_id);
    }
    public function testSaveTrackingDataWithTrackingCategory()
    {
        $account = Account::factory()->create(['source' => AccountSource::DIGISTORE24->value]);
        $order = Order::factory()->create(['account_id' => $account->id]);
        $dto = new TrackingDto(['cid' => 'tracking-id-1'], $order->account->source);
        $trackingCategory = TrackingCategory::factory()->create(['key' => 'cid']);
        $service = new ImportOrderService(PayloadSource::CLICKBANK_INS);
        $service->saveTrackingData($order, $dto);
        $actual = Tracking::where(['order_id' => $order->id])->first();
        $this->assertNotNull($actual);
        $this->assertEquals($trackingCategory->id, $actual->tracking_category_id);
    }

    public function testAttachCorrectAddressToOrder()
    {
        Event::fake([OrderTotalAmountCalculate::class]);

        $mock = $this->mock(
            DigistoreApi::class,
            function (MockInterface $mock) {
                $mock->shouldReceive('getOrders')->andReturn([]);
                $mock->shouldReceive('setApiKey');
            }
        );
        app()->instance(DigistoreApi::class, $mock);

        Config::set('services.hubspot.sync_enabled', false);
        $account = Account::factory()->create([
                                                  'source'  => AccountSource::DIGISTORE24->value,
                                                  'api_key' => 'api_key',
                                              ]);
        $customer = Customer::factory()->create(['email' => '<EMAIL>']);
        $shippingAddress = Address::factory()->create(['customer_id' => $customer->id, 'type' => AddressType::SHIPPING->value]);
        $billingAddress = Address::factory()->create(['customer_id' => $customer->id, 'type' => AddressType::BILLING->value]);
        $this->assertCount(2, $customer->addresses);

        Offer::factory()->create(['account_id' => $account->id, 'source_ref' => 468134]);
        $service = new ImportOrderService(PayloadSource::DIGISTORE_IPN);
        $transaction = $this->getJsonFixture('digistore_payload.json');
        $service->import($account, $transaction);
        $actual = Order::where(['source_ref' => $transaction['order_id']])->first();
        $this->assertNotNull($actual);
        $this->assertEquals($customer->id, $actual->customer_id);
        $customer->refresh();
        $this->assertCount(4, $customer->addresses);
    }
    public function testUpdateShippingAddressWhenEmpty()
    {
        Event::fake([OrderTotalAmountCalculate::class]);

        $mock = $this->mock(
            DigistoreApi::class,
            function (MockInterface $mock) {
                $mock->shouldReceive('getOrders')->andReturn([]);
                $mock->shouldReceive('setApiKey');
            }
        );
        app()->instance(DigistoreApi::class, $mock);

        Config::set('services.hubspot.sync_enabled', false);
        $account = Account::factory()->create([
                                                  'source'  => AccountSource::DIGISTORE24->value,
                                                  'api_key' => 'api_key',
                                              ]);
        $customer = Customer::factory()->create(['email' => '<EMAIL>']);
        $shippingAddress = Address::factory()->create([
            'customer_id' => $customer->id,
            'type' => AddressType::SHIPPING->value,
            'state' => 'TX',
            'city' => 'Fake City',
            'postal_code' => '12345',
            'address' => ''
        ]);
        $this->assertCount(1, $customer->addresses);
        $this->assertNotNull($shippingAddress->address);

        Offer::factory()->create(['account_id' => $account->id, 'source_ref' => 468134]);
        $service = new ImportOrderService(PayloadSource::DIGISTORE_IPN);
        $transaction = $this->getJsonFixture('digistore_payload.json');
        $service->import($account, $transaction);
        $actual = Order::where(['source_ref' => $transaction['order_id']])->first();
        $this->assertNotNull($actual);
        $this->assertEquals($customer->id, $actual->customer_id);
        $shippingAddress->refresh();
        $this->assertEquals('999 Main Street', $shippingAddress->address);
    }

    public function testReorderAtDateForSingleOrder()
    {
        $order = Order::factory()->create([
            'purchased_at' => Carbon::now()
        ]);
        $offer = Offer::factory()->create(['quantity' => 2]);
        OrderOffer::factory()->create(['order_id' => $order->id, 'offer_id' => $offer->id]);
        $product = Product::factory()->create();

        $offer->productAttach($product->id, 1, true);
        $service = new ImportOrderService(PayloadSource::DIGISTORE_IPN);
        $result = $service->getReorderAtDate($order, $offer, 3);

        $expectedDate = $order->purchased_at->addDays(2 * 3 * 30);
        $this->assertEquals($expectedDate, $result);
    }

    public function testReorderAtDateForParentOrderWithUpsell()
    {
        $parent = Order::factory()->create([
            'purchased_at' => Carbon::now()->subMinutes(5)
        ]);
        $offer = Offer::factory()->create(['quantity' => 2]);
        $parentOrderOffer = OrderOffer::factory()->create(['order_id' => $parent->id, 'offer_id' => $offer->id]);
        $product = Product::factory()->create();
        $product2 = Product::factory()->create();
        $offer->productAttach($product->id, 1, true);

        $offerUpsell = Offer::factory()->create(['quantity' => 6]);
        $offerUpsell->productAttach($product->id, 1, true);
        $offer2 = Offer::factory()->create(['quantity' => 1]);
        $offer2->productAttach($product2->id, 1, true);

        $upsellOrder = Order::factory()->create([
            'parent_order_id' => $parent->id,
            'purchased_at' => Carbon::now()
        ]);

        OrderOffer::factory()->create(['order_id' => $upsellOrder->id, 'offer_id' => $offerUpsell->id]);
        OrderOffer::factory()->create(['order_id' => $upsellOrder->id, 'offer_id' => $offer2->id]);

        $service = new ImportOrderService(PayloadSource::DIGISTORE_IPN);

        $result = $service->getReorderAtDate($upsellOrder, $offerUpsell, 1);
        $this->assertNull($result);

        $result = $service->getReorderAtDate($upsellOrder, $offer2, 1);
        $this->assertEquals(Carbon::now()->addDays(30), $result);

        $parentOrderOffer->refresh();
        $this->assertEquals(Carbon::now()->subMinutes(5)->addDays(8 * 30), $parentOrderOffer->re_order_at);
    }

    public function testReorderAtDateForSingleOrderWithTwoOrderOffer()
    {
        $order = Order::factory()->create([
            'purchased_at' => Carbon::now()
        ]);
        $offer = Offer::factory()->create(['quantity' => 2, 'type' => OfferType::INITIAL->value]);
        $oo1 = OrderOffer::factory()->create(['order_id' => $order->id, 'offer_id' => $offer->id]);
        $offer2 = Offer::factory()->create(['quantity' => 1, 'type' => OfferType::UPSELL->value]);
        $oo2 =OrderOffer::factory()->create(['order_id' => $order->id, 'offer_id' => $offer2->id]);
        $product = Product::factory()->create();
        $offer->productAttach($product->id, 1, true);
        $offer2->productAttach($product->id, 1, true);
        $service = new ImportOrderService(PayloadSource::DIGISTORE_IPN);
        $service->setReOrderAt($order);
        $oo1->refresh();
        $oo2->refresh();
        $expectedDate = now()->addDays(3 * 30);
        $this->assertEquals($expectedDate, $oo1->re_order_at);
        $this->assertNull($oo2->re_order_at);
    }
}
