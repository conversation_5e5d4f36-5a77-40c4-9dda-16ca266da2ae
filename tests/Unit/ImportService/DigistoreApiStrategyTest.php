<?php

namespace App\Tests\Unit\ImportService;

use App\Dto\ImportOrderDto;
use App\Enums\AccountSource;
use App\Enums\OrderStatus;
use App\Enums\PayloadSource;
use App\Enums\PayloadStatus;
use App\Models\Account;
use App\Models\ChargebackFee;
use App\Models\Offer;
use App\Models\Order;
use App\Models\Payload;
use App\Models\Product;
use App\Models\Refund;
use App\Models\Transaction;
use App\Services\ImportService\Order\ImportOrderService;
use App\Services\ImportService\Strategies\DigistoreApiStrategy;
use App\Tests\TestCase;
use Cknow\Money\Money;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Config;

class DigistoreApiStrategyTest extends TestCase
{
    use ExpectedDto;

    public function setUp(): void
    {
        parent::setUp();
        Config::set('services.konnektive.enabled', false);
    }

    public function testGetImportOrderDto()
    {
        $account = Account::factory()->create([
            'source'  => AccountSource::DIGISTORE24->value,
            'api_key' => 'api_key',
        ]);

        $strategy = new DigistoreApiStrategy();
        $strategy->setAccount($account);
        Product::factory()->create(['code' => '51629']);

        $transaction = $this->getJsonFixture('digistore_transaction.json');
        $actual = $strategy->getImportOrderDto($transaction);

        $expected = new ImportOrderDto([
            'order'           => $this->expectedOrderDto(),
            'customer'        => $this->expectedCustomerDto($account),
            'affiliate'       => $this->expectedAffiliateDto(),
            'meta'            => $this->expectedMetaDto(AccountSource::DIGISTORE24->value),
            'tracking'        => $this->expectedTrackingDto(AccountSource::DIGISTORE24->value),
            'offers'          => [$this->expectedOfferDto()],
            'transactionType' => 'payment',
            'transactionId'   => $transaction['transaction_id'],
        ]);

        $this->assertEquals($expected->affiliate, $actual->affiliate, 'Affiliate dto');
        $this->assertEquals($expected->customer, $actual->customer, 'Customer dto');
        $this->assertEquals($expected->order, $actual->order, 'Order dto');
        $this->assertEquals($expected->meta, $actual->meta, 'Meta dto');
        $this->assertEquals($expected->tracking, $actual->tracking, 'Tracking dto');
        $this->assertEquals($expected->offers, $actual->offers, 'Offers dto');
        $this->assertEquals($expected, $actual);

        $payload = Payload::query()->where('hash', $strategy->getPayload()->hash)->first();
        $this->assertNotNull($payload);
        $this->assertEquals(
            $strategy->getPayload()->toArray(),
            Arr::except($payload->toArray(), ['response', 'error', 'replayed_at', 'retry_attempts', 'retry_at'])
        );
    }

    public function testImportRefundOrder()
    {
        $account = Account::factory()->create([
            'source'  => AccountSource::DIGISTORE24->value,
            'api_key' => 'api_key',
        ]);

        Offer::factory()->create(['account_id' => $account->id, 'source_ref' => '535156']);
        $data = $this->getJsonFixture('api_payment_1.json');
        $importService = new ImportOrderService(PayloadSource::DIGISTORE_API);
        $importService->import($account, $data);
        $actual = Order::where(['account_id' => $account->id, 'source_ref' => 'FOOBAR'])->first();
        $this->assertNotNull($actual);
        $this->assertEquals(OrderStatus::COMPLETED->value, $actual->status);
        $this->assertCount(1, $actual->payloads);
        $actualTransaction = Transaction::where(['order_id' => $actual->id])->first();
        $this->assertNotNull($actualTransaction);
        $this->assertEquals($data['transaction_id'], $actualTransaction->source_ref);
        $payload = $actual->payloads->last();
        $this->assertEquals(PayloadStatus::SUCCESS->value, $payload->status);

        $data['rebill_stop_info']['icon_html'] = 'foo-bar';

        //check we will not save doublicated payloads
        $importService->import($account, $data);
        $actual = Order::where(['account_id' => $account->id, 'source_ref' => 'FOOBAR'])->first();
        $this->assertCount(1, $actual->payloads);
        $payload = $actual->payloads->last();
        $this->assertEquals(PayloadStatus::SUCCESS->value, $payload->status);

        $data = $this->getJsonFixture('api_refund_1.json');
        $importService->import($account, $data);
        $actual = Order::where(['account_id' => $account->id, 'source_ref' => 'FOOBAR'])->first();
        $this->assertEquals(OrderStatus::REFUNDED->value, $actual->status);
        $this->assertCount(2, $actual->payloads);

        $refund = Refund::where(['order_id' => $actual->id])->first();
        $actualTransaction = Transaction::where(['order_id' => $actual->id, 'refund_id' => $refund->id])->first();
        $this->assertNotNull($actualTransaction);
        $this->assertEquals($data['transaction_id'], $actualTransaction->source_ref);

        $payload = $actual->payloads->last();
        $this->assertEquals(PayloadStatus::SUCCESS->value, $payload->status);
    }

    public function testImportChargebackOrder()
    {
        $account = Account::factory()->create([
            'source'  => AccountSource::DIGISTORE24->value,
            'api_key' => 'api_key',
        ]);

        Offer::factory()->create(['account_id' => $account->id, 'source_ref' => '535156']);
        $data = $this->getJsonFixture('api_payment_1.json');
        $importService = new ImportOrderService(PayloadSource::DIGISTORE_API);
        $importService->import($account, $data);
        $actual = Order::where(['account_id' => $account->id, 'source_ref' => 'FOOBAR'])->first();
        $this->assertEquals(OrderStatus::COMPLETED->value, $actual->status);
        $this->assertCount(1, $actual->payloads);
        $payload = $actual->payloads->last();
        $this->assertEquals(PayloadStatus::SUCCESS->value, $payload->status);

        $data['rebill_stop_info']['icon_html'] = 'foo-bar';

        $data = $this->getJsonFixture('api_refund_1.json');
        $data['transaction_type'] = 'chargeback';
        $importService->import($account, $data);
        $actual = Order::where(['account_id' => $account->id, 'source_ref' => 'FOOBAR'])->first();
        $this->assertEquals(OrderStatus::CHARGEBACK->value, $actual->status);
        $this->assertCount(2, $actual->payloads);

        $cgbk = ChargebackFee::where(['order_id' => $actual->id])->first();
        $actualTransaction = Transaction::where(['order_id' => $actual->id, 'chargeback_fee_id' => $cgbk->id])->first();
        $this->assertNotNull($actualTransaction);
        $this->assertEquals($data['transaction_id'], $actualTransaction->source_ref);

        $payload = $actual->payloads->last();
        $this->assertEquals(PayloadStatus::SUCCESS->value, $payload->status);
    }

    public function testImportOrderWithFreeItem()
    {
        $account = Account::factory()->create([
            'source'  => AccountSource::DIGISTORE24->value,
            'api_key' => 'api_key',
        ]);

        $offer = Offer::factory()->create([
            'account_id' => $account->id,
            'source_ref' => '51629',
            'price' => Money::parse('100')
        ]);
        $product = Product::factory()->create(['code' => '51629']);
        $offer->productAttach($product->id, 1);
        $transaction = $this->getJsonFixture('digistore_transaction.json');
        $freeItem = $transaction['items'][0];
        $freeItem['total_netto_amount'] = 0;
        $transaction['items'][] = $freeItem;
        $importService = new ImportOrderService(PayloadSource::DIGISTORE_API);
        $importService->import($account, $transaction);
        $actual = Order::where(['source_ref' => 'p-foo-id'])->first();
        $this->assertNotNull($actual);
        $this->assertCount(2, $actual->orderOffers);
        $first = $actual->orderOffers->first();
        $last = $actual->orderOffers->last();
        $this->assertEquals(Money::parse('78.99'), $first->amount);
        $this->assertEquals(Money::parse('0'), $last->amount);
    }

    public function testImportOrderWithItemWithoutPrice()
    {
        $account = Account::factory()->create([
            'source'  => AccountSource::DIGISTORE24->value,
            'api_key' => 'api_key',
        ]);

        $offer = Offer::factory()->create([
            'account_id' => $account->id,
            'source_ref' => '51629',
            'price' => Money::parse('100')
        ]);
        $product = Product::factory()->create(['code' => '51629']);
        $offer->productAttach($product->id, 1);
        $transaction = $this->getJsonFixture('digistore_transaction.json');
        $freeItem = $transaction['items'][0];
        $freeItem['total_netto_amount'] = null;
        $transaction['items'][] = $freeItem;
        $importService = new ImportOrderService(PayloadSource::DIGISTORE_API);
        $importService->import($account, $transaction);
        $actual = Order::where(['source_ref' => 'p-foo-id'])->first();
        $this->assertNotNull($actual);
        $this->assertCount(2, $actual->orderOffers);
        $first = $actual->orderOffers->first();
        $last = $actual->orderOffers->last();
        $this->assertEquals(Money::parse('78.99'), $first->amount);
        $this->assertEquals(Money::parse('100'), $last->amount);
    }
}
