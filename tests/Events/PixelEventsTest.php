<?php

namespace App\Tests\Events;

use App\Models\Domain;
use App\Models\DomainProduct;
use App\Models\Pixel;
use App\Models\Product;
use App\Models\User;
use App\Services\Google\GoogleApi;
use App\Services\Google\GoogleService;
use App\Tests\TestCase;
use App\Tests\Traits\MockGoogleTagManagerTrait;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;
use Laravel\Sanctum\Sanctum;
use Mockery;
use Mockery\MockInterface;

class PixelEventsTest extends TestCase
{
    use MockGoogleTagManagerTrait;

    public function testPixelApprovedSendCodeSuccess()
    {
        Config::set('services.pixel.gtm_enabled', true);
        $user = User::factory()->create(['name' => 'Foo']);
        Sanctum::actingAs($user);
        $product = Product::factory()->create(['code' => 'AA', 'gtm' => 'GTM-11111111']);
        $domain = Domain::factory()->create();

        $domainProduct = new DomainProduct();
        $domainProduct->domain_id = $domain->id;
        $domainProduct->product_id = $product->id;
        $domainProduct->save();
        $domain->load(['products']);

        $pixel = Pixel::factory()->create([
            'domain_id' => $domain->id,
            'code' => '<script>console.log(\'foo\')</script>',
            'enabled' => false,
            'approved_at' => Carbon::now(),
            'gtm_tag_ref' => null,
            'gtm_trigger_ref' => null
        ]);

        $mock = Mockery::mock(GoogleApi::class, function (MockInterface $mock) use ($pixel) {
            $mock->shouldReceive('getContainer')->once()->andReturn($this->getContainer());
            $mock->shouldReceive('getWorkspaceByName')->once()->andReturn($this->getWorkspace());
            $mock->shouldReceive('createTrigger')->once()->andReturn($this->getTrigger());
            $mock->shouldReceive('getTriggerCondition')->andReturn($this->getTriggerCondition());
            $mock->shouldReceive('createTag')->once()->andReturn($this->getTag());
        });
        app()->instance(GoogleApi::class, $mock);

        $mock = Mockery::mock(GoogleService::class, function (MockInterface $mock) {
            $mock->shouldReceive('publishWorkspace')->once();
        });
        app()->instance(GoogleService::class, $mock);

        $pixel->enabled = true;
        $pixel->save();

        $actual = Pixel::find($pixel->id);
        $this->assertEquals('trigger-foo', $actual->gtm_trigger_ref);
        $this->assertEquals('tag-foo', $actual->gtm_tag_ref);
    }

    public function testPixelPausedSuccess()
    {
        Config::set('services.pixel.gtm_enabled', true);
        $user = User::factory()->create(['name' => 'Foo']);
        Sanctum::actingAs($user);
        $product = Product::factory()->create(['code' => 'AA', 'gtm' => 'GTM-11111111']);
        $domain = Domain::factory()->create();

        $domainProduct = new DomainProduct();
        $domainProduct->domain_id = $domain->id;
        $domainProduct->product_id = $product->id;
        $domainProduct->save();
        $domain->load(['products']);

        $pixel = Pixel::factory()->create([
            'domain_id' => $domain->id,
            'code' => '<script>console.log(\'foo\')</script>',
            'enabled' => true,
            'approved_at' => Carbon::now(),
            'gtm_tag_ref' => '1',
            'gtm_trigger_ref' => '1'
        ]);

        $mock = Mockery::mock(GoogleApi::class, function (MockInterface $mock) {
            $mock->shouldReceive('getContainer')->once()->andReturn($this->getContainer());
            $mock->shouldReceive('getWorkspaceByName')->once()->andReturn($this->getWorkspace());
            $mock->shouldReceive('getTag')->once()->andReturn($this->getTag());
            $mock->shouldReceive('setPausedTag')->once();
        });
        app()->instance(GoogleApi::class, $mock);

        $mock = Mockery::mock(GoogleService::class, function (MockInterface $mock) {
            $mock->shouldReceive('publishWorkspace')->once();
        });
        app()->instance(GoogleService::class, $mock);

        $pixel->enabled = false;
        $pixel->save();
    }

    public function testPixelUnpausedSuccess()
    {
        Config::set('services.pixel.gtm_enabled', true);
        $user = User::factory()->create(['name' => 'Foo']);
        Sanctum::actingAs($user);
        $product = Product::factory()->create(['code' => 'AA', 'gtm' => 'GTM-11111111']);
        $domain = Domain::factory()->create();

        $domainProduct = new DomainProduct();
        $domainProduct->domain_id = $domain->id;
        $domainProduct->product_id = $product->id;
        $domainProduct->save();
        $domain->load(['products']);

        $pixel = Pixel::factory()->create([
            'domain_id' => $domain->id,
            'code' => '<script>console.log(\'foo\')</script>',
            'enabled' => false,
            'approved_at' => Carbon::now(),
            'gtm_tag_ref' => '1',
            'gtm_trigger_ref' => '1'
        ]);

        $mock = Mockery::mock(GoogleApi::class, function (MockInterface $mock) {
            $mock->shouldReceive('getContainer')->once()->andReturn($this->getContainer());
            $mock->shouldReceive('getWorkspaceByName')->once()->andReturn($this->getWorkspace());
            $mock->shouldReceive('getTag')->once()->andReturn($this->getTag());
            $mock->shouldReceive('setPausedTag')->once();
        });
        app()->instance(GoogleApi::class, $mock);

        $mock = Mockery::mock(GoogleService::class, function (MockInterface $mock) {
            $mock->shouldReceive('publishWorkspace')->once();
        });
        app()->instance(GoogleService::class, $mock);

        $pixel->enabled = true;
        $pixel->save();
    }

    public function testPixelDeletedSuccess()
    {
        Config::set('services.pixel.gtm_enabled', true);
        $user = User::factory()->create(['name' => 'Foo']);
        Sanctum::actingAs($user);
        $product = Product::factory()->create(['code' => 'AA', 'gtm' => 'GTM-11111111']);
        $domain = Domain::factory()->create();

        $domainProduct = new DomainProduct();
        $domainProduct->domain_id = $domain->id;
        $domainProduct->product_id = $product->id;
        $domainProduct->save();
        $domain->load(['products']);

        $pixel = Pixel::factory()->create([
            'domain_id' => $domain->id,
            'code' => '<script>console.log(\'foo\')</script>',
            'enabled' => false,
            'approved_at' => Carbon::now(),
            'gtm_tag_ref' => '1',
            'gtm_trigger_ref' => '1'
        ]);

        $mock = Mockery::mock(GoogleApi::class, function (MockInterface $mock) {
            $mock->shouldReceive('getContainer')->once()->andReturn($this->getContainer());
            $mock->shouldReceive('getWorkspaceByName')->once()->andReturn($this->getWorkspace());
            $mock->shouldReceive('deleteTag')->once()->andReturn($this->getTag());
            $mock->shouldReceive('deleteTrigger')->once();
        });
        app()->instance(GoogleApi::class, $mock);

        $mock = Mockery::mock(GoogleService::class, function (MockInterface $mock) {
            $mock->shouldReceive('publishWorkspace')->once();
        });
        app()->instance(GoogleService::class, $mock);
        $pixel->delete();
    }
}