import {ApiService} from '@/api'
import {Filter} from '@/types/request'

export default class AccountRequestService extends ApiService {
    protected readonly endpoint = ''

    public listAccounts(params: Filter): Promise<any> {
        return new Promise((resolve, reject) => {
            return this.getRequest(this.url('accounts'), params)
                .then((res) => {
                    resolve(res)
                })
                .catch((err) => reject(err.response.data))
        })
    }
}
