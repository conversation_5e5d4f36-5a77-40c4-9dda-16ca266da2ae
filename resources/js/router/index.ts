import { createRouter, createWebHistory } from 'vue-router';
import Register from '@/pages/Register.vue'
import VerifyEmail from '@/pages/VerifyEmail.vue'
import Login from '@/pages/Login.vue'
import AffiliateRequest from '@/pages/AffiliateRequest.vue'
import AffiliateRequestUpdate from '@/pages/AffiliateRequestUpdate.vue'
import DefaultGuest from '@/layouts/DefaultGuest.vue'
import auth from '@/middleware/auth'
import title from '@/middleware/title'
import guest from '@/middleware/guest'

const router = createRouter({
    history: createWebHistory(),
    routes: [
        {
            path: '/register-as-affiliate',
            name: 'register',
            component: Register,
            beforeEnter: [title, guest],
            meta: {
                title: 'Register',
                layout: DefaultGuest
            }
        },
        {
            path: '/verify-email',
            name: 'verify-email',
            component: VerifyEmail,
            beforeEnter: [title, guest],
            meta: {
                title: 'Verify email',
                layout: DefaultGuest
            }
        },
        {
            path: '/',
            name: 'login',
            component: Login,
            beforeEnter: [title, guest],
            meta: {
                title: 'Login',
                layout: DefaultGuest
            }
        },
        {
            path: '/affiliate-request',
            name: 'affiliate-request',
            component: AffiliateRequest,
            beforeEnter: [title, auth],
            meta: {
                title: 'Create affiliate request'
            }
        },
        {
            path: '/affiliate-request/:id',
            name: 'affiliate-request-update',
            component: AffiliateRequestUpdate,
            beforeEnter: [title, auth],
            meta: {
                title: 'Update affiliate request'
            }
        },
    ],
});

export default router;
