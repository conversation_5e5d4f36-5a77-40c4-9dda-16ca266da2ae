export async function onRequest(context) {
    try {
        const url = new URL(context.request.url);
        const request = context.env.API_HOST + '/api/pixels';
        /**
         *  available values for "pathCondition" param:
         * 'equal', 'contain', '!contain', '!start_with', 'start_with', 'end_with', '!end_with', 'regexp', '!regexp'
         */
        let params = {
            domain: url.host,
            path: url.pathname,
            affiliates: [],
            pathCondition: 'contain',
            source: '',
            product: null,
        }

        async function fetchPixelTrackingCode(params) {
            if (!params.affiliates.length) {
                return;
            }

            const token = context.env.API_TOKEN;
            const query = (new URLSearchParams(params)).toString();
            const key = JSON.stringify(params);
            let value = await context.env.TEST.get(key);

            if (value) {
                return;
            }

            const response = await fetch(`${request}?${query}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'content-type': 'application/json;charset=UTF-8',
                },
            });
            const result = await gatherResponse(response);
            const secondsFromNow = 60 * 60 * 24;
            await context.env.TEST.put(key, result, {expirationTtl: secondsFromNow});
        }

        async function gatherResponse(response) {
            const {headers} = response;
            const contentType = headers.get("content-type") || "";

            if (contentType.includes("application/json")) {
                return JSON.stringify(await response.json());
            }

            return response.text();
        }

        function getURLParameter(url, name) {
            const params = new URLSearchParams(url.search);

            if (params.has(name)) {
                return params.get(name);
            }

            return false
        }

        if (getURLParameter(url, 'aff_id')) {
            params.affiliates.push(getURLParameter(url, 'aff_id'));
            params.source = 'buygoods';
        }

        if (getURLParameter(url, 'hop')) {
            params.affiliates.push(getURLParameter(url, 'hop'));
            params.source = 'clickbank';
        }

        if (getURLParameter(url, 'aff')) {
            params.affiliates.push(getURLParameter(url, 'aff'));
            params.source = 'digistore24';
        }

        if (getURLParameter(url, 'product')) {
            params.product = getURLParameter(url, 'product');
        }

        await fetchPixelTrackingCode(params);

        return await context.next();
    } catch (err) {
        return new Response(`${err.message}\n${err.stack}`, {status: 500});
    }
}
