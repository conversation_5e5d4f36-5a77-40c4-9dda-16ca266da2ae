<template>
  <div>
    <Head title="Manage Request" />

    <div class="md:flex items-center mb-3">
      <div class="flex flex-auto truncate items-center">
        <h1 class="font-normal text-xl md:text-xl">Request from {{affiliateRequest.affiliate.name}}</h1>
      </div>
      <div v-if="!affiliateRequest.declined_at.length && !affiliateRequest.approved_at.length && !affiliateRequest.registration.id" class="ml-auto flex items-center">
        <div class="mt-1 md:mt-0 md:ml-2 md:mr-2">
          <DefaultButton
              @click="isShowDeclineModal=true"
              type="submit"
              ref="runButton"
              dusk="confirm-delete-button"
              variant="solid"
              state="danger"
              class="bg-red-500"
          >
            Decline
          </DefaultButton>
        </div>
      </div>
    </div>

    <div>
      <div class="md:flex items-center mb-3">
        <div class="flex flex-auto truncate items-center">
          <h1 class="font-normal text-md-start md:text-xl">Manage domain</h1>
        </div>
      </div>
      <div v-if="!affiliateRequest.declined_at && !affiliateRequest.approved_at && !affiliateRequest.registration.id">
        <Card
            v-if="showNoRedirectPanel && googleAuthLink"
            style="min-height: 300px"
            class="divide-y divide-gray-100 dark:divide-gray-700"
            :class="{'block-relative' : showNoRedirectPanel && googleAuthLink}"
        >
          <div class="text-block">
            <p class="font-normal text-xl md:text-xl">To mange "no redirect links" provide access to google api.
              Please <a :href="googleAuthLink" class="link-default">authorize</a> and try again
            </p>
          </div>
        </Card>
        <Card
          style="min-height: 300px"
          v-if="!(showNoRedirectPanel && googleAuthLink)"
          class="divide-y divide-gray-100 dark:divide-gray-700"
      >
        <RadioButton v-model="selectExistingDomain" :field="domainField"></RadioButton>
        <DomainProvisionPanel
            v-if="selectExistingDomain"
            :affiliate-request="affiliateRequest"
        ></DomainProvisionPanel>
        <SelectUniqueDomainPanel v-else :affiliate-request="affiliateRequest"></SelectUniqueDomainPanel>
      </Card>
      </div>
      <div v-if="affiliateRequest.declined_at">
        <Card
            style="min-height: 300px"
            class="block-relative divide-y divide-gray-100 dark:divide-gray-700"
        >
          <div class="text-block">
            <p class="font-normal text-xl md:text-xl"> Request already declined
            </p>
          </div>
        </Card>
      </div>
      <div v-if="affiliateRequest.approved_at">
        <Card
            style="min-height: 300px"
            class="block-relative divide-y divide-gray-100 dark:divide-gray-700"
        >
          <div class="text-block">
            <p class="font-normal text-xl md:text-xl"> Request already approved</p>
          </div>
        </Card>
      </div>
      <div v-if="!affiliateRequest.declined_at && !affiliateRequest.approved_at && affiliateRequest.registration.id">
        <Card
            style="min-height: 300px"
            class="block-relative divide-y divide-gray-100 dark:divide-gray-700"
        >
          <div class="text-block">
            <p class="font-normal text-xl md:text-xl">
              Request to register "{{affiliateRequest.registration.domain}}"
            domain in process. Please wait while domain will be registered and provisioned.
            </p>
          </div>
        </Card>
      </div>
    </div>
    <decline-reason-modal
        :show="isShowDeclineModal"
        @close="isShowDeclineModal=false"
        @confirm="handleDeclineReason"
    ></decline-reason-modal>
  </div>
</template>

<script setup>
import {ref, computed} from "vue";
import DeclineReasonModal from "./../components/DeclineReasonModal.vue"
import AffiliateRequest from "../models/AffiliateRequest";
import DomainProvisionPanel from "../components/DomainProvisionPanel.vue"
import RadioButton from "../components/RadioButton";
import SelectUniqueDomainPanel from "../components/SelectUniqueDomainPanel.vue"

const domainOptions = ref([{label: 'Title', value:'1'}]);
const props = defineProps(['resource'])
const affiliateRequest = ref(new AffiliateRequest(props.resource.data))

const domainField = {
  flex: true,
  attribute: 'domain_type',
  visible: true,
  withLabel: true,
  required: false,
  options: [{label: 'Select existing domain', value: true}, {label: 'Create new domain', value: false}]
}

const isShowDeclineModal = ref(false)
const selectExistingDomain = ref(!affiliateRequest.value.domain_name.length)
const googleAuthLink = ref('')

const handleDeclineReason = async (e) => {
  try {
    await Nova.request().post(
        `/nova-vendor/manage-affiliate-request/${affiliateRequest.value.id}/decline`,
        {'decline_reason': e.value.value}
    )
    Nova.success('Request was declined')
  } catch (e) {
    console.log(e)
    Nova.error('Error')
  } finally {
    isShowDeclineModal.value = false
  }
}

const fetchAuthLink = async (e) => {
  try {
    const { data } = await Nova.request().get(`/nova-vendor/google/link`)
    googleAuthLink.value = data.link
  } catch (e) {
    console.log(e)
  }
}

fetchAuthLink()
const showDomainPanel = computed(() => {
  return affiliateRequest.value.type.includes('unique_domain') && !affiliateRequest.value.domain_id && !affiliateRequest.value.domain_name
})

const showNoRedirectPanel = computed(() => {
  return affiliateRequest.value.type.includes('no_redirects')
})
</script>

<style>
.text-block {
  vertical-align: middle;
  position: absolute;
  top: 40%;
  left: 10%;
}
.text-block p{
  display: inline-block;
  vertical-align: middle;
}
.block-relative {
  position: relative;
}
</style>
