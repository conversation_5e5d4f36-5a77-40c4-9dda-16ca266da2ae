<template>
  <DefaultField
      :field="{
            visible: true,
            withLabel: true,
            required: false
          }"
      fieldLabel="Domain"
      ref="domain"
      :showErrors="false"
      fieldName="domain"
  >
    <template #default>
      <FormLabel
          label-for="domain"
          class="mb-2 space-x-1"
      >
          <span>
            Affiliate
          </span>
      </FormLabel>
    </template>

    <template #field>
        <SelectControl
            size="xs"
            :options="options"
            @change="handle"
            dusk="action-select"
            class='w-full'
            :aria-label="__('Select Affiliate')"
        >
          <option value="" disabled selected>{{ __('Affiliate') }}</option>
        </SelectControl>
        <HelpText class="help-text-error" v-if="props.hasError && !props.modelValue">
          Affiliate field is required
        </HelpText>
    </template>
  </DefaultField>
</template>

<script setup>
import { ref } from "vue";
const emit = defineEmits(['update:modelValue'])
const props = defineProps(['modelValue', 'hasError'])
const value = ref(props.modelValue)
const options = ref([])

Nova.request().get('/nova-vendor/affiliates').then((res) => {
  options.value = res.data.data.map((item) => {
    return {label: `${item.name} (${item.source_ref})`, value: item.id}
  })
})

const handle = (eventValue) => {
  emit('update:modelValue', eventValue)
}
</script>
